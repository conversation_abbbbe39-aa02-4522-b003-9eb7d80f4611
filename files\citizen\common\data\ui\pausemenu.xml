<?xml version="1.0" encoding="utf-8"?>


<CMenuArray v="5.0">
<!-- kng is a meow lol -->
	<GeneralData v="2.4">

		<MovieSettings>
			<Item key="MinimapBG">
				<vPos 	x="0.162" y="0.222"/>
				<vSize 	x="0.6785" y="0.5975"/>
			</Item>
			
			<Item key="GalleryBG">
				<vPos 	  x="0.615"  y="0.222"/>
				<vSize 	  x="0.225"     y="0.5975"/>
			</Item>
			
			<Item key="CoronaMapBG">
				<vPos 	  x="0.615"  y="0.222"/>
				<vSize 	  x="0.225"     y="0.5975"/>
			</Item>
			
			<Item key="MIDDLE_AVATAR_BG" platform="ps3">
				<vPos x="0.38868" y="0.222"/>
				<vSize x="0.2245" y="0.597"/>
			</Item>			
			
			<Item key="RIGHT_AVATAR_BG" platform="ps3">
				<vPos x="0.6145" y="0.2225"/>
				<vSize x="0.1120" y="0.597"/>
			</Item>

			<Item key="MIDDLE_AVATAR_BG" platform="xenon|PC">
				<vPos x="0.3885" y="0.222"/>
				<vSize x="0.225" y="0.597"/>
			</Item>	
			
			<Item key="RIGHT_AVATAR_BG" platform="xenon|PC">
				<vPos x="0.6149" y="0.222"/>
				<vSize x="0.1119" y="0.5975"/>
			</Item>
			
			<Item key="PAUSE_MENU_HEADER">
				<vPos x="0.162" y="0.078"/>
				<vSize x="0.6782" y="0.145"/>
			</Item>
			<Item key="PAUSE_MENU_SHARED_COMPONENTS">
				<bRequiresMovieView value="false"/>
			</Item>

			<Item key="PAUSE_MENU_SHARED_COMPONENTS_02">
				<bRequiresMovieView value="false"/>
				<bDependsOnSharedComponents value="true"/>
			</Item>
			
			<Item key="PAUSE_MENU_SHARED_COMPONENTS_03">
				<bRequiresMovieView value="false"/>
				<bDependsOnSharedComponents value="true"/>
			</Item>

			<Item key="PAUSE_MENU_SHARED_COMPONENTS_MP_01">
				<bRequiresMovieView value="false"/>
				<bDependsOnSharedComponents value="true"/>
			</Item>
			<Item key="PAUSE_MENU_SP_CONTENT">
				<vPos x="0.162" y="0.222"/>
				<vSize x="1.0" y="1.0"/>
				<bDependsOnSharedComponents value="true"/>
			</Item>
			
			<Item key="PAUSE_MENU_SP_CONTENT_ALT">
				<vPos x="0.05" y="0.078"/>
				<vSize x="1.0" y="1.0"/>
				<bDependsOnSharedComponents value="true"/>
        <HAlign>L</HAlign>
			</Item>

			<Item key="PAUSE_MENU_PAGES_MAP">
				<RequiredMovies>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_03</movie>
				</RequiredMovies>
			</Item>

			<Item key="PAUSE_MENU_PAGES_SETTINGS">
				<RequiredMovies>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_02</movie>
				</RequiredMovies>
			</Item>
			
			<Item key="PAUSE_MENU_PAGES_KEYMAP" platform="pc_only">
				<RequiredMovies>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_02</movie>
				</RequiredMovies>
			</Item>

			<Item key="PAUSE_MENU_PAGES_SAVE">
				<RequiredMovies>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_02</movie>
				</RequiredMovies>
			</Item>
			<Item key="PAUSE_MENU_PAGES_MISSION_CREATOR">
				<RequiredMovies>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_02</movie>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_03</movie>
				</RequiredMovies>
			</Item>
			<Item key="PAUSE_MENU_PAGES_GAME">
				<RequiredMovies>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_02</movie>
				</RequiredMovies>
			</Item>
			
			<Item key="PAUSE_MENU_PAGES_CORONA">
				<RequiredMovies>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_02</movie>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_03</movie>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_MP_01</movie>
				</RequiredMovies>
			</Item>
			<Item key="PAUSE_MENU_PAGES_CORONA_PLAYERS">
				<RequiredMovies>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_MP_01</movie>
				</RequiredMovies>
			</Item>
			<Item key="PAUSE_MENU_PAGES_CORONA_LOBBY">
				<RequiredMovies>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_02</movie>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_MP_01</movie>
				</RequiredMovies>
			</Item>
			<Item key="PAUSE_MENU_PAGES_CORONA_RACE">
				<RequiredMovies>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_02</movie>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_MP_01</movie>
				</RequiredMovies>
			</Item>
			<Item key="PAUSE_MENU_PAGES_CREWS">
				<RequiredMovies>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_MP_01</movie>
				</RequiredMovies>
			</Item>
			<Item key="PAUSE_MENU_PAGES_FRIENDS_MP">
				<RequiredMovies>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_MP_01</movie>
				</RequiredMovies>
			</Item>		
			<Item key="PAUSE_MENU_PAGES_CHAR_MOM_DAD">
				<RequiredMovies>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_02</movie>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_MP_01</movie>
				</RequiredMovies>
			</Item>	
			<Item key="PAUSE_MENU_PAGES_CHAR_SELECT">
				<RequiredMovies>
					<movie>PAUSE_MENU_SHARED_COMPONENTS_MP_01</movie>
				</RequiredMovies>
			</Item>				
		</MovieSettings>
	
	</GeneralData>

	<DisplayValues>

		<Item>
			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>
			<MenuDisplayOptions>
				<Item> <cTextId>MO_OFF</cTextId> </Item>
				<Item> <cTextId>MO_ON</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<Item>
			<MenuOption>MENU_OPTION_DISPLAY_INVERT_LOOK</MenuOption>
			<MenuDisplayOptions>
				<Item> <cTextId>MO_OFF</cTextId> </Item>
				<Item> <cTextId>MO_ON</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<Item>
			<MenuOption>MENU_OPTION_DISPLAY_TARGET_CONFIG</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_TAR1</cTextId> </Item>
				<Item> <cTextId>MO_TAR2</cTextId> </Item>
				<Item> <cTextId>MO_TAR3</cTextId> </Item>
				<Item> <cTextId>MO_TAR4</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
    
		<Item>
			<MenuOption>MENU_OPTION_CONTROLS_HOLD_SPRINT</MenuOption>
			<MenuDisplayOptions>
				<Item> <cTextId>MO_TAP_SPRINT</cTextId> </Item>
				<Item> <cTextId>MO_HOLD_SPRINT</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

    <Item>
			<MenuOption>MENU_OPTION_DISPLAY_MEASUREMENT</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MS_IMPERIAL</cTextId> </Item>
				<Item> <cTextId>MS_METRIC</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

    <Item>
			<MenuOption>MENU_OPTION_CONTROLS_CONTEXT</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_CTRL_OFTPS</cTextId> </Item>
				<Item> <cTextId>MO_CTRL_IVTPS</cTextId> </Item>
				<Item> <cTextId>MO_CTRL_IATPS</cTextId> </Item>
				<Item> <cTextId>MO_CTRL_OFFPS</cTextId> </Item>
				<Item> <cTextId>MO_CTRL_IVFPS</cTextId> </Item>
				<Item> <cTextId>MO_CTRL_IAFPS</cTextId> </Item>
				<Item> <cTextId>MO_LCREATOR</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<Item>
			<MenuOption>MENU_OPTION_DISPLAY_CONTROL_CONFIG</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_CCONF_1</cTextId> </Item>
				<Item> <cTextId>MO_CCONF_2</cTextId> </Item>
				<Item> <cTextId>MO_CCONF_3</cTextId> </Item>
				<Item> <cTextId>MO_CCONF_4</cTextId> </Item>
				<Item> <cTextId>MO_CCONF_5</cTextId> </Item>
				<Item> <cTextId>MO_CCONF_6</cTextId> </Item>
				<Item> <cTextId>MO_CCONF_7</cTextId> </Item>
				<Item> <cTextId>MO_CCONF_8</cTextId> </Item>
				<Item> <cTextId>MO_CCONF_9</cTextId> </Item>
				<Item> <cTextId>MO_CCONF_10</cTextId> </Item>
				<Item> <cTextId>MO_CCONF_11</cTextId> </Item>
				<Item> <cTextId>MO_CCONF_12</cTextId> </Item>
		  </MenuDisplayOptions>
		</Item>

		<Item>
			<MenuOption>MENU_OPTION_DISPLAY_CONTROL_DRIVEBY</MenuOption>
			<MenuDisplayOptions>
				<Item> <cTextId>MO_ADB_OFF</cTextId> </Item>
				<Item> <cTextId>MO_ADB_ON</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>	
		
		<Item platform="pc_only">
			<MenuOption>MENU_OPTION_FPS_DEFAULT_AIM_TYPE</MenuOption>
			<MenuDisplayOptions>
				<Item> <cTextId>MO_FPS_DEFAULT_AIM_NORMAL</cTextId> </Item>
				<Item> <cTextId>MO_FPS_DEFAULT_AIM_IRON</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>	
	
		<Item>
			<MenuOption>MENU_OPTION_MOUSE_TYPE</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_MT_RAW</cTextId> </Item>
				<Item> <cTextId>MO_MT_DI</cTextId> </Item>
				<Item> <cTextId>MO_MT_WIN</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<Item>
			<MenuOption>MENU_OPTION_CONTROLLER_SENSITIVITY</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_CS_LOW</cTextId> </Item>
				<Item> <cTextId>MO_CS_MED</cTextId> </Item>
				<Item> <cTextId>MO_CS_HIGH</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		<Item>
			<MenuOption>MENU_OPTION_GAME_FLOW</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_LOAD_SP</cTextId> </Item>
				<Item> <cTextId>MO_LOAD_MP</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		<Item>
			<MenuOption>MENU_OPTION_GFX_QUALITY</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_GFX_NORM</cTextId> </Item>
				<Item> <cTextId>MO_CS_HIGH</cTextId> </Item>
				<Item> <cTextId>MO_GFX_VHIGH</cTextId> </Item>
				<Item> <cTextId>MO_GFX_ULTRA</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		<Item>
			<MenuOption>MENU_OPTION_GFX_SHADERQ</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_GFX_NORM</cTextId> </Item>
				<Item> <cTextId>MO_CS_HIGH</cTextId> </Item>
        <Item> <cTextId>MO_GFX_VHIGH</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		<Item>
			<MenuOption>MENU_OPTION_MEMORY_GRAPH</MenuOption>
			
			<!-- Filled by code -->
			<MenuDisplayOptions>
				<Item> <cTextId>MO_OFF</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		<Item>
			<MenuOption>MENU_OPTION_VOICE_FEEDBACK</MenuOption>
			
			<!-- Filled by code -->
			<MenuDisplayOptions>
				<Item> <cTextId>MO_OFF</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		<Item>
			<MenuOption>MENU_OPTION_GFX_SHADOWS</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_OFF</cTextId> </Item>
				<Item> <cTextId>MO_GFX_NORM</cTextId> </Item>
				<Item> <cTextId>MO_CS_HIGH</cTextId> </Item>
				<Item> <cTextId>MO_GFX_VHIGH</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		<Item>
			<MenuOption>MENU_OPTION_GFX_REFLECTION</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_GFX_NORM</cTextId> </Item>
				<Item> <cTextId>MO_CS_HIGH</cTextId> </Item>
				<Item> <cTextId>MO_GFX_VHIGH</cTextId> </Item>
				<Item> <cTextId>MO_GFX_ULTRA</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		<Item>
			<MenuOption>MENU_OPTION_GFX_NIGHTLIGHTS</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_GFX_NORM</cTextId> </Item>
				<Item> <cTextId>MO_CS_HIGH</cTextId> </Item>
				<Item> <cTextId>MO_GFX_VHIGH</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

    <Item>
			<MenuOption>MENU_OPTION_ROLL_YAW_PITCH</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_ROLLPITCH</cTextId> </Item>
				<Item> <cTextId>MO_YAWPITCH</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

    <Item>
			<MenuOption>MENU_OPTION_CAM_VEH_OFF</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>PM_PANE_CAM</cTextId> </Item>
				<Item> <cTextId>MO_VEHICLE</cTextId> </Item>
				<Item> <cTextId>MO_OFF</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		<Item>
			<MenuOption>MENU_OPTION_GFX_SOFTNESS</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>GFX_SHARP</cTextId> </Item>
				<Item> <cTextId>GFX_SOFT</cTextId> </Item>
				<Item> <cTextId>GFX_SOFTER</cTextId> </Item>
				<Item> <cTextId>GFX_SOFTEST</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<Item>
			<MenuOption>MENU_OPTION_VID_VSYNC</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_OFF</cTextId> </Item>
				<Item> <cTextId>MO_ON</cTextId> </Item>
				<Item> <cTextId>MO_VID_VSHALF</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<Item>
			<MenuOption>MENU_OPTION_VID_ASPECT</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>VID_ASP_AUTO</cTextId> </Item>
				<Item> <cTextId>VID_ASP_32</cTextId> </Item>
				<Item> <cTextId>VID_ASP_43</cTextId> </Item>
				<Item> <cTextId>VID_ASP_53</cTextId> </Item>
				<Item> <cTextId>VID_ASP_54</cTextId> </Item>
				<Item> <cTextId>VID_ASP_169</cTextId> </Item>
				<Item> <cTextId>VID_ASP_1610</cTextId> </Item>
				<Item> <cTextId>VID_ASP_179</cTextId> </Item>
				<Item> <cTextId>VID_ASP_219</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		<Item>
			<MenuOption>MENU_OPTION_GFX_MSAA</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_OFF</cTextId> </Item>
				<Item> <cTextId>MO_GFX_X2</cTextId> </Item>
				<Item> <cTextId>MO_GFX_X4</cTextId> </Item>
				<Item> <cTextId>MO_GFX_X8</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		<Item>
			<MenuOption>MENU_OPTION_GFX_SCALING</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_GFX_1o1</cTextId> </Item>
				<Item> <cTextId>MO_GFX_1o2</cTextId> </Item>
				<Item> <cTextId>MO_GFX_2o3</cTextId> </Item>
				<Item> <cTextId>MO_GFX_3o4</cTextId> </Item>
				<Item> <cTextId>MO_GFX_5o6</cTextId> </Item>
				<Item> <cTextId>MO_GFX_5o4</cTextId> </Item>
				<Item> <cTextId>MO_GFX_3o2</cTextId> </Item>
				<Item> <cTextId>MO_GFX_7o4</cTextId> </Item>
				<Item> <cTextId>MO_GFX_2o1</cTextId> </Item>
				<Item> <cTextId>MO_GFX_5o2</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		<Item>
			<MenuOption>MENU_OPTION_GFX_ANISOT</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_OFF</cTextId> </Item>
				<Item> <cTextId>MO_GFX_X2</cTextId> </Item>
				<Item> <cTextId>MO_GFX_X4</cTextId> </Item>
				<Item> <cTextId>MO_GFX_X8</cTextId> </Item>
				<Item> <cTextId>MO_GFX_X16</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		<Item>
			<MenuOption>MENU_OPTION_GFX_AMBIENTOCC</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_OFF</cTextId> </Item>
				<Item> <cTextId>MO_GFX_NORM</cTextId> </Item>
				<Item> <cTextId>MO_CS_HIGH</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		<Item>
			<MenuOption>MENU_OPTION_GFX_DXVERSION</MenuOption>
			
			<MenuDisplayOptions>
				<!--<Item> <cTextId>MO_GFX_DX9</cTextId> </Item>-->
				<Item> <cTextId>MO_GFX_DX10</cTextId> </Item>
				<Item> <cTextId>MO_GFX_DX101</cTextId> </Item>
				<Item> <cTextId>MO_GFX_DX11</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<Item>
			<MenuOption>MENU_OPTION_REPLAY_MODE</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_REPLAY_MANUAL</cTextId> </Item>
				<!--Item> <cTextId>MO_REPLAY_MISSION</cTextId> </Item-->
				<Item> <cTextId>MO_REPLAY_CONT</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<Item>
			<MenuOption>MENU_OPTION_REPLAY_MEM_LIMIT</MenuOption>	
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_REPLAY_MEM_250MB</cTextId> </Item>
				<Item> <cTextId>MO_REPLAY_MEM_500MB</cTextId> </Item>
				<Item> <cTextId>MO_REPLAY_MEM_750MB</cTextId> </Item>
				<Item> <cTextId>MO_REPLAY_MEM_1GB</cTextId> </Item>
				<Item> <cTextId>MO_REPLAY_MEM_1_5GB</cTextId> </Item>
				<Item> <cTextId>MO_REPLAY_MEM_2GB</cTextId> </Item>
				<Item> <cTextId>MO_REPLAY_MEM_5GB</cTextId> </Item>
				<Item> <cTextId>MO_REPLAY_MEM_10GB</cTextId> </Item>
				<Item> <cTextId>MO_REPLAY_MEM_15GB</cTextId> </Item>
				<Item> <cTextId>MO_REPLAY_MEM_25GB</cTextId> </Item>
				<Item> <cTextId>MO_REPLAY_MEM_50GB</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<Item>
			<MenuOption>MENU_OPTION_REPLAY_AUTO_RESUME_RECORDING</MenuOption>
	
			<MenuDisplayOptions>
				<Item> <cTextId>MO_OFF</cTextId> </Item>
				<Item> <cTextId>MO_ON</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

    <Item>
      <MenuOption>MENU_OPTION_REPLAY_AUTO_SAVE_RECORDING</MenuOption>

      <MenuDisplayOptions>
        <Item>
          <cTextId>MO_OFF</cTextId>
        </Item>
        <Item>
          <cTextId>MO_ON</cTextId>
        </Item>
      </MenuDisplayOptions>
    </Item>
    
		<Item>
			<MenuOption>MENU_OPTION_VIDEO_UPLOAD_PRIVACY</MenuOption>
	
			<MenuDisplayOptions>
				<Item> <cTextId>MO_PRIVACY_PUBLIC</cTextId> </Item>
				<Item> <cTextId>MO_PRIVACY_UNLISTED</cTextId> </Item>
				<Item> <cTextId>MO_PRIVACY_PRIVATE</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		
		<Item>
			<MenuOption>MENU_OPTION_SCREEN_TYPE</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>VID_FULLSCREEN</cTextId> </Item>
				<Item> <cTextId>VID_SCR_WIN</cTextId> </Item>
				<Item> <cTextId>VID_SCR_BORDERLESS</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<Item>
			<MenuOption>MENU_OPTION_DISPLAY_GPS_SPEECH</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_OFF</cTextId> </Item>
				<Item> <cTextId>MO_GPS1</cTextId> </Item>
				<Item> <cTextId>MO_ON</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		<Item>
			<MenuOption>MENU_OPTION_DISPLAY_VOICE_TYPE</MenuOption>
			<MenuDisplayOptions>
				<Item> <cTextId>MO_VC_TALK</cTextId> </Item>
				<Item> <cTextId>MO_VC_PUSH</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<!-- this is filled out by code 
		<Item>
			<MenuOption>MENU_OPTION_DISPLAY_SPEAKER_OUTPUT</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_TVOUTP</cTextId> </Item>
				<Item> <cTextId>MO_SPEAKERS</cTextId> </Item>
				<Item> <cTextId>MO_SHEAD</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		-->
		<Item>
			<MenuOption>MENU_OPTION_DISPLAY_SWHEADSET</MenuOption>
			<MenuDisplayOptions>
				<Item> <cTextId>MO_SWHEADSET</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<Item>
			<MenuOption>MENU_OPTION_CAMERA_HEIGHT</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_LOW</cTextId> </Item>
				<Item> <cTextId>MO_HIGH</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<Item>
			<MenuOption>MENU_OPTION_CAMERA_ZOOM</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_ZOOM_NEAR</cTextId> </Item>
				<Item> <cTextId>MO_ZOOM_MED</cTextId> </Item>
				<Item> <cTextId>MO_ZOOM_FAR</cTextId> </Item>
				<Item> <cTextId>MO_ZOOM_FIRST</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<Item>
			<MenuOption>MENU_OPTION_CAMERA_ZOOM_NO_FP</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_ZOOM_NEAR</cTextId> </Item>
				<Item> <cTextId>MO_ZOOM_MED</cTextId> </Item>
				<Item> <cTextId>MO_ZOOM_FAR</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<!-- this is filled out by code 
		<Item>
			<MenuOption>MENU_OPTION_DISPLAY_RADIO_STATIONS</MenuOption>
		</Item>
		-->

		<Item>
			<MenuOption>MENU_OPTION_DISPLAY_VOICE_OUTPUT</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_VFRV</cTextId> </Item>
				<Item> <cTextId>MO_VRRV</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

    <Item>
      <MenuOption>MENU_OPTION_DISPLAY_UR_PLAYMODE</MenuOption>

      <MenuDisplayOptions>
        <Item>
          <cTextId>MO_URRADIO</cTextId>
        </Item>
        <Item>
          <cTextId>MO_URSEQ</cTextId>
        </Item>
        <Item>
          <cTextId>MO_URRAND</cTextId>
        </Item>
      </MenuDisplayOptions>
    </Item>

		<Item>
			<MenuOption>MENU_OPTION_DISPLAY_MINIMAP_MODE</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_OFF</cTextId> </Item>
				<Item> <cTextId>MO_ON</cTextId> </Item>
				<Item> <cTextId>MO_BLI</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<Item>
			<MenuOption>MENU_OPTION_DISPLAY_RETICLE_MODE</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>MO_RETICULE_OF</cTextId> </Item>
				<Item> <cTextId>MO_RETICULE_ON</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

		<Item>
			<MenuOption>MENU_OPTION_DISPLAY_LANGUAGE</MenuOption>
			
			<MenuDisplayOptions>
				<Item> <cTextId>LANG_E</cTextId> </Item>
				<Item> <cTextId>LANG_F</cTextId> </Item>
				<Item> <cTextId>LANG_G</cTextId> </Item>
				<Item> <cTextId>LANG_I</cTextId> </Item>
				<Item> <cTextId>LANG_S</cTextId> </Item>
				<Item> <cTextId>LANG_PT</cTextId> </Item>
				<Item> <cTextId>LANG_PL</cTextId> </Item>
				<Item> <cTextId>LANG_R</cTextId> </Item>
				<Item> <cTextId>LANG_K</cTextId> </Item>
				<Item> <cTextId>LANG_CH</cTextId> </Item>
				<Item> <cTextId>LANG_J</cTextId> </Item>
				<Item> <cTextId>LANG_MEX</cTextId> </Item>
				<Item> <cTextId>LANG_CHS</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>
		
		<Item>
			<MenuOption>MENU_OPTION_FEED_DELAY</MenuOption>
			<MenuDisplayOptions>
				<Item> <cTextId>FEED_NO_DELAY</cTextId> </Item>
				<Item> <cTextId>FEED_DELAY_1MIN</cTextId> </Item>
				<Item> <cTextId>FEED_DELAY_2MIN</cTextId> </Item>
				<Item> <cTextId>FEED_DELAY_3MIN</cTextId> </Item>
				<Item> <cTextId>FEED_DELAY_4MIN</cTextId> </Item>
				<Item> <cTextId>FEED_DELAY_5MIN</cTextId> </Item>
				<Item> <cTextId>FEED_DELAY_10MIN</cTextId> </Item>
				<Item> <cTextId>FEED_DELAY_15MIN</cTextId> </Item>
				<Item> <cTextId>FEED_DELAY_30MIN</cTextId> </Item>
				<Item> <cTextId>FEED_DELAY_1HOUR</cTextId> </Item>
			</MenuDisplayOptions>
		</Item>

	</DisplayValues>

	<DefaultButtonList AreTheDefaults="true">
		<ButtonPrompts>
			<CMenuButton> <hButtonHash>HUD_INPUT1C</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_BUMPERS</ButtonInputGroup> 	<Contexts>*NONE*,TABS_ARE_COLUMNS,NAVIGATING_CONTENT,MENU_OFF</Contexts>				</CMenuButton>
			<CMenuButton> <hButtonHash>IB_HIDEMENU</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> 						<Contexts>*ALL*,CAN_TOGGLE_MENU,		 *NONE*,MENU_OFF, NAVIGATING_CONTENT</Contexts> </CMenuButton>
			<!--<CMenuButton> <hButtonHash>IB_SOCIALCLUB_UI</hButtonHash>	<ButtonInput>INPUT_FRONTEND_SELECT</ButtonInput> 					<Contexts>*ALL*,x64,		 *NONE*,MENU_OFF, NAVIGATING_CONTENT</Contexts> </CMenuButton>-->
			<CMenuButton> <hButtonHash>IB_SHOWMENU</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> 						<Contexts>*ALL*,CAN_TOGGLE_MENU,MENU_OFF,*NONE*,		  NAVIGATING_CONTENT</Contexts> </CMenuButton>
			<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> 					<Contexts>*NONE*,MENU_OFF,HIDE_ACCEPTBUTTON,HasContextMenu</Contexts> 					</CMenuButton>
      <CMenuButton> <hButtonHash>IB_MORDEETS</hButtonHash>			<ButtonInput>INPUT_FRONTEND_X</ButtonInput> 					<Contexts>*ALL*,ON_VIDEO_TAB, SHOW_VIDEO_BUTTON</Contexts> 					</CMenuButton>
			<CMenuButton> <hButtonHash>IB_OPTIONS</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> 					<Contexts>*NONE*,MENU_OFF,HIDE_ACCEPTBUTTON,*ALL*,HasContextMenu</Contexts> 			</CMenuButton>
			<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput>  																											</CMenuButton>
			<CMenuButton> <hButtonHash>HUD_INPUT10</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> 						<Contexts>*ALL*,DISPLAY_CAN_JOIN</Contexts> 											</CMenuButton>
			<CMenuButton> <hButtonHash>PM_DYNAMICSTRING</hButtonHash>	<RawButtonIcon>NOTHING</RawButtonIcon>								<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_DISPLAY_TIMER</Contexts> 				</CMenuButton>
		</ButtonPrompts>
	</DefaultButtonList>
	
	<MenuVersions>
	
		<!-- MenuVersions' VersionFlags 
		
		====== INTERESTING FLAGS =======
		
		kNoBackground			Has no background
		kNotDimmable			Starts undimmed
		kFadesIn				Fades in over time
		kNoPlayerInfo			No "Thing in the corner"
		kTabsAreColumns			Screen is "one screen" without tabs that change the full thing
		
		=== Headers ===
		kNoHeaderText			No header text (or will be set later)
		kAllHighlighted			All headers are highlighted
		kHideHeaders			Headers are not shown at all
		kUseAlternateContentPos Place the content at the location specified in PAUSE_MENU_SP_CONTENT_ALT
		
		=== Controls ===
		kNoTabChangeWhileLocked	Do not allow users to change tabs while another is queued to load
		kNoTabChange			Do not allow users to change tabs at all
		kNoSwitchDelay			Skip loading delay on tab switch	
		kAllowBackingOut		Users can press B at the top menu to escape the menu
		kAllowStartButton		Users can press Start to escape the menu
		
		= Boring =
		
		kGeneratesMenuData		Menu will populate itself from XML after creation
		kMenuSectionJump		Menu will kill content and set menu state
		kRenderMenusInitially	Does this menu need work done later (probably, use sparingly)
		kNoPeds               	Doesn't initialize any ped variables.  Use this for menus that don't contain any peds
		kAllowBackingOutInMenu 	This will allow you to close the pause menu immediately after backing out of menu (foregoing "Browse" tab state)
		
		-->

		<!-- Legacy code-only versions -->
		<Item>
			<MenuVersionId>FE_MENU_VERSION_SP_PAUSE</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER</InitialScreen>
			<MenuHeader>PM_PAUSE_HDR</MenuHeader>
			
			<VersionFlags>kFadesIn kGeneratesMenuData kMenuSectionJump kAllowBackingOut kAllowStartButton kCanHide</VersionFlags>
			<InitialContexts>InSP</InitialContexts>
			<MenuItemColour>HUD_COLOUR_PAUSE_SINGLEPLAYER</MenuItemColour>
		</Item>
		<Item>
			<MenuVersionId>FE_MENU_VERSION_MP_PAUSE</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_MULTIPLAYER</InitialScreen>
			<MenuHeader>FE_THDR_GTAO</MenuHeader>
			
			<VersionFlags>kFadesIn kGeneratesMenuData kMenuSectionJump kAllowBackingOut kAllowStartButton kNoBackground kCanHide</VersionFlags>
			<InitialContexts>InMP</InitialContexts>
		</Item>
		<Item>
			<MenuVersionId>FE_MENU_VERSION_CREATOR_PAUSE</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER</InitialScreen>
			<MenuHeader>PM_MISS_CREATOR</MenuHeader>
			
			<VersionFlags>kFadesIn kGeneratesMenuData kMenuSectionJump kAllowBackingOut kAllowStartButton kNoBackground kCanHide</VersionFlags>
			<InitialContexts>InSP</InitialContexts>
			<MenuItemColour>HUD_COLOUR_PAUSE_SINGLEPLAYER</MenuItemColour>
		</Item>
		
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_CUTSCENE_PAUSE</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_CUTSCENE_EMPTY</InitialScreen>
			
			<VersionFlags>kFadesIn kRenderMenusInitially kNoPlayerInfo kNoSwitchDelay kNoBackground kAllowBackingOut kForceButtonRender kForceProcessInput kNoTimeWarp</VersionFlags>
		</Item>
		
		<!-- Legacy script-only versions -->

		<Item>
			<MenuVersionId>FE_MENU_VERSION_SAVEGAME</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_SAVE_GAME</InitialScreen>
			<MenuHeader>PM_SG_PAUSE</MenuHeader>
			
			<VersionFlags>kFadesIn kGeneratesMenuData kMenuSectionJump kAllowBackingOut kNoBackground kNoTimeWarp kNoSwitchDelay</VersionFlags>
		</Item>
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_PRE_LOBBY</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_PRE_LOBBY</InitialScreen>
			<MenuHeader>PM_MP_PRELOBBY</MenuHeader>
			
			<VersionFlags>kNoBackground</VersionFlags>
			<InitialContexts>InLobby, InPreLobby</InitialContexts>
		</Item>
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_LOBBY</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_LOBBY</InitialScreen>
			<MenuHeader>PM_MP_LOBBY</MenuHeader>
			
			<VersionFlags>kNoBackground</VersionFlags>
			<InitialContexts>InLobby</InitialContexts>
		</Item>
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_MP_CHARACTER_SELECT</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_MP_CHARACTER_SELECT</InitialScreen>
			<MenuHeader>PM_MP_CHARSEL</MenuHeader>
			
			<VersionFlags>kNoHeaderText kNoSwitchDelay kTabsAreColumns kNoTabChange kNoPlayerInfo kNoBackground kHideHeaders kUseAlternateContentPos</VersionFlags>
		</Item>
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_MP_CHARACTER_CREATION</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_MP_CHARACTER_CREATION</InitialScreen>
			
			<VersionFlags>kNoSwitchDelay kTabsAreColumns kNoTabChangeWhileLocked kNoTabChange kNotDimmable kNoPlayerInfo kNoBackground kHideHeaders kUseAlternateContentPos</VersionFlags>
		</Item>
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_EMPTY</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_EMPTY</InitialScreen>
			
			<VersionFlags>kRenderMenusInitially kNoSwitchDelay kForceButtonRender kNoBackground</VersionFlags>
		</Item>
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_EMPTY_NO_BACKGROUND</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_EMPTY</InitialScreen>
			
			<VersionFlags>kRenderMenusInitially kNoPlayerInfo kNoSwitchDelay kNoBackground kForceButtonRender</VersionFlags>
		</Item>
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_TEXT_SELECTION</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_TEXT_SELECTION</InitialScreen>
			
			<VersionFlags>kHideHeaders kNoPlayerInfo kNoBackground</VersionFlags>
		</Item>
		
		<!-- CORONAS -->
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_CORONA</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_CORONA</InitialScreen>
			
			<VersionFlags>kAllHighlighted kNotDimmable kTabsAreColumns kNoBackground kNoTabChange kDelayAudioUntilUnsuppressed</VersionFlags>
			<InitialContexts>InMP</InitialContexts>
		</Item>
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_CORONA_LOBBY</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_CORONA_LOBBY</InitialScreen>
			
			<VersionFlags>kAllHighlighted kNotDimmable kTabsAreColumns kNoBackground kNoTabChange</VersionFlags>
			<InitialContexts>InMP</InitialContexts>
		</Item>
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_CORONA_JOINED_PLAYERS</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_CORONA_JOINED_PLAYERS</InitialScreen>
			
			<VersionFlags>kAllHighlighted kNotDimmable kTabsAreColumns kAutoShiftDepth kNoBackground</VersionFlags>
			<InitialContexts>InMP</InitialContexts>
		</Item>
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_CORONA_INVITE_PLAYERS</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_CORONA_INVITE_PLAYERS</InitialScreen>
			
			<VersionFlags>kAllHighlighted kNotDimmable kTabsAreColumns kAutoShiftDepth kNoBackground</VersionFlags>
			<InitialContexts>InMP</InitialContexts>
		</Item>
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_CORONA_INVITE_FRIENDS</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_CORONA_INVITE_FRIENDS</InitialScreen>
			
			<VersionFlags>kAllHighlighted kNotDimmable kTabsAreColumns kAutoShiftDepth kNoBackground</VersionFlags>
			<InitialContexts>InMP</InitialContexts>
		</Item>
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_CORONA_INVITE_CREWS</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_CORONA_INVITE_CREWS</InitialScreen>
			
			<VersionFlags>kAllHighlighted kNotDimmable kTabsAreColumns kAutoShiftDepth kNoBackground</VersionFlags>
			<InitialContexts>InMP</InitialContexts>
		</Item>
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_CORONA_INVITE_MATCHED_PLAYERS</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_CORONA_INVITE_MATCHED_PLAYERS</InitialScreen>
			
			<VersionFlags>kAllHighlighted kNotDimmable kTabsAreColumns kAutoShiftDepth kNoBackground</VersionFlags>
			<InitialContexts>InMP</InitialContexts>
		</Item>
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_CORONA_INVITE_LAST_JOB_PLAYERS</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_CORONA_INVITE_LAST_JOB_PLAYERS</InitialScreen>
			
			<VersionFlags>kAllHighlighted kNotDimmable kTabsAreColumns kAutoShiftDepth kNoBackground</VersionFlags>
			<InitialContexts>InMP</InitialContexts>
		</Item>
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_CORONA_RACE</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_RACE</InitialScreen>
			
			<VersionFlags>kAllHighlighted kNotDimmable kTabsAreColumns kNoBackground kNoTabChange</VersionFlags>
			<InitialContexts>InMP</InitialContexts>
		</Item>

		<Item>
			<MenuVersionId>FE_MENU_VERSION_CORONA_BETTING</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_BETTING</InitialScreen>
			
			<VersionFlags>kAllHighlighted kNotDimmable kTabsAreColumns kNoBackground kNoTabChange</VersionFlags>
			<InitialContexts>InMP</InitialContexts>
		</Item>
		
		<!-- WHATEVER THE JOINING SCREEN IS -->
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_JOINING_SCREEN</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_JOINING_SCREEN</InitialScreen>
			<VersionFlags> kNoBackground</VersionFlags>
		</Item>
		
		<Item>
			<MenuVersionId>FE_MENU_VERSION_LANDING_MENU</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_LANDING_PAGE</InitialScreen>
			<VersionFlags>kGeneratesMenuData kAllowBackingOut kAllowBackingOutInMenu kAllowStartButton kNotDimmable kNoBackground kAutoShiftDepth kNoPeds kNoTimeWarp kNoTabChange kNoPlayerInfo</VersionFlags>
		</Item>

		<Item>
			<MenuVersionId>FE_MENU_VERSION_LANDING_KEYMAPPING_MENU</MenuVersionId>
			<InitialScreen>MENU_UNIQUE_ID_HEADER_LANDING_KEYMAPPING</InitialScreen>
			<VersionFlags>kGeneratesMenuData kAllowBackingOut kAllowBackingOutInMenu kAllowStartButton kNotDimmable kNoBackground kAutoShiftDepth kNoPeds kNoTimeWarp kNoTabChange kNoPlayerInfo</VersionFlags>
		</Item>
	</MenuVersions>

	<MenuScreens>
	
	<!--
		== SCROLL FLAGS (for reference) ==
		
			Width_1 			Determine width of bar
			Width_2
			Width_3
			InitiallyVisible	Is it shown initially
			ManualUpdate		Will code/script be handling it, or will ActionScript?
			Arrows_LeftRight 	Which Arrows are desired? (LeftRight default)
			Arrows_UpDown
			
			Align_Left			Align left instead of center
			Align_Right			As above, but has extra information (1/12 on the left)
		
		
		
			== 	Regular Flags ==
			EnterMenuOnMouseClick Menu content will automatically activate when tab is with mouse
			NotDimmable			Menu is not dimmable
			Input_NoAdvance		Can't press A/Cross to go deeper from this menu			
			Input_NoBack		Can't press B/Circle to back out of this menu
			Input_NoTabChange	can't press LB/R1 to change away from this menu
			AlwaysUseButtons	Buttons will override the default ones even while not NAVIGATING_CONTENT

-->

		<!--  ACTUAL SCREEN ID: 0 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_SCR_MAP</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_MAP</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_BRF</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_INFO</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_STA</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_STATS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_GAM</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_GAME</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_SET</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS</MenuUniqueId> </Item>
				<!--<Item> <cTextId>PM_SCR_MIS</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_MISSION_CREATOR</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_FRI</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_FRIENDS</MenuUniqueId> </Item>-->
				<!--<Item> <cTextId>PM_SCR_SOC</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SOCIALCLUB</MenuUniqueId> </Item>-->
				<Item> <cTextId>PM_SCR_GAL</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_GALLERY</MenuUniqueId> </Item>
				<!--<Item> <cTextId>CMRC_GOTOSTORE</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_STORE</MenuUniqueId> </Item>-->
				<Item> <cTextId>PM_SCR_RPL</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_REPLAY_EDITOR</MenuUniqueId> <Contexts>*ALL*,ReplayEditor,*NONE*,Creator</Contexts> </Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: 1 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_SAVE_GAME</MenuScreen>
			<depth>0</depth>
			<MenuItems>
					<Item> <cTextId>PM_SCR_SAV</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SAVE_GAME</MenuUniqueId> </Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: 2 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_MULTIPLAYER</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_SCR_MAP</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_MAP</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_GAM</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_GAME</MenuUniqueId> </Item>
				<!--<Item> <cTextId>PM_SCR_PLA</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_PLAYERS</MenuUniqueId> </Item>-->
				<!--<Item> <cTextId>PM_SCR_FRI</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_FRIENDS_MP</MenuUniqueId> </Item>-->
				<Item> <cTextId>PM_SCR_INF</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_INFO</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_STA</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_STATS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_SET</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS</MenuUniqueId> </Item>
				<!-- MOVED INTO THE MP MENU
				<Item> <cTextId>PM_SCR_CRW</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CREWS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_WEA</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_WEAPONS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_AWA</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_MEDALS</MenuUniqueId> </Item>
				-->
				<Item> <cTextId>PM_SCR_GAL</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_GALLERY</MenuUniqueId> </Item>
				<!--<Item> <cTextId>CMRC_GOTOSTORE</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_STORE</MenuUniqueId> </Item>-->
				<Item> <cTextId>PM_SCR_RPL</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_REPLAY_EDITOR</MenuUniqueId> <Contexts>*ALL*, ReplayEditor</Contexts> </Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: 3 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_EMPTY</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<!-- this has no entries on purpose - please do not add any, or remove this! -->
			</MenuItems>
			<Flags>AlwaysUseButtons</Flags>
			<ButtonList>
				<ButtonPrompts>
					<CMenuButton> <hButtonHash>HUD_RETURNSP</hButtonHash>	<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> <Contexts>*ALL*,RETURN_TO_MENU</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash>		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> <Contexts>*ALL*,EMPTY_BACK_BUTTON</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_ENTPLY</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*ALL*,ENTER_PLAYLIST</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
		</Item>
		
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CUTSCENE_EMPTY</MenuScreen>
			<runtime>
				<type>CUTSCENE</type>
			</runtime>		
			<depth>0</depth>
			<MenuItems>
				<!-- this has no entries on purpose - please do not add any, or remove this! -->
			</MenuItems>
			<Flags>AlwaysUseButtons</Flags>
			<ButtonList>
				<ButtonPrompts>
					<CMenuButton> <hButtonHash>IB_ENTERPM</hButtonHash>		<ButtonInput>INPUT_FRONTEND_PAUSE</ButtonInput> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash>		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> </CMenuButton>
				
				</ButtonPrompts>
			</ButtonList>
		</Item>

		<!--  ACTUAL SCREEN ID: 4 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_TEXT_SELECTION</MenuScreen>
			<depth>0</depth>
			<Flags>NotDimmable SF_NoMenuAdvance SF_NoClearRootColumns</Flags>
			<MenuItems>
				<!--Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item-->
				<Item> <cTextId>PM_SCR_LLI</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_LOBBY_LIST</MenuUniqueId> </Item>
			</MenuItems>
		</Item>
		
		<!--  ACTUAL SCREEN ID: 5 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_PRE_LOBBY</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_SCR_PAR</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_PARTY</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_FRI</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_FRIENDS_MP</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_CRW</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CREWS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_WEA</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_WEAPONS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_AWA</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_MEDALS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_STA</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_STATS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_UNL</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_PARTY_LIST</MenuUniqueId> </Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: 6 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_LOBBY</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_SCR_LOB</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_LOBBY</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_FRI</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_FRIENDS_MP</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_CRW</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CREWS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_WEA</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_WEAPONS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_AWA</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_MEDALS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_STA</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_STATS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_UNL</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_PARTY_LIST</MenuUniqueId> </Item>
			</MenuItems>
		</Item>
		
		<!--  ACTUAL SCREEN ID: 5 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_JOINING_SCREEN</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_SCR_PLA</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_PLAYERS</MenuUniqueId> </Item>
			</MenuItems>
		</Item>
		
		<!--  CORONAS SCREEN ID: 1 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_CORONA</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_SCR_SET</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_SETTINGS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_PLA</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_SETTINGS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_DET</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_SETTINGS</MenuUniqueId> </Item>
			</MenuItems>
		</Item>

		<!--  CORONAS SCREEN ID: 2 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_CORONA_LOBBY</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_SCR_INV</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_PLA</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_MRC</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE</MenuUniqueId> </Item>
			</MenuItems>
		</Item>

		<!--  CORONAS SCREEN ID: 3 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_CORONA_JOINED_PLAYERS</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_SCR_JOP</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_JOINED_PLAYERS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_CHR</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_JOINED_PLAYERS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_PLC</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_JOINED_PLAYERS</MenuUniqueId> </Item>
			</MenuItems>
		</Item>
		<!--  CORONAS SCREEN ID: 4 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_CORONA_INVITE_PLAYERS</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_SCR_IVP</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_PLAYERS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_CHR</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_PLAYERS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_PLC</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_PLAYERS</MenuUniqueId> </Item>
			</MenuItems>
		</Item>
		<!--  CORONAS SCREEN ID: 5 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_CORONA_INVITE_FRIENDS</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_SCR_IFR</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_FRIENDS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_CHR</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_FRIENDS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_PLC</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_FRIENDS</MenuUniqueId> </Item>
			</MenuItems>
		</Item>
		<!--  CORONAS SCREEN ID: 6 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_CORONA_INVITE_CREWS</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_SCR_ICM</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_CREWS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_CHR</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_CREWS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_PLC</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_CREWS</MenuUniqueId> </Item>
			</MenuItems>
		</Item>

		<!--  CORONAS SCREEN ID: 7 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_CORONA_INVITE_MATCHED_PLAYERS</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_SCR_IMP</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_MATCHED_PLAYERS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_CHR</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_MATCHED_PLAYERS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_PLC</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_MATCHED_PLAYERS</MenuUniqueId> </Item>
			</MenuItems>
		</Item>

		<!--  CORONAS SCREEN ID: 8 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_CORONA_INVITE_LAST_JOB_PLAYERS</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_SCR_ILJ</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_LAST_JOB_PLAYERS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_CHR</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_LAST_JOB_PLAYERS</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_PLC</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_LAST_JOB_PLAYERS</MenuUniqueId> </Item>
			</MenuItems>
		</Item>
		
		<!--  ACTUAL SCREEN ID: 7 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_MAP</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_MAP</cGfxFilename>
			<runtime>
				<type>MAP</type>
				<params>
					<data key="path">PauseMenu_Map</data>
					<data key="sp_path">PauseMenu_Map</data>
					<data key="continual">TRUE</data>
				</params>
			</runtime>
			
			<depth>1</depth>
			<ScrollBarFlags>Width_1 Offset_2Right Arrows_UpDown Align_Right</ScrollBarFlags>


			<ButtonList>
				<ButtonPrompts>
				
					<CMenuButton> <hButtonHash>IB_MS_WARP</hButtonHash>		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*ALL*,MAP_CanWarp, *NONE*,CORONA_BIGMAP_CLOSE, CORONA_BIGMAP_CLOSE_KM</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_WAYPOINT</hButtonHash>	<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*ALL*,MAP_Waypoint, *NONE*,CORONA_BIGMAP_CLOSE, CORONA_BIGMAP_CLOSE_KM, MAP_Destination</Contexts></CMenuButton>
          <CMenuButton> <hButtonHash>IB_SETDEST</hButtonHash>	<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*ALL*,MAP_Destination, *NONE*,CORONA_BIGMAP_CLOSE, CORONA_BIGMAP_CLOSE_KM</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_ZOOM</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_TRIGGERS</ButtonInputGroup> <Contexts>*ALL*,MAP_CanZoom</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_POI</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*ALL*,MAP_CanDropPOI, *NONE*,CORONA_BIGMAP_CLOSE, CORONA_BIGMAP_CLOSE_KM</Contexts></CMenuButton>
				<!--	<CMenuButton> <hButtonHash>IB_CRIMINAL</hButtonHash>	<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> <Contexts>*ALL*,MAP_Criminal</Contexts></CMenuButton> -->
					<CMenuButton> <hButtonHash>IB_INTEXT</hButtonHash>		<ButtonInput>INPUT_FRONTEND_SELECT</ButtonInput> <Contexts>*ALL*,MAP_Interior,*NONE*,MAP_Criminal, *NONE*,CORONA_BIGMAP_CLOSE, CORONA_BIGMAP_CLOSE_KM</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_GOLF</hButtonHash>		<ButtonInput>INPUT_FRONTEND_SELECT</ButtonInput> <Contexts>*ALL*,MAP_Golf,*NONE*,MAP_Interior,MAP_Criminal, *NONE*,CORONA_BIGMAP_CLOSE, CORONA_BIGMAP_CLOSE_KM</Contexts></CMenuButton>
				
					<!--<CMenuButton> <hButtonHash>IB_NAVIGATE</hButtonHash>	<ButtonInputGroup>INPUTGROUP_FRONTEND_LSTICK_ALL</ButtonInputGroup></CMenuButton>-->
					<CMenuButton> <hButtonHash>IB_LEGEND</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_DPAD_ALL</ButtonInputGroup> <Contexts>*NONE*,CORONA_BIGMAP_CLOSE, CORONA_BIGMAP_CLOSE_KM</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_BLIPVIS1</hButtonHash>		<ButtonInput>INPUT_FRONTEND_RS</ButtonInput> <Contexts>*ALL*,MAP_VisHigh, *NONE*,CORONA_BIGMAP_CLOSE, CORONA_BIGMAP_CLOSE_KM</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_BLIPVIS0</hButtonHash>		<ButtonInput>INPUT_FRONTEND_RS</ButtonInput> <Contexts>*ALL*,MAP_VisLow, *NONE*,CORONA_BIGMAP_CLOSE, CORONA_BIGMAP_CLOSE_KM</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_SRTMISS</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,MAP_CanStartMiss, *NONE*,CORONA_BIGMAP_CLOSE, CORONA_BIGMAP_CLOSE_KM</Contexts></CMenuButton>
          <CMenuButton> <hButtonHash>BLIP_CONT</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,MAP_CanContact, *NONE*,CORONA_BIGMAP_CLOSE, CORONA_BIGMAP_CLOSE_KM</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> <Contexts>*NONE*,CORONA_BIGMAP_CLOSE, CORONA_BIGMAP_CLOSE_KM</Contexts> </CMenuButton>
					
					<CMenuButton> <hButtonHash>FM_COR_HIDE_BIGMAP</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>			<Contexts>*ALL*, CORONA_BIGMAP_CLOSE, *NONE*</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>FM_COR_HIDE_BIGMAP</hButtonHash>		<ButtonInput>INPUT_FRONTEND_LS</ButtonInput>		<Contexts>*ALL*, CORONA_BIGMAP_CLOSE_KM, *NONE*</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
			<MenuItems>	 
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>   
		</Item>
		
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_MAP_LEGEND</MenuScreen>
			<depth>3</depth>
			<Flags>SF_NoClearRootColumns SF_NoMenuAdvance</Flags>
			<ScrollBarFlags>Width_2 Arrows_LeftRight Arrows_UpDown InitiallyVisible</ScrollBarFlags>
		</Item>

		<!--  ACTUAL SCREEN ID: 8 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_INFO</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_INFO</cGfxFilename>
			<runtime>
				<type>INFO</type>
			</runtime>
			<depth>1</depth>
			<Flags>EnterMenuOnMouseClick</Flags>
			<MenuItems>
				<!-- <Item> <cTextId>PM_PANE_MIS</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_HOME_MISSION</MenuUniqueId> <Contexts>*ANY*,InSP</Contexts> </Item> -->
				<Item> <cTextId>PM_PANE_MIS</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_HOME_MISSION</MenuUniqueId> </Item>
				<!-- <Item> <cTextId>PM_PANE_MPJ</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_HOME_MISSION</MenuUniqueId> <Contexts>*ANY*,InMP</Contexts> </Item> -->
				<Item> <cTextId>PM_PANE_HLP</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_HOME_HELP</MenuUniqueId> </Item>
				<Item> <cTextId>PM_PANE_BRI</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_HOME_BRIEF</MenuUniqueId> </Item>
				<Item> <cTextId>PM_PANE_FEE</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_HOME_FEED</MenuUniqueId>	</Item>
				<!-- <Item> <cTextId>PM_PANE_NEWS</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_HOME_NEWSWIRE</MenuUniqueId>	</Item> -->
				<!-- <Item> <cTextId>PM_PANE_XBHELP</cTextId> <MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_HELP</MenuUniqueId> <Contexts>*ALL*, DURANGO</Contexts></Item> -->
			</MenuItems>
			
			<ButtonList>
				<ButtonPrompts>
					<!--<CMenuButton> <hButtonHash>FE_HLP5</hButtonHash>		<RawButtonIcon>ARROW_UPDOWN</RawButtonIcon> 	  	<Contexts>*ANY*,SINGLE_SCREEN,NAVIGATING_CONTENT</Contexts> </CMenuButton>-->
					<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash>	<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>		 <Contexts>*ALL*,NewswireOffline</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_SCROLL</hButtonHash>	<ButtonInput>INPUT_FRONTEND_RIGHT_AXIS_Y</ButtonInput>		 <Contexts>*ALL*,INFO_CanScroll,*NONE*,NewswireOffline</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash>		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>				<Contexts>*ALL*,SHOW_ACCEPTBUTTON_INFO</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 			</CMenuButton>
				</ButtonPrompts>
			</ButtonList>
		</Item>

		<!-- <ScrollBarFlags>Width_1 DisplayOnlyOnFocus Offset_2Right</ScrollBarFlags> -->
		<!--  ACTUAL SCREEN ID: 9 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_SETTINGS</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_SETTINGS</cGfxFilename>
			<runtime>
				<type>SETTINGS</type>
				<!--
				<params>
					<data key="fbookColumnHeight">403</data>
				</params>
				-->
			</runtime>
			<depth>1</depth>
			<Flags>LayoutChangedOnBack EnterMenuOnMouseClick</Flags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
				<Item> <cTextId>PM_PANE_CON</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_CONTROLS</MenuUniqueId> <Contexts>*NONE*,x64</Contexts></Item>
				<Item> <cTextId>PM_PANE_GPC</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_CONTROLS</MenuUniqueId> <Contexts>*ALL*,x64</Contexts> </Item>
				<Item> <cTextId>PM_PANE_MCON</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_MISC_CONTROLS</MenuUniqueId> <Contexts>*NONE*,StreamingBuild,*ALL*,x64</Contexts> </Item>
				<Item platform="pc_only"> <cTextId>PM_PANE_KEYS</cTextId> <MenuAction>MENU_OPTION_ACTION_INCEPT</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_KEYMAP</MenuUniqueId> <Contexts>*NONE*,StreamingBuild</Contexts> </Item>
        <!--<Item> <cTextId>PM_PANE_FPS</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_FIRST_PERSON</MenuUniqueId> </Item>-->
				<Item> <cTextId>PM_PANE_AUD</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_AUDIO</MenuUniqueId> </Item>
        <Item> <cTextId>PM_PANE_CAM</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_CAMERA</MenuUniqueId> </Item>
				<Item> <cTextId>PM_PANE_DIS</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_DISPLAY</MenuUniqueId> </Item>
				<Item> <cTextId>PM_PANE_GFX</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_GRAPHICS</MenuUniqueId> <Contexts>*NONE*,StreamingBuild,*ALL*,x64</Contexts> </Item>
        <Item> <cTextId>PM_PANE_AGFX</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_ADVANCED_GFX</MenuUniqueId> <Contexts>*NONE*,StreamingBuild,*ALL*,x64</Contexts> </Item>
				<Item> <cTextId>MO_VOUT</cTextId>	  <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_VOICE_CHAT</MenuUniqueId> <Contexts>*ALL*,x64</Contexts> </Item>
				<!--<Item> <cTextId>PM_PANE_FEE</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_FEED</MenuUniqueId> </Item>-->
				<Item> <cTextId>PM_PANE_RPY</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_REPLAY</MenuUniqueId> <Contexts>*ALL*,ReplayEditor</Contexts> </Item>
				<Item> <cTextId>PM_PANE_SAV</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_SAVEGAME</MenuUniqueId> <Contexts>*ANY*,InSP</Contexts> </Item>
				<Item> <cTextId>MO_SIXAXIS</cTextId>  <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_SIXAXIS</MenuUniqueId> <Contexts>*ALL*,PS4</Contexts></Item>
				<!--<Item> <cTextId>PM_PANE_FB</cTextId>  <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_FACEBOOK</MenuUniqueId> <Contexts>*NONE*,OnLandingPage,FB_Disabled</Contexts> </Item>-->
			</MenuItems>
			
			<ButtonList>
				<ButtonPrompts>
					<!--<CMenuButton> <hButtonHash>FE_HLP5</hButtonHash>		<RawButtonIcon>ARROW_UPDOWN</RawButtonIcon> 	  	</CMenuButton>-->
					<CMenuButton> <hButtonHash>IB_ADJUST</hButtonHash>		<RawButtonIcon>ARROW_LEFTRIGHT</RawButtonIcon> 	  	<Contexts>*ALL*,SETTINGS_PrefSwitch</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>MO_GFX_APPLY</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput>				<Contexts>*ANY*,GFX_Dirty</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>MO_GFX_BENCHMARK</hButtonHash>	<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>				<Contexts>*ANY*,InSP,OnLandingPage,*ALL*,ON_VID_GFX_MENU,NOT_ON_A_MISSION</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash>		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>				<Contexts>*NONE*,HIDE_ACCEPTBUTTON</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
		</Item>
		
		<Item platform="pc_only">
			<MenuScreen>MENU_UNIQUE_ID_KEYMAP</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_KEYMAP</cGfxFilename>
			<runtime>
				<type>KEYMAP</type>
			</runtime>
			<depth>1</depth>
			<Flags>HandlesDisplayDataSlot LayoutChangedOnBack SF_NoClearRootColumns EnterMenuOnMouseClick</Flags>
			

			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
				<Item>	<MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction>	<MenuUniqueId>MENU_UNIQUE_ID_KEYMAP_LIST</MenuUniqueId>	</Item>
				<Item>	<MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction>	<MenuUniqueId>MENU_UNIQUE_ID_KEYMAP_LISTITEM</MenuUniqueId>	</Item>
				<!--
				<Item> <cTextId>PM_PANE_GENERAL</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_KEYMAP_GENERAL</MenuUniqueId> </Item>
				<Item> <cTextId>PM_PANE_FOOT</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_KEYMAP_FOOT</MenuUniqueId> </Item>
				<Item> <cTextId>PM_PANE_VEH</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_KEYMAP_VEHICLE</MenuUniqueId> </Item>
				<Item> <cTextId>PM_PANE_AIR</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_KEYMAP_AIRCRAFT</MenuUniqueId> </Item>
				<Item> <cTextId>PM_PANE_GTAO</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_KEYMAP_GTAO</MenuUniqueId> </Item>
				-->
			</MenuItems>
		
			<ButtonList>
				<ButtonPrompts>
					<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash>		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>	<Contexts>*NONE*,HIDE_ACCEPTBUTTON</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_DELETE</hButtonHash>		<ButtonInput>INPUT_FRONTEND_DELETE</ButtonInput>	<Contexts>*ALL*,KEYMAP_CAN_DELETE</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
		</Item>

<!--
		<Item platform="pc_only">
			<MenuScreen>MENU_UNIQUE_ID_KEYMAP_LIST</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns</Flags>
		</Item>
		-->
		<Item platform="pc_only">
			<MenuScreen>MENU_UNIQUE_ID_KEYMAP_LISTITEM</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns LayoutChangedOnBack NeverGenerateMenuData</Flags>
			<ScrollBarFlags>Width_2 ManualUpdate InitiallyInvisible</ScrollBarFlags>
		</Item>

		<!--  ACTUAL SCREEN ID: 10 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_FRIENDS</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_FRIENDS</cGfxFilename>
			<runtime>
				<type>FRIENDS</type>
				<params>
					<data key="path">SP_MENUPED</data>
					<data key="canpersist">TRUE</data>
				</params>
			</runtime>
			<depth>1</depth>
			<ScrollBarFlags>Width_1 InitiallyInvisible ManualUpdate Align_Right</ScrollBarFlags>
			<Flags>HandlesDisplayDataSlot EnterMenuOnMouseClick</Flags>
			<ButtonList>
				<ButtonPrompts IncludeDefaults="true">
					<CMenuButton> <hButtonHash>IB_SOCIAL_CLUB</hButtonHash> <ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*ALL*,NotInSocialClub,NAVIGATING_CONTENT,*NONE*,Offline,UpdateSocialClub</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_UPDATE_SOCIAL_CLUB</hButtonHash> <ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*ALL*,UpdateSocialClub,NAVIGATING_CONTENT,*NONE*,Offline,NotInSocialClub</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_COMPARE</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,SyncingStats,Comparing,*ALL*,HasPlayers,NAVIGATING_CONTENT,HasPlayedMode</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_UNCOMPARE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,SyncingStats,*ALL*,HasPlayers,NAVIGATING_CONTENT,HasPlayedMode,Comparing</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_REFRESH</hButtonHash>	<ButtonInput>INPUT_FRONTEND_SELECT</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,SyncingStats,*ALL*,NAVIGATING_CONTENT,CanRefreshFriends</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
			<ContextMenu>
				<TriggerMenuId>MENU_UNIQUE_ID_FRIENDS_LIST</TriggerMenuId>
				<ContextMenuId>MENU_UNIQUE_ID_FRIENDS_OPTIONS_LIST</ContextMenuId>
				<depth>2</depth>
				<contextOptions>
					<contextOption>JOIN_GAME</contextOption>
					<contextOption>JIP_GAME</contextOption>
					<contextOption>JIP_GAME_DIS</contextOption>
					<contextOption>JIP_GAME_TRANSITION</contextOption>
					<contextOption>SEND_GAME_INVITE</contextOption>
					<contextOption>SEND_GAME_INVITE_DIS</contextOption>
					<contextOption>SEND_PARTY_INVITE</contextOption>
					<contextOption>JOIN_PARTY</contextOption>
					<contextOption>KICK_PARTY</contextOption>
					<contextOption>SEND_MESSAGE</contextOption>
					<contextOption>SHOW_GAMER_CARD</contextOption>
					<contextOption>SPECTATE_OTHER_SESSION</contextOption>
					<contextOption>SPECTATE_OTHER_SESSION_DISABLED</contextOption>
					<contextOption>GAMER_REVIEW</contextOption>
					<contextOption>SEND_CREW_INVITE</contextOption>
					<contextOption>ADD_A_FRIEND</contextOption>
				</contextOptions>
			</ContextMenu>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: 11 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CREWS</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CREWS</cGfxFilename>
			<runtime>
				<type>CREW</type>
			</runtime>
			<!--<Flags>SF_CallImmediately</Flags>-->
			<Flags>LayoutChangedOnBack</Flags>
			<depth>1</depth>
			<MenuItems>
				<Item>	<MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
				<Item>	<cTextId>CRW_MINE</cTextId>			<MenuAction>MENU_OPTION_ACTION_LINK</MenuAction>	<MenuUniqueId>MENU_UNIQUE_ID_CREW_MINE</MenuUniqueId>			</Item>
				<Item>	<cTextId>CRW_ROCKSTAR</cTextId>		<MenuAction>MENU_OPTION_ACTION_LINK</MenuAction>	<MenuUniqueId>MENU_UNIQUE_ID_CREW_ROCKSTAR</MenuUniqueId>		</Item>
				<Item>	<cTextId>CRW_FRIENDS</cTextId>		<MenuAction>MENU_OPTION_ACTION_LINK</MenuAction>	<MenuUniqueId>MENU_UNIQUE_ID_CREW_FRIENDS</MenuUniqueId>		</Item>
				<Item>	<cTextId>CRW_INVITES</cTextId>		<MenuAction>MENU_OPTION_ACTION_LINK</MenuAction>	<MenuUniqueId>MENU_UNIQUE_ID_CREW_INVITES</MenuUniqueId>		<!--<Flags>InitiallyDisabled</Flags>--></Item>
				<Item>	<cTextId>CRW_REQUESTS</cTextId>		<MenuAction>MENU_OPTION_ACTION_LINK</MenuAction>	<MenuUniqueId>MENU_UNIQUE_ID_CREW_REQUEST</MenuUniqueId>		<!--<Flags>InitiallyDisabled</Flags>--></Item>
				<Item>	<cTextId>CRW_LEADERBOARDS</cTextId>	<MenuAction>MENU_OPTION_ACTION_LINK</MenuAction>	<MenuUniqueId>MENU_UNIQUE_ID_CREW_LEADERBOARDS</MenuUniqueId>	</Item>
				
				<Item>	<MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction>	<MenuUniqueId>MENU_UNIQUE_ID_CREW</MenuUniqueId>	</Item>
			</MenuItems>
			<ButtonList>
				<ButtonPrompts>
					<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash> <ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*NONE*,MENU_OFF,HIDE_ACCEPTBUTTON,HasContextMenu</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_OPTIONS</hButtonHash> <ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*NONE*,MENU_OFF,HIDE_ACCEPTBUTTON,*ALL*,HasContextMenu</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> <ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_COMPARE</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,CAN_COMPARE,CREW_SingleCard,			*NONE*,IN_CREW_DETAILS,Crew_NoMembership</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_LBD_LBD</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,CAN_COMPARE,CREW_SingleCard,VARIANT_LBS,CREW_NoMembership,*NONE*,IN_CREW_DETAILS</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_CREW_CARD</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,CAN_COMPARE,CREW_Compare,				*NONE*,IN_CREW_DETAILS,VARIANT_LBS</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_CREW_CARD</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,CAN_COMPARE,CREW_Stats,VARIANT_LBS,		*NONE*,IN_CREW_DETAILS</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_LBD_LBD</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,CAN_COMPARE,CREW_Compare,VARIANT_LBS,	*NONE*,IN_CREW_DETAILS,CREW_NoMembership</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>

			<ContextMenu>
				<TriggerMenuId>MENU_UNIQUE_ID_CREW_LIST</TriggerMenuId>
				<ContextMenuId>MENU_UNIQUE_ID_FRIENDS_OPTIONS_LIST</ContextMenuId><!-- not really this id, but I couldn't care less -->
				<depth>3</depth>
				<contextOptions>
					<contextOption>SET_PRIMARY</contextOption>
					<contextOption>VIEW_MEMBERS</contextOption>
					<contextOption>JOIN_CREW</contextOption>
					<contextOption>REQUEST_INVITE</contextOption>
					<contextOption>LEAVE_CREW</contextOption>
					<contextOption>INVITE_ACCEPT</contextOption>
					<contextOption>INVITE_DECLINE</contextOption>
				</contextOptions>
			</ContextMenu>
	
		</Item>

		<!--  ACTUAL SCREEN ID: (Hashed):  0x2157f8ef -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CREW_MINE</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns HandlesDisplayDataSlot</Flags>
			<!--<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>-->
		</Item>

		<!--  ACTUAL SCREEN ID: (Hashed) 0xf52d2a6b -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CREW_ROCKSTAR</MenuScreen>
			<depth>2</depth>
			<ScrollBarFlags>Width_1 ManualUpdate Align_Right InitiallyInvisible</ScrollBarFlags>
			<Flags>SF_NoClearRootColumns HandlesDisplayDataSlot</Flags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction></Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: (Hashed) 0xad3068e0-->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CREW_FRIENDS</MenuScreen>
			<depth>2</depth>
			<ScrollBarFlags>Width_1 ManualUpdate Align_Right InitiallyInvisible</ScrollBarFlags>
			<Flags>SF_NoClearRootColumns HandlesDisplayDataSlot</Flags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction></Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: (Hashed) 0xda70b4be-->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CREW_INVITES</MenuScreen>
			<depth>2</depth>
			<ScrollBarFlags>Width_1 ManualUpdate Align_Right InitiallyInvisible</ScrollBarFlags>
			<Flags>SF_NoClearRootColumns HandlesDisplayDataSlot</Flags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction></Item>
			</MenuItems>
		</Item>
		
		<!-- CREW REQUEST -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CREW_REQUEST</MenuScreen>
			<depth>2</depth>
			<ScrollBarFlags>Width_1 ManualUpdate Align_Right InitiallyInvisible</ScrollBarFlags>
			<Flags>SF_NoClearRootColumns HandlesDisplayDataSlot</Flags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction></Item>
			</MenuItems>

			<ContextMenu>
				<TriggerMenuId>MENU_UNIQUE_ID_CREW_LIST</TriggerMenuId>
				<ContextMenuId>MENU_UNIQUE_ID_FRIENDS_OPTIONS_LIST</ContextMenuId>
				<!-- not really this id, but I couldn't care less -->
				<depth>3</depth>
				<contextOptions>
					<contextOption>REQUEST_ACCEPT</contextOption>
					<contextOption>REQUEST_REJECT</contextOption>
					<contextOption>SEND_FRIEND_INVITE</contextOption>
					<contextOption>SEND_MESSAGE</contextOption>
					<contextOption>SHOW_GAMER_CARD</contextOption>
					<contextOption>GAMER_REVIEW</contextOption>
				</contextOptions>
			</ContextMenu>
		</Item>
		
		<!--  ACTUAL SCREEN ID: -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CREW_LEADERBOARDS</MenuScreen>
			<depth>2</depth>
			<ScrollBarFlags>Width_2 ManualUpdate Align_Right InitiallyInvisible</ScrollBarFlags>
			<Flags>SF_NoClearRootColumns HandlesDisplayDataSlot</Flags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction></Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: 67 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_FRIENDS_LIST</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns SF_NoMenuAdvance NeverGenerateMenuData</Flags>
    	</Item>

		<!--  ACTUAL SCREEN ID: 11 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_WEAPONS</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_WEAPONS</cGfxFilename>
			<runtime>
				<type>SCRIPT</type>
				<params>
					<data key="path">MP_Weapons</data>
					<data key="continual">TRUE</data>
				</params>
			</runtime>
			
			<depth>1</depth>
			<ScrollBarFlags>Width_3 Arrows_UpDown Arrows_LeftRight Align_Right</ScrollBarFlags>
			<ButtonList>
				<ButtonPrompts IncludeDefaults="true" IncludeNavigate="false">
					<!--<CMenuButton> <hButtonHash>IB_NAVIGATE</hButtonHash>	<RawButtonIcon>ARROW_ALL</RawButtonIcon><Contexts>*ALL*,NAVIGATING_CONTENT</Contexts></CMenuButton>-->
				</ButtonPrompts>
			</ButtonList>

			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: 12 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_MEDALS</MenuScreen>
			<runtime>
				<type>SCRIPT</type>
				<params>
					<data key="path">MP_Awards</data>
					<data key="continual">TRUE</data>
				</params>
			</runtime>
			<cGfxFilename>PAUSE_MENU_PAGES_AWARDS</cGfxFilename>
			<depth>1</depth>
			<ScrollBarFlags>Width_2 Offset_1Right Arrows_UpDown Arrows_LeftRight Align_Right ManualUpdate InitiallyInvisible</ScrollBarFlags>
			<ButtonList>
				<ButtonPrompts IncludeDefaults="true" IncludeNavigate="false">			
					<!--<CMenuButton> <hButtonHash>IB_NAVIGATE</hButtonHash>	<RawButtonIcon>ARROW_ALL</RawButtonIcon><Contexts>*ALL*,IS_NAVIGATING_ALL,*NONE*,IS_NAVIGATING_UPDOWN</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT1</hButtonHash>	<RawButtonIcon>ARROW_UPDOWN</RawButtonIcon><Contexts>*ALL*,IS_NAVIGATING_UPDOWN,*NONE*,IS_NAVIGATING_ALL</Contexts></CMenuButton>-->
					<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash>		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*ANY*,STATS_CanSelect</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: 13 -->
		 <Item> 
			<MenuScreen>MENU_UNIQUE_ID_PARTY</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_FRIENDS_MP</cGfxFilename>
			<runtime>
				<type>PARTY</type>
				<params>
					<data key="path">MP_MENUPED</data>
					<data key="canpersist">TRUE</data>
				</params>
			</runtime>			 
			<depth>1</depth>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
			<ScrollBarFlags>Width_1 ManualUpdate Align_Right</ScrollBarFlags>
			<Flags>HandlesDisplayDataSlot</Flags>
			<ButtonList>
				<ButtonPrompts IncludeDefaults="true">
					<CMenuButton> <hButtonHash>IB_SOCIAL_CLUB</hButtonHash> <ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*ALL*,NotInSocialClub,NAVIGATING_CONTENT,*NONE*,Xbox360,Offline,UpdateSocialClub</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_UPDATE_SOCIAL_CLUB</hButtonHash> <ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*ALL*,UpdateSocialClub,NAVIGATING_CONTENT,*NONE*,Xbox360,Offline,NotInSocialClub</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_COMPARE</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,SyncingStats,Comparing,*ALL*,HasPlayers,NAVIGATING_CONTENT,HasPlayedMode</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_UNCOMPARE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,SyncingStats,*ALL*,HasPlayers,NAVIGATING_CONTENT,HasPlayedMode,Comparing</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_INVITE_PARTY</hButtonHash>	<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*NONE*,SoloSession, *ALL*,Xbox360,HasPlayers,NAVIGATING_CONTENT</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
			<ContextMenu>
				<TriggerMenuId>MENU_UNIQUE_ID_PARTY_LIST</TriggerMenuId>
				<ContextMenuId>MENU_UNIQUE_ID_PARTY_OPTIONS_LIST</ContextMenuId>
				<depth>2</depth>
				
				<contextOptions>
					<contextOption>JOIN_GAME</contextOption>
					<contextOption>JIP_GAME</contextOption>
					<contextOption>JIP_GAME_DIS</contextOption>
					<contextOption>JIP_GAME_TRANSITION</contextOption>
					<contextOption>SEND_GAME_INVITE</contextOption>
					<contextOption>SEND_GAME_INVITE_DIS</contextOption>
					<contextOption>SEND_MESSAGE</contextOption>
					<contextOption>MUTE</contextOption>
					<contextOption>KICK</contextOption>
					<contextOption>KICK_DISABLED</contextOption>
					<contextOption>UNKICK</contextOption>
					<contextOption>KICK_PARTY</contextOption>
					<contextOption>SHOW_GAMER_CARD</contextOption>
					<contextOption>SPECTATE_OTHER_SESSION</contextOption>
					<contextOption>SPECTATE_OTHER_SESSION_DISABLED</contextOption>
					<contextOption>GAMER_REVIEW</contextOption>
					<contextOption>SEND_FRIEND_INVITE</contextOption>
					<contextOption>SEND_CREW_INVITE</contextOption>
				</contextOptions>
			</ContextMenu>	
		</Item>
		
		<!--  ACTUAL SCREEN ID: 14 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_LOBBY</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_FRIENDS_MP</cGfxFilename>
			<runtime>
				<type>LOBBY</type>
				<params>
					<data key="path">MP_Awards</data>
					<data key="continual">TRUE</data>
				</params>
			</runtime>
			<depth>1</depth>
			<ScrollBarFlags>Width_1 ManualUpdate Align_Right</ScrollBarFlags>
			<Flags>HandlesDisplayDataSlot</Flags>
			
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			
			</MenuItems>
			<ButtonList>
				<ButtonPrompts IncludeDefaults="true">
					<CMenuButton> <hButtonHash>IB_SOCIAL_CLUB</hButtonHash> <ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*ALL*,NotInSocialClub,NAVIGATING_CONTENT,*NONE*,Offline,UpdateSocialClub</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_UPDATE_SOCIAL_CLUB</hButtonHash> <ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*ALL*,UpdateSocialClub,NAVIGATING_CONTENT,*NONE*,Offline,NotInSocialClub</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_COMPARE</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,SyncingStats,Comparing,*ALL*,HasPlayers,NAVIGATING_CONTENT,HasPlayedMode</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_UNCOMPARE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,SyncingStats,*ALL*,HasPlayers,NAVIGATING_CONTENT,HasPlayedMode,Comparing</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
			
			<ContextMenu>
				<TriggerMenuId>MENU_UNIQUE_ID_PLAYERS_LIST</TriggerMenuId>
				<ContextMenuId>MENU_UNIQUE_ID_PLAYERS_OPTIONS_LIST</ContextMenuId>
				<depth>2</depth>
			
				<contextOptions>
					<contextOption>SEND_PARTY_INVITE</contextOption>
					<contextOption>SEND_FRIEND_INVITE</contextOption>
					<contextOption>SEND_CREW_INVITE</contextOption>
					<contextOption>SEND_MESSAGE</contextOption>
					<contextOption>SPECTATE_OTHER_SESSION</contextOption>
					<contextOption>SPECTATE_OTHER_SESSION_DISABLED</contextOption>
					<contextOption>JOIN_PARTY</contextOption>
					<contextOption>MUTE</contextOption>
					<contextOption>KICK</contextOption>
					<contextOption>KICK_DISABLED</contextOption>
					<contextOption>UNKICK</contextOption>
					<contextOption>KICK_PARTY</contextOption>
					<contextOption>REPORT</contextOption>
					<contextOption>REPORT_DISABLED</contextOption>
					<contextOption>COMMEND</contextOption>
					<contextOption>COMMEND_DISABLED</contextOption>
					<contextOption>SHOW_GAMER_CARD</contextOption>
					<contextOption>GAMER_REVIEW</contextOption>
				</contextOptions>
			</ContextMenu>					
		</Item>

		<!--  ACTUAL SCREEN ID: 15 -->
		<Item> 
			<MenuScreen>MENU_UNIQUE_ID_PLAYERS</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_FRIENDS_MP</cGfxFilename>
			<runtime>
				<type>PLAYERS</type>
			</runtime>
			<depth>1</depth>
			<ScrollBarFlags>Width_1 ManualUpdate Align_Right</ScrollBarFlags>
			<Flags>HandlesDisplayDataSlot</Flags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
			<ButtonList>
				<ButtonPrompts IncludeDefaults="true">
					<CMenuButton> <hButtonHash>IB_SOCIAL_CLUB</hButtonHash> <ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*ALL*,NotInSocialClub,NAVIGATING_CONTENT,*NONE*,Offline,UpdateSocialClub</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_UPDATE_SOCIAL_CLUB</hButtonHash> <ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*ALL*,UpdateSocialClub,NAVIGATING_CONTENT,*NONE*,Offline,NotInSocialClub</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_COMPARE</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,SyncingStats,Comparing,*ALL*,HasPlayers,NAVIGATING_CONTENT,HasPlayedMode</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_UNCOMPARE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,SyncingStats,*ALL*,HasPlayers,NAVIGATING_CONTENT,HasPlayedMode,Comparing</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
			
			<ContextMenu>
				<TriggerMenuId>MENU_UNIQUE_ID_PLAYERS_LIST</TriggerMenuId>
				<ContextMenuId>MENU_UNIQUE_ID_PLAYERS_OPTIONS_LIST</ContextMenuId>
				<depth>2</depth>
			
				<contextOptions>
					<contextOption>SEND_PARTY_INVITE</contextOption>
					<contextOption>SEND_FRIEND_INVITE</contextOption>
					<contextOption>SEND_CREW_INVITE</contextOption>
					<contextOption>SEND_MESSAGE</contextOption>
					<contextOption>JIP_GAME_TRANSITION</contextOption>
					<contextOption>JOIN_PARTY</contextOption>
					<contextOption>MUTE</contextOption>
					<contextOption>KICK</contextOption>
					<contextOption>KICK_DISABLED</contextOption>
					<contextOption>UNKICK</contextOption>
					<contextOption>KICK_PARTY</contextOption>
					<contextOption>REPORT</contextOption>
					<contextOption>REPORT_DISABLED</contextOption>
					<contextOption>COMMEND</contextOption>
					<contextOption>COMMEND_DISABLED</contextOption>
					<contextOption>SHOW_GAMER_CARD</contextOption>
					<contextOption>GAMER_REVIEW</contextOption>
				</contextOptions>
			</ContextMenu>					
		</Item>
	
		<!--  ACTUAL SCREEN ID: 16 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_STATS</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_STATS</cGfxFilename>
			<runtime>
				<type>STATS</type>
			</runtime>
			<depth>1</depth>
			<Flags>Sound_NoAccept SF_NoClearRootColumns EnterMenuOnMouseClick</Flags>
			
			<MenuItems>
				<Item>
					<MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction>
					<!-- MENU_UNIQUE_ID_STATS_LISTITEM is used here by code-->
				</Item>
			</MenuItems>
			
			<ButtonList>
				<ButtonPrompts>
					<CMenuButton> <hButtonHash>HUD_INPUT1C</hButtonHash>	<ButtonInputGroup>INPUTGROUP_FRONTEND_DPAD_LR</ButtonInputGroup> 		<Contexts>*NONE*,NAVIGATING_CONTENT</Contexts></CMenuButton>
					<!--<CMenuButton> <hButtonHash>FE_HLP5</hButtonHash>		<RawButtonIcon>ARROW_UPDOWN</RawButtonIcon> 	  	<Contexts>*ANY*,SINGLE_SCREEN,NAVIGATING_CONTENT</Contexts></CMenuButton>-->
          <!--<CMenuButton> <hButtonHash>CMRC_GOTOSTORE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*ALL*,MENU_UNIQUE_ID_HEADER,STORE_AVAILABLE</Contexts> </CMenuButton>-->
					<CMenuButton> <hButtonHash>IB_ADJUST</hButtonHash>		<RawButtonIcon>ARROW_LEFTRIGHT</RawButtonIcon> 	  	<Contexts>*ALL*,STATS_PlayerSwitch,*ANY*,SINGLE_SCREEN,NAVIGATING_CONTENT</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash>		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*ANY*,STATS_CanSelect</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_SCROLL</hButtonHash>	<ButtonInput>INPUT_FRONTEND_RIGHT_AXIS_Y</ButtonInput> <Contexts>*ANY*, STATS_CanScroll</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT10</hButtonHash>	<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*ALL*,DISPLAY_CAN_JOIN</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
			
		</Item>
		
		
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_STATS_LISTITEM</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns SF_NoMenuAdvance</Flags>
			<ScrollBarFlags>Width_2 ManualUpdate InitiallyInvisible</ScrollBarFlags>
		</Item>

		<!--  ACTUAL SCREEN ID: 17 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_MISSION_CREATOR</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_MISSIONCREATOR</cGfxFilename>
			<runtime>
				<type>SCRIPT</type>
				<params>
					<data key="path">PauseMenu_Multiplayer</data>
					<data key="continual">TRUE</data>
				</params>
			</runtime>
			<depth>1</depth>
			<!--<ScrollBarFlags>Width_2 Offset_1Right Arrows_UpDown ManualUpdate InitiallyInvisible</ScrollBarFlags>-->

			<Flags>Sound_NoAccept Input_NoBackIfNavigating EnterMenuOnMouseClick</Flags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CREWS</MenuUniqueId> </Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_PLAYERS</MenuUniqueId> <Contexts>*ALL*,InMP</Contexts></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_FRIENDS</MenuUniqueId> <Contexts>*ALL*,InMP</Contexts></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_PARTY</MenuUniqueId> <Contexts>*ALL*,InMP</Contexts></Item>
				
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_WEAPONS</MenuUniqueId> <Contexts>*ALL*,InMP</Contexts></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_MEDALS</MenuUniqueId> <Contexts>*ALL*,InMP</Contexts></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_PARTY_LIST</MenuUniqueId> <Contexts>*ALL*,InMP</Contexts></Item>

				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
			</MenuItems>
			<ButtonList>
				<ButtonPrompts>
					
					<!--<CMenuButton> <hButtonHash>FE_HLP5</hButtonHash>		<RawButtonIcon>ARROW_UPDOWN</RawButtonIcon> 	  <Contexts>*ANY*,SINGLE_SCREEN,NAVIGATING_CONTENT</Contexts></CMenuButton>-->
					<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash>		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*NONE*,EditPlaylistDrop,EditPlaylistPick,SupressSelectPM</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> </CMenuButton>
					
					<CMenuButton> <hButtonHash>PM_PL_CHALANGE</hButtonHash> <ButtonInput>INPUT_FRONTEND_SELECT</ButtonInput> <Contexts>*ALL*,SetCrewChallenge</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_DELETECAP</hButtonHash> <ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,DeletePlaylist</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_PICKCAP</hButtonHash> <ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*ALL*,EditPlaylistPick</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_DROPCAP</hButtonHash> <ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*ALL*,EditPlaylistDrop</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_EDITCAP</hButtonHash> <ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*ALL*,EditPlaylist</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_START_H2H</hButtonHash> <ButtonInput>INPUT_FRONTEND_RS</ButtonInput> <Contexts>*ALL*,StartHeadToHead</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_SCROLL</hButtonHash> <ButtonInput>INPUT_FRONTEND_RIGHT_AXIS_Y</ButtonInput> <Contexts>*ALL*,RScrollUpDown</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>FMMC_ADDBK</hButtonHash> <ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,BookmarkJob</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_REM_BM</hButtonHash> <ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,RemoveBookmark</Contexts> </CMenuButton>
					
					<CMenuButton> <hButtonHash>PM_N_PAGE</hButtonHash> <ButtonInput>INPUT_FRONTEND_RIGHT</ButtonInput> <Contexts>*ALL*,PageRight</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_P_PAGE</hButtonHash> <ButtonInput>INPUT_FRONTEND_LEFT</ButtonInput> <Contexts>*ALL*,PageLeft</Contexts> </CMenuButton>
					
					<CMenuButton> <hButtonHash>PM_CH_BOOKM</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToBookmarkedChallenges</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_CH_CREW</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToCrewChallenges</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_CH_MINE</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToMyChallenges</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_CH_TOP</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToTopChallenges</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_CH_RECENT</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToRecentChallenges</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_CH_ROCKSTAR</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToRockstarChallenges</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_CH_FRIEND</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToFriendChallenges</Contexts> </CMenuButton>
					
					<CMenuButton> <hButtonHash>PM_DM_ALL</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToAllDeathmatches</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_DM_FFA</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToDeathmatches</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_DM_TEAM</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToTeamDeathmatches</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_DM_VEH</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToVehicleDeathmatches</Contexts> </CMenuButton>
					
					<CMenuButton> <hButtonHash>PM_RC_ALL</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToAllRaces</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_RC_LAND</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToLandRaces</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_RC_BIKE</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToBikeRaces</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_RC_AIR</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToAirRaces</Contexts> </CMenuButton>	
					<CMenuButton> <hButtonHash>PM_RC_BOAT</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToWaterRaces</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_RC_STUNT</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToStuntRaces</Contexts> </CMenuButton>

					<CMenuButton> <hButtonHash>PM_CAP_ALL</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToAllCaptures</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_CAP_GTA</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToGTACaptures</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_CAP_HOLD</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToHoldCaptures</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_CAP_RAID</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToRaidCaptures</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_CAP_CONT</hButtonHash> <ButtonInput>INPUT_FRONTEND_LS</ButtonInput> <Contexts>*ALL*,CycleToContendCaptures</Contexts> </CMenuButton>	

				</ButtonPrompts>
			</ButtonList>
			
		</Item>
		
		<!--  ACTUAL SCREEN ID: 18 -->
		<!--
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_SOCIALCLUB</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_PLACEHOLDER</cGfxFilename>
			<depth>1</depth>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_PLACEHOLDER</MenuAction> <cPlaceholderString>Placeholder menu - Press A/Cross to sign into Social Club</cPlaceholderString> </Item>
			</MenuItems>
		</Item>
		
		-->

		<!--  ACTUAL SCREEN ID: 19 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_GALLERY</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_GALLERY</cGfxFilename>
			<runtime>
				<type>GALLERY</type>
			</runtime>
			<depth>1</depth>
			<MenuItems>
				<Item>
					<MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction>
				</Item>
			</MenuItems>
		
			<ButtonList>
				<ButtonPrompts>
					<!-- navigating internally -->
					<!--<CMenuButton> <hButtonHash>IB_NAVIGATE</hButtonHash>	<RawButtonIcon>ARROW_ALL</RawButtonIcon>		<Contexts>*NONE*, GALLERY_MAXIMIZE, GALLERY_EMPTY , *ALL*,NAVIGATING_CONTENT,GALLERY_THUMBNAIL</Contexts></CMenuButton>-->
					<!--<CMenuButton> <hButtonHash>IB_NAVIGATE</hButtonHash>  <RawButtonIcon>ARROW_LEFTRIGHT</RawButtonIcon>	<Contexts>*NONE*, GALLERY_THUMBNAIL, GALLERY_EMPTY, *ALL*,NAVIGATING_CONTENT,GALLERY_MAXIMIZE</Contexts></CMenuButton>-->
					
					<!-- In multiple modes -->
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> <Contexts>*NONE*,GALLERY_MAXIMIZE,*ALL*,NAVIGATING_CONTENT,*ANY*,GALLERY_THUMBNAIL,GALLERY_PLACE_TEXT,GALLERY_REVIEW_TEXT,GALLERY_PROFANITY_CHECK</Contexts> </CMenuButton>
					
					<!-- Thumbnail mode -->
					<CMenuButton> <hButtonHash>IB_MAXIMIZE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*NONE*, GALLERY_IMAGE_CORRUPTION, GALLERY_EMPTY, GALLERY_PLACE_TEXT, GALLERY_REVIEW_TEXT,GALLERY_FINALIZE_TEXT, *ALL*,NAVIGATING_CONTENT,GALLERY_THUMBNAIL</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_DELETE</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*NONE*, GALLERY_EMPTY, GALLERY_DISABLE_DELETE, GALLERY_PLACE_TEXT, GALLERY_REVIEW_TEXT,GALLERY_FINALIZE_TEXT, *ALL*,NAVIGATING_CONTENT,GALLERY_THUMBNAIL</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_UPLOAD</hButtonHash>		<ButtonInput>INPUT_FRONTEND_RS</ButtonInput> <Contexts>*NONE*, GALLERY_IMAGE_CORRUPTION, GALLERY_EMPTY, GALLERY_DISABLE_UPLOAD, GALLERY_PLACE_TEXT, GALLERY_REVIEW_TEXT,GALLERY_FINALIZE_TEXT, *ALL*,NAVIGATING_CONTENT,GALLERY_THUMBNAIL</Contexts></CMenuButton>
					
          <CMenuButton> <hButtonHash>IB_WAYPOINT</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*, GALLERY_IMAGE_CORRUPTION, GALLERY_EMPTY,
					GALLERY_MAXIMIZE, GALLERY_PLACE_TEXT, GALLERY_REVIEW_TEXT,GALLERY_FINALIZE_TEXT,GALLERY_DISABLE_WAYPOINT,*ALL*,NAVIGATING_CONTENT,GALLERY_THUMBNAIL</Contexts></CMenuButton>
				
					<!-- Maximized mode -->
					<CMenuButton> <hButtonHash>IB_FACEBOOK</hButtonHash>	<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*NONE*,GALLERY_DENY_FACEBOOK, GALLERY_EMPTY, GALLERY_PLACE_TEXT, GALLERY_REVIEW_TEXT,GALLERY_FINALIZE_TEXT, *ALL*,NAVIGATING_CONTENT,GALLERY_MAXIMIZE</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_THUMBNAIL</hButtonHash>	<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> <Contexts>*NONE*,GALLERY_PLACE_TEXT, GALLERY_REVIEW_TEXT,GALLERY_FINALIZE_TEXT,*ALL*,NAVIGATING_CONTENT,GALLERY_MAXIMIZE</Contexts> </CMenuButton>			
					<CMenuButton> <hButtonHash>IB_RENAME</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*NONE*,GALLERY_PLACE_TEXT, GALLERY_REVIEW_TEXT,GALLERY_FINALIZE_TEXT,*ALL*,NAVIGATING_CONTENT,GALLERY_MAXIMIZE</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_ADD_TEXT</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,GALLERY_NO_MEME,GALLERY_PLACE_TEXT, GALLERY_REVIEW_TEXT,GALLERY_FINALIZE_TEXT,*ALL*,NAVIGATING_CONTENT,GALLERY_MAXIMIZE</Contexts></CMenuButton>
					
					<!-- Meme Editor - Text Placement Mode -->
					<CMenuButton> <hButtonHash>IB_PLACE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*ALL*,GALLERY_PLACE_TEXT</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_RESIZE</hButtonHash>	<ButtonInputGroup>INPUTGROUP_FRONTEND_TRIGGERS</ButtonInputGroup> <Contexts>*ALL*,GALLERY_PLACE_TEXT</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_COLOR</hButtonHash>	<ButtonInputGroup>INPUTGROUP_FRONTEND_DPAD_LR</ButtonInputGroup> <Contexts>*ALL*,GALLERY_PLACE_TEXT</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_FONT</hButtonHash>	<ButtonInputGroup>INPUTGROUP_FRONTEND_DPAD_UD</ButtonInputGroup> <Contexts>*ALL*,GALLERY_PLACE_TEXT,GALLERY_ALLOW_FONT_EDITS</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_MOVE</hButtonHash>	<ButtonInputGroup>INPUTGROUP_FRONTEND_LSTICK_ALL</ButtonInputGroup> <Contexts>*ALL*,GALLERY_PLACE_TEXT</Contexts> </CMenuButton>
					
					<!-- Meme review mode -->
					<CMenuButton> <hButtonHash>IB_SAVE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*ALL*,GALLERY_REVIEW_TEXT,*NONE*,GALLERY_PLACE_TEXT</Contexts> </CMenuButton>
					
					
				</ButtonPrompts>
			</ButtonList>
		</Item>

		<!--  ACTUAL SCREEN ID: 20 -->
		
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_GAME</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_GAME</cGfxFilename>
			<runtime>
				<type>SCRIPT</type>
				<params>
					<data key="path">PauseMenu_SP_Repeat</data>
				</params>
			</runtime>
			<Flags>LayoutChangedOnBack EnterMenuOnMouseClick</Flags>
			<depth>1</depth>
			<MenuItems>
				<Item> <cTextId>PM_REPEAT</cTextId>		<MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_REPLAY_MISSION</MenuUniqueId> <Contexts>*ANY*,InSP</Contexts> <!--<Contexts>HAS_DONE_MISSIONS</Contexts>--></Item>
				<Item> <cTextId>PM_REPEATRC</cTextId>	<MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_REPLAY_RANDOM</MenuUniqueId> <Contexts>*ANY*,InSP</Contexts> <!--<Contexts>HAS_DONE_MISSIONS</Contexts>--></Item>
				<Item> <cTextId>PM_PANE_LOA</cTextId>	<MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_LOAD_GAME</MenuUniqueId> <Contexts>*ANY*,InSP</Contexts> </Item>
				<Item> <cTextId>PM_PANE_NEW</cTextId>	<MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_NEW_GAME</MenuUniqueId> <Contexts>*ANY*,InSP</Contexts> </Item>
				<Item> <cTextId>PM_PANE_PROC</cTextId>	<MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_PROCESS_SAVEGAME</MenuUniqueId> <Contexts>*ALL*, ALLOW_PROCESS_SAVEGAME</Contexts> </Item>
				<Item> <cTextId>PM_PANE_IMP</cTextId>	<MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_IMPORT_SAVEGAME</MenuUniqueId> <Contexts>*ALL*, ALLOW_IMPORT_SAVEGAME</Contexts> </Item>
				<Item> <cTextId>PM_PANE_LEAVE</cTextId> <MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SHOW_ACCOUNT_PICKER</MenuUniqueId> <Contexts>*ALL*, x64</Contexts></Item>
				<Item> <cTextId>UI_FLOW_SP_L_M</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_LEGAL</MenuUniqueId> <Contexts>*ALL*, LEGAL_ENABLED, *NONE*,Creator,CREDITS_ENABLED</Contexts> </Item>
				
				<Item> <cTextId>UI_FLOW_SP_C_M</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CREDITS</MenuUniqueId> <Contexts>*ALL*, CREDITS_ENABLED, *NONE*,Creator,LEGAL_ENABLED</Contexts> </Item>
				<!--<Item> <cTextId>UI_FLOW_SP_CL_M</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CREDITS_LEGAL</MenuUniqueId> <Contexts>*ALL*, CREDITS_ENABLED, LEGAL_ENABLED, *NONE*,Creator</Contexts> </Item>-->
				<Item> <cTextId>PM_PANE_QUIT</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_EXIT_TO_WINDOWS</MenuUniqueId> <Contexts>*ALL*, x64</Contexts></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>

			<ButtonList>
				<ButtonPrompts  IncludeDefaults="false">
					<CMenuButton> <hButtonHash>HUD_INPUT1C</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_BUMPERS</ButtonInputGroup> 	<Contexts>*NONE*,TABS_ARE_COLUMNS,NAVIGATING_CONTENT,MENU_OFF</Contexts>				</CMenuButton>
					<CMenuButton> <hButtonHash>IB_HIDEMENU</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> 						<Contexts>*ALL*,CAN_TOGGLE_MENU,		 *NONE*,MENU_OFF, NAVIGATING_CONTENT</Contexts> </CMenuButton>
					<!--<CMenuButton> <hButtonHash>IB_SOCIALCLUB_UI</hButtonHash>	<ButtonInput>INPUT_FRONTEND_SELECT</ButtonInput> 						<Contexts>*ALL*,x64,		 *NONE*,MENU_OFF, NAVIGATING_CONTENT</Contexts> </CMenuButton>-->
					<CMenuButton> <hButtonHash>IB_SHOWMENU</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> 						<Contexts>*ALL*,CAN_TOGGLE_MENU,MENU_OFF,*NONE*,		  NAVIGATING_CONTENT</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> 					<Contexts>*NONE*,MENU_OFF,HIDE_ACCEPTBUTTON,HasContextMenu,NO_SAVEGAMES</Contexts> 					</CMenuButton>
					<CMenuButton> <hButtonHash>IB_OPTIONS</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> 					<Contexts>*NONE*,MENU_OFF,HIDE_ACCEPTBUTTON,*ALL*,HasContextMenu</Contexts> 			</CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput>  																											</CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT10</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> 						<Contexts>*ALL*,DISPLAY_CAN_JOIN</Contexts> 											</CMenuButton>
					<CMenuButton> <hButtonHash>PM_DYNAMICSTRING</hButtonHash>	<RawButtonIcon>NOTHING</RawButtonIcon>								<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_DISPLAY_TIMER</Contexts> 				</CMenuButton>
			
					<CMenuButton> <hButtonHash>IB_SELDEV</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,XBOX360,SELECT_STORAGE_DEVICE</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_DELSAVE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,DURANGO,DELETE_SAVEGAME,*NONE*,NO_SAVEGAMES</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_DELSAVE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,PS4,DELETE_SAVEGAME,*NONE*,NO_SAVEGAMES</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_DELSAVE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,x64,DELETE_SAVEGAME,*NONE*,NO_SAVEGAMES</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>

		</Item>
		
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_REPLAY_MISSION</MenuScreen>
			<depth>2</depth>
			<ScrollBarFlags>Width_1 ManualUpdate Align_Right InitiallyInvisible</ScrollBarFlags>
			<Flags>SF_NoClearRootColumns</Flags>
		</Item>
		
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_REPLAY_RANDOM</MenuScreen>
			<depth>2</depth>
			<ScrollBarFlags>Width_1 ManualUpdate Align_Right InitiallyInvisible</ScrollBarFlags>
			<Flags>SF_NoClearRootColumns</Flags>
		</Item>
		
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_REPLAY_MISSION_LIST</MenuScreen>
			<depth>3</depth>
			<Flags>SF_NoClearRootColumns SF_NoMenuAdvance</Flags>	
		</Item>
		
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_REPLAY_MISSION_ACTIVITY</MenuScreen>
			<depth>4</depth>
		</Item>


		<!--  ACTUAL SCREEN ID: 22 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_SAVE_GAME</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_SAVE</cGfxFilename>
			<runtime>
				<type>SAVEGAME</type>
			</runtime>
			<depth>1</depth>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
			
			<ButtonList>
				<ButtonPrompts  IncludeDefaults="true">
					<CMenuButton> <hButtonHash>IB_SELDEV</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,XBOX360,SELECT_STORAGE_DEVICE</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_DELSAVE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,DURANGO,DELETE_SAVEGAME</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_DELSAVE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,PS4,DELETE_SAVEGAME</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_DELSAVE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*ALL*,x64,DELETE_SAVEGAME</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
			
		</Item>
		
		<!--  ACTUAL SCREEN ID: 23 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_SETTINGS_AUDIO</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns</Flags>
			<ScrollBarFlags>Width_2 ManualUpdate InitiallyInvisible</ScrollBarFlags>
			<MenuItems>
				<Item> <cTextId>MO_SFX</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_SFX_VOLUME</MenuPref>		<MenuOption>MENU_OPTION_SLIDER</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_MUS</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MUSIC_VOLUME_IN_MP</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,InMP</Contexts></Item>
				<Item> <cTextId>MO_MUS</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MUSIC_VOLUME</MenuPref>		<MenuOption>MENU_OPTION_SLIDER</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*NONE*,InMP</Contexts></Item>
				<Item> <cTextId>MO_DBOOST</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>	<MenuPref>PREF_DIAG_BOOST</MenuPref>		<MenuOption>MENU_OPTION_SLIDER</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_RST</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_RADIO_STATION</MenuPref>		<MenuOption>MENU_OPTION_DISPLAY_RADIO_STATIONS</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*NONE*,OnLandingPage</Contexts></Item>
        <Item>
          <cTextId>MO_UR_PLAYMODE</cTextId>
          <MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>
          <MenuPref>PREF_UR_PLAYMODE</MenuPref>
          <MenuOption>MENU_OPTION_DISPLAY_UR_PLAYMODE</MenuOption>
          <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId>
          <Contexts>*ALL*,x64</Contexts>
        </Item>
        
        <Item>
          <cTextId>MO_UR_AUTOSCAN</cTextId>
          <MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>
          <MenuPref>PREF_UR_AUTOSCAN</MenuPref>
          <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId>
          <MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>
          <Contexts>*ALL*,x64</Contexts>
        </Item>
        <Item>
          <cTextId>MO_UR_QUICKSCAN</cTextId>
          <MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>
          <MenuUniqueIdHash>UR_QUICKSCAN</MenuUniqueIdHash>
          <Contexts>*ALL*,x64,*NONE*,AUD_QSCANNING,AUD_FSCANNING</Contexts>
        </Item>
	      <Item>
          <cTextId>MO_UR_QUICKSCAN</cTextId>
          <MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>
          <MenuUniqueIdHash>UR_QUICKSCAN</MenuUniqueIdHash>
          <Contexts>*ALL*,x64,AUD_FSCANNING</Contexts>
	        <Flags>InitiallyDisabled</Flags>
        </Item>
        <Item>
          <cTextId>MO_UR_SCANINPROGRESS</cTextId>
          <MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>
          <Contexts>*ALL*,x64,AUD_QSCANNING</Contexts>
          <Flags>InitiallyDisabled</Flags>
        </Item>
        <Item>
          <cTextId>MO_UR_COMPLETESCAN</cTextId>
          <MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>
          <MenuUniqueIdHash>UR_COMPLETESCAN</MenuUniqueIdHash>
          <Contexts>*ALL*,x64,*NONE*,AUD_FSCANNING,AUD_QSCANNING</Contexts>
        </Item>
 	      <Item>
          <cTextId>MO_UR_COMPLETESCAN</cTextId>
          <MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>
          <MenuUniqueIdHash>UR_COMPLETESCAN</MenuUniqueIdHash>
          <Contexts>*ALL*,x64,AUD_QSCANNING</Contexts>
	        <Flags>InitiallyDisabled</Flags>
        </Item>
        <Item>
          <cTextId>MO_UR_SCANINPROGRESS</cTextId>
          <MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>
          <Contexts>*ALL*,x64,AUD_FSCANNING</Contexts>
	        <Flags>InitiallyDisabled</Flags>
        </Item>

        <Item> <cTextId>MO_VOUT</cTextId>   <MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>   <MenuPref>PREF_VOICE_OUTPUT</MenuPref>    <MenuOption>MENU_OPTION_DISPLAY_VOICE_OUTPUT</MenuOption>     <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*NONE*,x64</Contexts></Item>
        <Item> <cTextId>MO_VOICESPKR</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VOICE_SPEAKERS</MenuPref>	<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,PS3</Contexts></Item>
				<Item> <cTextId>MO_OUTP</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_SPEAKER_OUTPUT</MenuPref>	<MenuOption>MENU_OPTION_DISPLAY_SPEAKER_OUTPUT</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*NONE*,AUD_WIRELESSHEADSET</Contexts></Item>
				<Item> <cTextId>MO_CTRLSPKR</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_CTRL_SPEAKER</MenuPref>	<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,PS4,PADSPEAKER_ENABLED,*NONE*,HEADPHONE_ENABLED</Contexts></Item>
				<Item> <cTextId>MO_CTRLSPKR</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_CTRL_SPEAKER_HEADPHONE</MenuPref>	<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,PS4,PADSPEAKER_ENABLED,HEADPHONE_ENABLED</Contexts></Item>
				<Item> <cTextId>MO_CTRLSPKR_VOL</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_CTRL_SPEAKER_VOL</MenuPref>		<MenuOption>MENU_OPTION_SLIDER</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,PS4,PADSPEAKER_ENABLED</Contexts></Item>
				<Item> <cTextId>MO_PULSEHEADSET</cTextId>  <MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction> <MenuPref>PREF_PULSE_HEADSET</MenuPref><MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption><Contexts>*ALL*,PS4,HEADPHONE_ENABLED</Contexts></Item>			
				<Item> <cTextId>MO_SS_FRONT</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_SS_FRONT</MenuPref>			<MenuOption>MENU_OPTION_DISPLAY_SS_FRONTREAR</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,SS_SUPPORTED,*NONE*,AUD_WIRELESSHEADSET</Contexts> </Item>
				<Item> <cTextId>MO_SS_REAR</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_SS_REAR</MenuPref>			<MenuOption>MENU_OPTION_DISPLAY_SS_FRONTREAR</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,SS_SUPPORTED,*NONE*,AUD_WIRELESSHEADSET</Contexts> </Item>
				<Item> <cTextId>MO_AUDIO_FOCUS</cTextId> <MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction> <MenuPref>PREF_AUDIO_MUTE_ON_FOCUS_LOSS</MenuPref>
				<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId>  
				<Contexts>*ALL*,x64</Contexts> </Item>
				<Item> <cTextId>MO_RDF</cTextId> <MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction> <MenuUniqueIdHash>RESTORE_DEFAULTS_AUDIO</MenuUniqueIdHash> 
				</Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: 24 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_SETTINGS_DISPLAY</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns</Flags>
			<ScrollBarFlags>Width_2 ManualUpdate InitiallyInvisible</ScrollBarFlags>
			<MenuItems>
				<Item> <cTextId>MO_RAD</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_RADAR_MODE</MenuPref> 		<MenuOption>MENU_OPTION_DISPLAY_MINIMAP_MODE</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_HUD</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_DISPLAY_HUD</MenuPref> 		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_HUD_TARG</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_RETICULE</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_RETICLE_MODE</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_RET_SIZE</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_RETICULE_SIZE</MenuPref> 	<MenuOption>MENU_OPTION_SLIDER</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_DISPGPS</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_DISPLAY_GPS</MenuPref> 		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SETTING_11</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_BIG_RADAR</MenuPref> 		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId>  <Contexts>*ALL*,InMP</Contexts> </Item>
				<Item> <cTextId>PM_SETTING_12</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_BIG_RADAR_NAMES</MenuPref> 	<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId>  <Contexts>*ALL*,InMP</Contexts> </Item>
				<Item> <cTextId>MO_TEXT_CHAT</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_SHOW_TEXT_CHAT</MenuPref> 	<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId>  <Contexts>*ALL*,InMP,b2699</Contexts> </Item>
				<Item> <cTextId>MO_BRI</cTextId>		<MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>			<MenuPref>PREF_GAMMA</MenuPref>				<MenuUniqueId>MENU_UNIQUE_ID_BRIGHTNESS_CALIBRATION</MenuUniqueId> </Item>
				<Item platform="!ps4"> <cTextId>MO_SAFEZONE</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_SAFEZONE_SIZE</MenuPref> 	<MenuOption>MENU_OPTION_SLIDER</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item platform="ps4"> <cTextId>MO_SAFEZONE</cTextId>	<MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>		<MenuUniqueIdHash>PREF_SAFEZONE_SIZE</MenuUniqueIdHash> </Item>
				<Item> <cTextId>MO_DOF</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_DOF</MenuPref> 				<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*NONE*,x64</Contexts> </Item>
				<Item> <cTextId>MO_SKFX</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_SKFX</MenuPref> 				<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_SUB</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_SUBTITLES</MenuPref> 		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>MO_MEASUREMENT</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MEASUREMENT_SYSTEM</MenuPref> 		<MenuOption>MENU_OPTION_DISPLAY_MEASUREMENT</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_LAN</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_CURRENT_LANGUAGE</MenuPref> 	<MenuOption>MENU_OPTION_DISPLAY_LANGUAGE</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId></Item>
				<Item> <cTextId>MO_RDF</cTextId>		<MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>			<MenuUniqueIdHash>RESTORE_DEFAULTS_DISPLAY</MenuUniqueIdHash> </Item>
			</MenuItems>
		</Item>
		
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_SETTINGS_GRAPHICS</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns</Flags>
			<ScrollBarFlags>Width_2</ScrollBarFlags>
			<MenuItems>
				<Item> <cTextId>GFXVID_OVERRIDE</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_VID_OVERRIDE</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>GFX_DXVERSION</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_DXVERSION</MenuPref>				<MenuOption>MENU_OPTION_GFX_DXVERSION</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				
				<Item> <MenuAction>MENU_OPTION_ACTION_SEPARATOR</MenuAction> </Item>

				<Item> <cTextId>GFX_SCALING</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_SCALING</MenuPref> 				<MenuOption>MENU_OPTION_GFX_SCALING</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>				
				<!--<Item> <cTextId>VID_ADAPTER</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VID_ADAPTER</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>-->
				<Item> <cTextId>VID_MONITOR</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VID_MONITOR</MenuPref> 				<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>				
								
				<Item> <MenuAction>MENU_OPTION_ACTION_SEPARATOR</MenuAction> </Item>

				<Item> <cTextId>GFX_FXAA</cTextId>			<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_FXAA</MenuPref> 					<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>GFX_MSAA</cTextId>			<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_MSAA</MenuPref> 					<MenuOption>MENU_OPTION_GFX_MSAA</MenuOption>			<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>

		<Item> <cTextId>GFX_TXAA</cTextId>			<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_TXAA</MenuPref> 					<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,GFX_SUPPORT_TXAA</Contexts> </Item>
				<Item> <cTextId>VID_VSYNC</cTextId>			<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VID_VSYNC</MenuPref> 				<MenuOption>MENU_OPTION_VID_VSYNC</MenuOption>			<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_STER</cTextId>			<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VID_STEREO</MenuPref>				<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ANY*,VID_STEREO</Contexts></Item>
				<!--<Item> <cTextId>STE_SEPAR</cTextId>			<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VID_STEREO_SEPARATION</MenuPref>		<MenuOption>MENU_OPTION_SLIDER</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ANY*,VID_STEREO</Contexts></Item>-->
				<Item> <cTextId>VID_FOCUS</cTextId>			<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VID_PAUSE_ON_FOCUS_LOSS</MenuPref> 	<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>			<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>

				<Item>  <MenuAction>MENU_OPTION_ACTION_SEPARATOR</MenuAction> </Item>

				<Item> <cTextId>GFX_DENSITY</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_CITY_DENSITY</MenuPref>			<MenuOption>MENU_OPTION_SLIDER</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>GFX_POP_VARIETY</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction> <MenuPref>PREF_GFX_POP_VARIETY</MenuPref>       <MenuOption>MENU_OPTION_SLIDER</MenuOption>       <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>GFX_DIST_SCALE</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_DIST_SCALE</MenuPref>			<MenuOption>MENU_OPTION_SLIDER</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>

				<Item> <MenuAction>MENU_OPTION_ACTION_SEPARATOR</MenuAction> </Item>

				<!-- move this once the uniqueid is advanced gfx for the requirements -->
				<Item> <cTextId>GFX_BUDGET</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VID_STEREO_CONVERGENCE</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>GFX_TXTQ</cTextId>			<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_TEXTURE_QUALITY</MenuPref> 		<MenuOption>MENU_OPTION_GFX_NIGHTLIGHTS</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>GFX_SHADERQ</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_SHADER_QUALITY</MenuPref> 		<MenuOption>MENU_OPTION_GFX_SHADERQ</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
		<!--Changed to allow Shadows=OFF-->
		<!--<Item> <cTextId>GFX_SHADOWQ</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_SHADOW_QUALITY</MenuPref> 		<MenuOption>MENU_OPTION_GFX_NIGHTLIGHTS</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>-->
				<Item> <cTextId>GFX_SHADOWQ</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_SHADOW_QUALITY</MenuPref> 		<MenuOption>MENU_OPTION_GFX_SHADOWS</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>GFX_REFELECTIONQ</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_REFLECTION_QUALITY</MenuPref>	<MenuOption>MENU_OPTION_GFX_REFLECTION</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>GFX_REFLECTION_MSAA</cTextId><MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>	<MenuPref>PREF_GFX_REFLECTION_MSAA</MenuPref>		<MenuOption>MENU_OPTION_GFX_MSAA</MenuOption>			<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>GFX_WATERQ</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_WATER_QUALITY</MenuPref> 		<MenuOption>MENU_OPTION_GFX_SHADERQ</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>GFX_PRTQ</cTextId>			<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_PARTICLES_QUALITY</MenuPref> 		<MenuOption>MENU_OPTION_GFX_NIGHTLIGHTS</MenuOption><MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>GFX_GRASSQ</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_GRASS_QUALITY</MenuPref>				<MenuOption>MENU_OPTION_GFX_QUALITY</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <!--<Item> <cTextId>GFX_NIGHTLIGHTS</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_NIGHT_LIGHTS</MenuPref>			<MenuOption>MENU_OPTION_GFX_SHADOWS</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>-->
				<Item> <cTextId>GFX_SOFT_SHADOW</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_SHADOW_SOFTNESS</MenuPref>		<MenuOption>MENU_OPTION_GFX_SOFTNESS</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>GFX_POST_FX</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_POST_FX</MenuPref>				<MenuOption>MENU_OPTION_GFX_QUALITY</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>GFX_MB_STR</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_MB_STRENGTH</MenuPref>			<MenuOption>MENU_OPTION_SLIDER</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_DOF</cTextId>			<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_DOF</MenuPref> 					<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>GFX_ANISOT</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_ANISOTROPIC_FILTERING</MenuPref> <MenuOption>MENU_OPTION_GFX_ANISOT</MenuOption>			<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>GFX_AMBIENTOCC</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_AMBIENT_OCCLUSION</MenuPref> 	<MenuOption>MENU_OPTION_GFX_AMBIENTOCC</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>GFX_TESSELLATION</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_GFX_TESSELLATION</MenuPref> 			<MenuOption>MENU_OPTION_GFX_SHADOWS</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				
				<Item> <MenuAction>MENU_OPTION_ACTION_SEPARATOR</MenuAction> </Item>
				
				<Item> <cTextId>MO_RDF</cTextId>			<MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>			<MenuUniqueIdHash>RESTORE_DEFAULTS_GRAPHICS</MenuUniqueIdHash> </Item>

			</MenuItems>
		</Item>
    
    <Item>
			<MenuScreen>MENU_UNIQUE_ID_SETTINGS_ADVANCED_GFX</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns</Flags>
			<ScrollBarFlags>Width_2 ManualUpdate InitiallyInvisible</ScrollBarFlags>
			<MenuItems>
		<Item> <cTextId>VID_SCR_TYPE</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VID_SCREEN_TYPE</MenuPref> 			<MenuOption>MENU_OPTION_SCREEN_TYPE</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
		<Item> <cTextId>VID_RESOLUTION</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VID_RESOLUTION</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
		<Item> <cTextId>VID_REFRESH</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VID_REFRESH</MenuPref> 				<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
		<Item> <cTextId>VID_ASPECT</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VID_ASPECT</MenuPref> 				<MenuOption>MENU_OPTION_VID_ASPECT</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>

        <Item> <cTextId>AGFX_WARNING</cTextId> <MenuAction>MENU_OPTION_ACTION_SEPARATOR</MenuAction> </Item>        
		<Item> <cTextId>AGFX_LSHADOW</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_ADV_GFX_LONG_SHADOWS</MenuPref>				<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>AGFX_USHADOW</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_ADV_GFX_ULTRA_SHADOWS</MenuPref>				<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
		<Item> <cTextId>AGFX_HD_FLIGHT</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_ADV_GFX_HD_FLIGHT</MenuPref>				<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>AGFX_MAXLOD</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_ADV_GFX_MAX_LOD</MenuPref>				<MenuOption>MENU_OPTION_SLIDER</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>AGFX_SHADOWS_DIST</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_ADV_GFX_SHADOWS_DIST_MULT</MenuPref>				<MenuOption>MENU_OPTION_SLIDER</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				
				<Item> <cTextId>MO_RDF</cTextId>			<MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>			<MenuUniqueIdHash>RESTORE_DEFAULTS_ADVANCED_GFX</MenuUniqueIdHash> </Item>
			</MenuItems>
		</Item>

		<Item>
			<MenuScreen>MENU_UNIQUE_ID_SETTINGS_REPLAY</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns</Flags>
			<ScrollBarFlags>Width_2 ManualUpdate InitiallyInvisible</ScrollBarFlags>
			<MenuItems>
				<!--<Item> <cTextId>PM_PANE_RPL_MODE</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_REPLAY_MODE</MenuPref>			<MenuOption>MENU_OPTION_REPLAY_MODE</MenuOption>		 <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>-->
				<Item> <cTextId>PM_PANE_RPL_MEM_LIMIT</cTextId>				<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_REPLAY_MEM_LIMIT</MenuPref>					<MenuOption>MENU_OPTION_REPLAY_MEM_LIMIT</MenuOption>	 				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,x64</Contexts></Item>
        		<!--<Item> <cTextId>PM_PANE_RPL_AUTO_RESUME_RECORDING</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_REPLAY_AUTO_RESUME_RECORDING</MenuPref>		<MenuOption>MENU_OPTION_REPLAY_AUTO_RESUME_RECORDING</MenuOption>	 	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>-->
        		<Item> <cTextId>PM_PANE_RPL_AUTO_SAVE_RECORDING</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_REPLAY_AUTO_SAVE_RECORDING</MenuPref>		<MenuOption>MENU_OPTION_REPLAY_AUTO_SAVE_RECORDING</MenuOption>	 	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>PM_PANE_RPL_UPLOAD_PRIVACY</cTextId>				<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VIDEO_UPLOAD_PRIVACY</MenuPref>					<MenuOption>MENU_OPTION_VIDEO_UPLOAD_PRIVACY</MenuOption>	 				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>REDITOR_TOOLTIP</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_ROCKSTAR_EDITOR_TOOLTIP</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>REDITOR_EXPORT_GRAPHICS_UPGRADE</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_ROCKSTAR_EDITOR_EXPORT_GRAPHICS_UPGRADE</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,x64</Contexts>  </Item>
				
			</MenuItems>
		</Item>
		
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_SETTINGS_VOICE_CHAT</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns</Flags>
			<ScrollBarFlags>Width_2</ScrollBarFlags>
			<MenuItems>
				<Item> <cTextId>MO_VOICE_ENABLE</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VOICE_ENABLE</MenuPref>			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_VOICE_LISTEN</cTextId> <MenuAction>MENU_OPTION_ACTION_SEPARATOR</MenuAction> </Item>
				<Item> <cTextId>MO_OUTP_DVC</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VOICE_OUTPUT_DEVICE</MenuPref>	<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_CHAT_OVOL</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VOICE_OUTPUT_VOLUME</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_SOUND_VOL</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VOICE_SOUND_VOLUME</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_MUSIC_VOL</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VOICE_MUSIC_VOLUME</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_VOICE_TALK</cTextId> <MenuAction>MENU_OPTION_ACTION_SEPARATOR</MenuAction> </Item>
				<Item> <cTextId>MO_TLK_ENABLE</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VOICE_TALK_ENABLED</MenuPref>	<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>VOX_FEEDBACK</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VOICE_FEEDBACK</MenuPref>		<MenuOption>MENU_OPTION_VOICE_FEEDBACK</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Flags>InitiallyDisabled</Flags></Item>
				<Item> <cTextId>MO_INPUT_DVC</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VOICE_INPUT_DEVICE</MenuPref>	<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_VCHAT_MODE</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VOICE_CHAT_MODE</MenuPref>		<MenuOption>MENU_OPTION_DISPLAY_VOICE_TYPE</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_MIC_VOL</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VOICE_MIC_VOLUME</MenuPref>		<MenuOption>MENU_OPTION_SLIDER</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_MIC_SEN</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VOICE_MIC_SENSITIVITY</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_SEPARATOR</MenuAction> </Item>
				<Item> <cTextId>MO_RDF</cTextId>		<MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>			<MenuUniqueIdHash>RESTORE_DEFAULTS_VOICE_CHAT</MenuUniqueIdHash> </Item>
			</MenuItems>
		</Item>
    
    <!--  ACTUAL SCREEN ID: 25 -->
		<Item>
    <MenuScreen>MENU_UNIQUE_ID_SETTINGS_CONTROLS</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns</Flags>
			<ScrollBarFlags>Width_2</ScrollBarFlags>
			<MenuItems>
				<Item> <cTextId>MO_CTRL_CONTEXT</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_CONTROLS_CONTEXT</MenuPref> 		<MenuOption>MENU_OPTION_CONTROLS_CONTEXT</MenuOption>	 <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_TAR</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_TARGET_CONFIG</MenuPref> 		<MenuOption>MENU_OPTION_DISPLAY_TARGET_CONFIG</MenuOption>	 <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Flags>InitiallyDisabled</Flags> <Contexts>*ALL*,InMP</Contexts></Item>
				<Item> <cTextId>MO_TAR</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_TARGET_CONFIG</MenuPref> 		<MenuOption>MENU_OPTION_DISPLAY_TARGET_CONFIG</MenuOption>	 <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*NONE*,InMP</Contexts></Item>
				<Item> <cTextId>MO_SPRINT</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_HOLD_SPRINT</MenuPref> 		<MenuOption>MENU_OPTION_CONTROLS_HOLD_SPRINT</MenuOption>	 <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*NONE*,InSP,*ALL*,b2944</Contexts></Item>
				<Item> <cTextId>MO_VIB</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_VIBRATION</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_INV</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_INVERT_LOOK</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_INVERT_LOOK</MenuOption>   <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_CTY</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_CONTROL_CONFIG</MenuPref>		<MenuOption>MENU_OPTION_DISPLAY_CONTROL_CONFIG</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*NONE*,RemotePlay</Contexts></Item>
        <Item> <cTextId>MO_CTY_FPS</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_CONTROL_CONFIG_FPS</MenuPref>		<MenuOption>MENU_OPTION_DISPLAY_CONTROL_CONFIG</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*NONE*,RemotePlay</Contexts></Item>        
				<Item> <cTextId>MO_SENS</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_CONTROLLER_SENSITIVITY</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>MO_FPS_LSENS</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FPS_LOOK_SENSITIVITY</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>			<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_FPS_ASENS</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FPS_AIM_SENSITIVITY</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>			<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>MO_LK_SENS</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_LOOK_AROUND_SENSITIVITY</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_TPS_DZONE</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_AIM_DEADZONE</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>			<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>MO_FPS_DZONE</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FPS_AIM_DEADZONE</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>			<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_TPS_ACCEL</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_AIM_ACCELERATION</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>			<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>MO_FPS_ACCEL</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FPS_AIM_ACCELERATION</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>			<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_SNIPE</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_SNIPER_ZOOM</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<!--<Item> <cTextId>MO_CMS</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_CINEMATIC_SHOOTING</MenuPref> 		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*NONE*,InMP</Contexts> </Item>-->
				<Item> <cTextId>MO_DHB</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_ALTERNATE_HANDBRAKE</MenuPref> 		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*NONE*,RemotePlay</Contexts></Item>
				<Item> <cTextId>MO_ADB</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_ALTERNATE_DRIVEBY</MenuPref> 		<MenuOption>MENU_OPTION_DISPLAY_CONTROL_DRIVEBY</MenuOption>			<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*NONE*,RemotePlay</Contexts></Item>
				<Item> <cTextId>MO_LTE</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_CONTROLLER_LIGHT_EFFECT</MenuPref> 	<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,PS4</Contexts></Item>
				<Item> <cTextId>MO_GAMEPAD</cTextId>		<MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>		<MenuUniqueIdHash>PREF_PCGAMEPAD</MenuUniqueIdHash>		<Contexts>*ALL*,x64,*NONE*,OnLandingPage,InMP</Contexts> </Item>
				<Item> <cTextId>MO_RDF</cTextId>		<MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>			<MenuUniqueIdHash>RESTORE_DEFAULTS_CONTROLS</MenuUniqueIdHash> </Item>
			</MenuItems>
		</Item>
		
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_SETTINGS_MISC_CONTROLS</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns</Flags>
			<ScrollBarFlags>Width_2</ScrollBarFlags>
			<MenuItems>
				<Item> <cTextId>MO_MTYPE</cTextId>			<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MOUSE_TYPE</MenuPref>			<MenuOption>MENU_OPTION_MOUSE_TYPE</MenuOption>			<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_MONFSENS</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MOUSE_ON_FOOT_SCALE</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>MO_MOUSEWEIGHT</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MOUSE_WEIGHT_SCALE</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>MO_MOUSEACC</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MOUSE_ACCELERATION</MenuPref>		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_INVMOUSE</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_INVERT_MOUSE</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_INVERT_LOOK</MenuOption>   <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,x64</Contexts></Item>
				<Item> <cTextId>MO_KBM_TOGGLAIM</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_KBM_TOGGLE_AIM</MenuPref>		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item platform="pc_only"> <cTextId>MO_FPS_DEFAULT_AIM_TYPE</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FPS_DEFAULT_AIM_TYPE</MenuPref> 		<MenuOption>MENU_OPTION_FPS_DEFAULT_AIM_TYPE</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>				
				<Item> <cTextId>MO_MOUSEDRIVE</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MOUSE_DRIVE</MenuPref> 			<MenuOption>MENU_OPTION_CAM_VEH_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_MOUSEFLY</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MOUSE_FLY</MenuPref> 			<MenuOption>MENU_OPTION_CAM_VEH_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>MO_MOUSESUB</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MOUSE_SUB</MenuPref> 			<MenuOption>MENU_OPTION_CAM_VEH_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_MDRVSENS</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MOUSE_DRIVING_SCALE</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>MO_MPPILSENS</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MOUSE_PLANE_SCALE</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
		    <Item> <cTextId>MO_MHPILSEN</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MOUSE_HELI_SCALE</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
    		<Item> <cTextId>MO_MSUBSENS</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MOUSE_SUB_SCALE</MenuPref>	<MenuOption>MENU_OPTION_SLIDER</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_MOUSEFLYINV</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_INVERT_MOUSE_FLYING</MenuPref> 	<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>   <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,x64</Contexts></Item>
				<Item> <cTextId>MO_MOUSESUBINV</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_INVERT_MOUSE_SUB</MenuPref> 		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>   <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,x64</Contexts></Item>
				<Item> <cTextId>MO_MOUSEFLYSWAP</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_SWAP_ROLL_YAW_MOUSE_FLYING</MenuPref> 	<MenuOption>MENU_OPTION_ROLL_YAW_PITCH</MenuOption>   <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,x64</Contexts></Item>
        <Item> <cTextId>MO_ACMBIKE</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MOUSE_AUTOCENTER_BIKE</MenuPref>		<MenuOption>MENU_OPTION_SLIDER</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>MO_ACMCAR</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MOUSE_AUTOCENTER_CAR</MenuPref>		<MenuOption>MENU_OPTION_SLIDER</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>MO_ACMPLANE</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_MOUSE_AUTOCENTER_PLANE</MenuPref>		<MenuOption>MENU_OPTION_SLIDER</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_LTE</cTextId>			<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_CONTROLLER_LIGHT_EFFECT</MenuPref> 	<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,LightEffectHardware</Contexts></Item>
				<Item> <cTextId>MO_RDF</cTextId>			<MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>			<MenuUniqueIdHash>RESTORE_DEFAULTS_MISC_CONTROLS</MenuUniqueIdHash> </Item>
			</MenuItems>
		</Item>
    
    <Item>
			<MenuScreen>MENU_UNIQUE_ID_SETTINGS_FIRST_PERSON</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns</Flags>
			<ScrollBarFlags>Width_2 ManualUpdate InitiallyInvisible</ScrollBarFlags>
			<MenuItems>
        <!--Leaving this menu in as it may come back-->
        <Item> <cTextId>MO_RDF</cTextId>			<MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>			<MenuUniqueIdHash>RESTORE_DEFAULTS_FIRST_PERSON</MenuUniqueIdHash> </Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: 136 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_SETTINGS_CAMERA</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns</Flags>
      <ScrollBarFlags>Width_2 ManualUpdate InitiallyInvisible</ScrollBarFlags>
			<MenuItems>
        <Item> <cTextId>MO_FPS_TOGGLE</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FPS_PERSISTANT_VIEW</MenuPref>	<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>MO_CAMERAHEIGHT</cTextId> <MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>	<MenuPref>PREF_CAMERA_HEIGHT</MenuPref> 	<MenuOption>MENU_OPTION_CAMERA_HEIGHT</MenuOption>	<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>MO_FPS_ALVL</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FPS_AUTO_LEVEL</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_FPS_FOV</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FPS_FIELD_OF_VIEW</MenuPref>		<MenuOption>MENU_OPTION_SLIDER</MenuOption>			<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_FPS_RAG</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FPS_RAGDOLL</MenuPref>			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_FPS_ROLL</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FPS_COMBATROLL</MenuPref>		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_FPS_HDB</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FPS_HEADBOB</MenuPref>		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_FPS_TPC</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FPS_THIRD_PERSON_COVER</MenuPref>		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>MO_FPS_VAC</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FPS_VEH_AUTO_CENTER</MenuPref>		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>				<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
        <Item> <cTextId>MO_HOOD_CAMERA</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_HOOD_CAMERA</MenuPref>		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
		<Item> <cTextId>MO_FPS_REL_CAM_DRIVEBY</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FPS_RELATIVE_VEHICLE_CAMERA_DRIVEBY_AIMING</MenuPref>		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>MO_RDF</cTextId>		<MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction>			<MenuUniqueIdHash>RESTORE_DEFAULTS_CAMERA</MenuUniqueIdHash> </Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: 26 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_SETTINGS_SAVEGAME</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns</Flags>
			<ScrollBarFlags>Width_2 ManualUpdate InitiallyInvisible</ScrollBarFlags>
			<MenuItems>
				<Item> <cTextId>MO_AUTOSAV</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_AUTOSAVE</MenuPref> 		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>		<MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ANY*,InSP</Contexts> </Item>
			</MenuItems>
		</Item>
		
		<!-- -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_SETTINGS_FACEBOOK</MenuScreen>
			<Flags>SF_NoClearRootColumns</Flags>
			<ScrollBarFlags>Width_2 ManualUpdate InitiallyInvisible</ScrollBarFlags>
			<depth>2</depth>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
				<Item> <cTextId>FB_LINK</cTextId>		<MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction> 		<MenuUniqueIdHash>LINK_FACEBOOK</MenuUniqueIdHash> <Contexts>*ALL*,FB_NeedsSignUp</Contexts> </Item>
				<Item> <cTextId>FB_LINK</cTextId>		<MenuAction>MENU_OPTION_ACTION_TRIGGER</MenuAction> 		<MenuUniqueIdHash>LINK_FACEBOOK</MenuUniqueIdHash> <Contexts>*NONE*,FB_NeedsSignUp,FB_Ready</Contexts> <Flags>InitiallyDisabled</Flags></Item>
				<Item> <cTextId>MO_FACEBOOK</cTextId>	<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FACEBOOK_UPDATES</MenuPref> 		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> <Contexts>*ALL*,FB_Ready</Contexts> </Item>
				
			</MenuItems>
		</Item>
		

		<!--  ACTUAL SCREEN ID: 27 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HOME_MISSION</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoMenuAdvance SF_NoClearRootColumns Sound_NoAccept</Flags>
			<ScrollBarFlags>Width_2 InitiallyInvisible ManualUpdate</ScrollBarFlags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
		</Item>
		
		<!--  ACTUAL SCREEN ID: 27 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HOME_HELP</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoMenuAdvance SF_NoClearRootColumns Sound_NoAccept</Flags>
			<ScrollBarFlags>Width_2 InitiallyInvisible ManualUpdate</ScrollBarFlags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: 28 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HOME_BRIEF</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoMenuAdvance SF_NoClearRootColumns Sound_NoAccept</Flags>
			<ScrollBarFlags>Width_2 InitiallyInvisible ManualUpdate</ScrollBarFlags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: 29 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HOME_FEED</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoMenuAdvance SF_NoClearRootColumns Sound_NoAccept</Flags>
			<ScrollBarFlags>Width_2 InitiallyInvisible ManualUpdate</ScrollBarFlags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
		</Item>
		
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HOME_NEWSWIRE</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoMenuAdvance SF_NoClearRootColumns Sound_NoAccept</Flags>
			<ScrollBarFlags>Width_2 InitiallyInvisible ManualUpdate Align_Right</ScrollBarFlags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: 30 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_LOAD_GAME</MenuScreen>
			<depth>2</depth>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
			<Flags>SF_NoClearRootColumns</Flags>
		</Item>
		
		<!--  ACTUAL SCREEN ID: 31 -->
		<!--<Item handleButtons="true" >-->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_MP_CHARACTER_SELECT</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CHAR_SELECT</cGfxFilename>
			<depth>0</depth>
			<Flags>NotDimmable</Flags>
			<MenuItems>
				<Item> <cTextId>PM_SCR_NCH</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_MP_CHAR_1</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_NCH</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_MP_CHAR_2</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_NCH</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_MP_CHAR_3</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_NCH</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_MP_CHAR_4</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_NCH</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_MP_CHAR_5</MenuUniqueId> </Item>
			</MenuItems>
		</Item>
		
		<!--  ACTUAL SCREEN ID: 32 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_MP_CHAR_1</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CHAR_SELECT</cGfxFilename>
			<depth>1</depth>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
			</MenuItems>
			<Flags>AlwaysUseButtons</Flags>
			<ButtonList>
				<ButtonPrompts>
			
					<CMenuButton> <hButtonHash>HUD_RETURNSP</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 				</CMenuButton>
				
					<CMenuButton> <hButtonHash>HUD_INPUT2</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> 		<Contexts>*ALL*,IS_SELECTED_PED</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT95</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> 		<Contexts>*ALL*,CAN_RETRY_LOAD</Contexts></CMenuButton>
					
					
					<CMenuButton> <hButtonHash>HUD_INPUT9</hButtonHash>			<ButtonInput>INPUT_FRONTEND_DELETE</ButtonInput>		<Contexts>*ALL*,CAN_DELETE_PED</Contexts>	</CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT33</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> 		<Contexts>*ALL*,CAN_EDIT_TAB</Contexts>	</CMenuButton>
					<CMenuButton> <hButtonHash>IB_NAVIGATE</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_BUMPERS</ButtonInputGroup>	</CMenuButton>
					<CMenuButton> <hButtonHash>HUD_ENTERSCTV</hButtonHash> 		<ButtonInputGroup>INPUTGROUP_FRONTEND_STICKS</ButtonInputGroup> 		<Contexts>*ALL*,IS_RSTAR_DEV</Contexts>	</CMenuButton>
	
				</ButtonPrompts>
			</ButtonList>
				
		</Item>
		
		<!--  ACTUAL SCREEN ID: 33 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_MP_CHAR_2</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CHAR_SELECT</cGfxFilename>
			<depth>1</depth>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
			</MenuItems>
			<Flags>AlwaysUseButtons</Flags>
			<ButtonList>
				<ButtonPrompts>
			
					<CMenuButton> <hButtonHash>HUD_RETURNSP</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 				</CMenuButton>
		
					<CMenuButton> <hButtonHash>HUD_INPUT2</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> 		<Contexts>*ALL*,IS_SELECTED_PED</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT95</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> 		<Contexts>*ALL*,CAN_RETRY_LOAD</Contexts></CMenuButton>
		
					<CMenuButton> <hButtonHash>HUD_INPUT9</hButtonHash>			<ButtonInput>INPUT_FRONTEND_DELETE</ButtonInput>		<Contexts>*ALL*,CAN_DELETE_PED</Contexts>	</CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT33</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> 		<Contexts>*ALL*,CAN_EDIT_TAB</Contexts>	</CMenuButton>
					<CMenuButton> <hButtonHash>IB_NAVIGATE</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_BUMPERS</ButtonInputGroup>	</CMenuButton>
				</ButtonPrompts>
			</ButtonList>
				
		</Item>
		
		<!--  ACTUAL SCREEN ID: 34 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_MP_CHAR_3</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CHAR_SELECT</cGfxFilename>
			<depth>1</depth>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
			</MenuItems>
			<Flags>AlwaysUseButtons</Flags>
			<ButtonList>
				<ButtonPrompts>
					
					<CMenuButton> <hButtonHash>HUD_RETURNSP</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 				</CMenuButton>
	
				
					<CMenuButton> <hButtonHash>HUD_INPUT2</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> 		<Contexts>*ALL*,IS_SELECTED_PED</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT95</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> 		<Contexts>*ALL*,CAN_RETRY_LOAD</Contexts></CMenuButton>
					
					<CMenuButton> <hButtonHash>HUD_INPUT9</hButtonHash>			<ButtonInput>INPUT_FRONTEND_DELETE</ButtonInput>		<Contexts>*ALL*,CAN_DELETE_PED</Contexts>	</CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT33</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> 		<Contexts>*ALL*,CAN_EDIT_TAB</Contexts>	</CMenuButton>
					<CMenuButton> <hButtonHash>IB_NAVIGATE</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_BUMPERS</ButtonInputGroup>	</CMenuButton>
				</ButtonPrompts>
			</ButtonList>
			
		</Item>
		
		<!--  ACTUAL SCREEN ID: 35 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_MP_CHAR_4</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CHAR_SELECT</cGfxFilename>
			<depth>1</depth>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
			</MenuItems>
			<Flags>AlwaysUseButtons</Flags>
			<ButtonList>
				<ButtonPrompts>
				
					<CMenuButton> <hButtonHash>HUD_RETURNSP</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 				</CMenuButton>
	
					<CMenuButton> <hButtonHash>HUD_INPUT2</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> 		<Contexts>*ALL*,IS_SELECTED_PED</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT95</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> 		<Contexts>*ALL*,CAN_RETRY_LOAD</Contexts></CMenuButton>
					
					<CMenuButton> <hButtonHash>HUD_INPUT9</hButtonHash>			<ButtonInput>INPUT_FRONTEND_DELETE</ButtonInput>		<Contexts>*ALL*,CAN_DELETE_PED</Contexts>	</CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT33</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> 		<Contexts>*ALL*,CAN_EDIT_TAB</Contexts>	</CMenuButton>
					<CMenuButton> <hButtonHash>IB_NAVIGATE</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_BUMPERS</ButtonInputGroup>	</CMenuButton>
				</ButtonPrompts>
			</ButtonList>
			
		</Item>

		<!--  ACTUAL SCREEN ID: 36 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_MP_CHAR_5</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CHAR_SELECT</cGfxFilename>
			<depth>1</depth>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
			</MenuItems>
			<Flags>AlwaysUseButtons</Flags>
			<ButtonList>
				<ButtonPrompts>
			
					<CMenuButton> <hButtonHash>HUD_RETURNSP</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 				</CMenuButton>
	
					<CMenuButton> <hButtonHash>HUD_INPUT2</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> 		<Contexts>*ALL*,IS_SELECTED_PED</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT95</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> 		<Contexts>*ALL*,CAN_RETRY_LOAD</Contexts></CMenuButton>
					
					<CMenuButton> <hButtonHash>HUD_INPUT9</hButtonHash>			<ButtonInput>INPUT_FRONTEND_DELETE</ButtonInput>		<Contexts>*ALL*,CAN_DELETE_PED</Contexts>	</CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT33</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> 		<Contexts>*ALL*,CAN_EDIT_TAB</Contexts>	</CMenuButton>
					<CMenuButton> <hButtonHash>IB_NAVIGATE</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_BUMPERS</ButtonInputGroup>	</CMenuButton>
				</ButtonPrompts>
			</ButtonList>
			
		</Item>
		
		<!--  ACTUAL SCREEN ID: 37 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_MP_CHARACTER_CREATION</MenuScreen>
			<depth>0</depth>
			<Flags>Input_NoBack</Flags>
			<Flags>Sound_NoAccept</Flags>
			<MenuItems>
				<Item> <cTextId>CC_PM_OPT</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CREATION_HERITAGE</MenuUniqueId> </Item>
				<Item> <cTextId>CC_PM_DET</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CREATION_LIFESTYLE</MenuUniqueId> </Item>
				<Item> <cTextId>PM_SCR_CCY</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CREATION_YOU</MenuUniqueId> </Item>
			</MenuItems>
		</Item>
		
		<!--  ACTUAL SCREEN ID: 38 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CREATION_HERITAGE</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CHAR_MOM_DAD</cGfxFilename>
			<depth>1</depth>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
			</MenuItems>
			<Flags>AlwaysUseButtons</Flags>
			<Flags>Sound_NoAccept</Flags>
			<ButtonList>
				<WrappingPoint value="200"/>
				<ButtonPrompts>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 					<Contexts>*ALL*, CC_BACK</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>CC_RETURN</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 					<Contexts>*ALL*, CC_RETURN</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>FACES_ACC</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>					<Contexts>*ALL*, CC_YES</Contexts></CMenuButton>	
					<CMenuButton> <hButtonHash>IB_QUIT</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 					<Contexts>*ALL*, CC_QUIT</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>					<Contexts>*ALL*, CC_SELECT</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>CC_EDIT</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>					<Contexts>*ALL*, CC_SELECT_NEW</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT95</hButtonHash>		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>					<Contexts>*ALL*, CC_RETRY</Contexts></CMenuButton>					
					<CMenuButton> <hButtonHash>HUD_RANDOM</hButtonHash>			<ButtonInput>INPUT_FRONTEND_X</ButtonInput>							<Contexts>*ALL*, CC_RANDOM</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_RFEAT</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput>							<Contexts>*ALL*, CC_RANDOM_FEATURE</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_RAPP</hButtonHash>			<ButtonInput>INPUT_FRONTEND_X</ButtonInput>							<Contexts>*ALL*, CC_RANDOM_APPEARANCE</Contexts></CMenuButton>					
					<CMenuButton> <hButtonHash>FACES_TUNE</hButtonHash>			<ButtonInputGroup>INPUTGROUP_FRONTEND_RSTICK_ALL</ButtonInputGroup>	<Contexts>*ALL*, CC_TUNE</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_TUNE_KM</hButtonHash>		<ButtonInputGroup>INPUTGROUP_CURSOR</ButtonInputGroup>				<Contexts>*ALL*, CC_TUNE_MOUSE</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT9</hButtonHash>			<ButtonInput>INPUT_FRONTEND_DELETE</ButtonInput>							<Contexts>*ALL*, CC_DELETE</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT33</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>							<Contexts>*ALL*, CC_EDITNAME</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_RETURNSP</hButtonHash>		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput>					<Contexts>*ALL*, CC_RETURNSP</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_NAVIGATE</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_BUMPERS</ButtonInputGroup>	<Contexts>*ALL*, CC_NAV</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_NAVIGATE_KM</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_DPAD_LR</ButtonInputGroup>	<Contexts>*ALL*, CC_NAV_KEYBOARD</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_CLEARS</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>							<Contexts>*ALL*, CC_CLEARSTATS</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT56</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_RSTICK_ALL</ButtonInputGroup>	<Contexts>*ALL*, CC_TURN_HEAD</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT56_KM</hButtonHash>		<ButtonInputGroup>INPUTGROUP_CURSOR</ButtonInputGroup>				<Contexts>*ALL*, CC_TURN_HEAD_MOUSE</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_TR</hButtonHash>			<ButtonInput>INPUT_FRONTEND_RB</ButtonInput>						<Contexts>*ALL*, CC_TURN_RIGHT</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_TL</hButtonHash>			<ButtonInput>INPUT_FRONTEND_LB</ButtonInput>						<Contexts>*ALL*, CC_TURN_LEFT</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACE_CCOL</hButtonHash>			<ButtonInputGroup>INPUTGROUP_FRONTEND_TRIGGERS</ButtonInputGroup>	<Contexts>*ALL*, CC_CHANGE_COLOUR</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>FACE_COPAC</hButtonHash>			<ButtonInputGroup>INPUTGROUP_FRONTEND_RSTICK_ALL</ButtonInputGroup>	<Contexts>*ALL*, CC_CHANGE_OPACITY</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACE_COPAC_KM</hButtonHash>		<ButtonInputGroup>INPUTGROUP_CURSOR</ButtonInputGroup>				<Contexts>*ALL*, CC_CHANGE_OPACITY_MOUSE</Contexts></CMenuButton>
				
				</ButtonPrompts>
			</ButtonList>
				
		</Item>
		
		<!--  ACTUAL SCREEN ID: 39 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CREATION_LIFESTYLE</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CHAR_MOM_DAD</cGfxFilename>
			<depth>1</depth>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
			</MenuItems>
			<Flags>AlwaysUseButtons</Flags>
			<Flags>Sound_NoAccept</Flags>
			<ButtonList>
				<ButtonPrompts>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 					<Contexts>*ALL*, CC_BACK</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>CC_RETURN</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 					<Contexts>*ALL*, CC_RETURN</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>FACES_ACC</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>					<Contexts>*ALL*, CC_YES</Contexts></CMenuButton>	
					<CMenuButton> <hButtonHash>IB_QUIT</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 					<Contexts>*ALL*, CC_QUIT</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>					<Contexts>*ALL*, CC_SELECT</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT95</hButtonHash>		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>					<Contexts>*ALL*, CC_RETRY</Contexts></CMenuButton>					
					<CMenuButton> <hButtonHash>HUD_RANDOM</hButtonHash>			<ButtonInput>INPUT_FRONTEND_X</ButtonInput>							<Contexts>*ALL*, CC_RANDOM</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_RFEAT</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput>							<Contexts>*ALL*, CC_RANDOM_FEATURE</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_RAPP</hButtonHash>			<ButtonInput>INPUT_FRONTEND_X</ButtonInput>							<Contexts>*ALL*, CC_RANDOM_APPEARANCE</Contexts></CMenuButton>					
					<CMenuButton> <hButtonHash>FACES_TUNE</hButtonHash>			<ButtonInputGroup>INPUTGROUP_FRONTEND_RSTICK_ALL</ButtonInputGroup>	<Contexts>*ALL*, CC_TUNE</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_TUNE_KM</hButtonHash>		<ButtonInputGroup>INPUTGROUP_CURSOR</ButtonInputGroup>				<Contexts>*ALL*, CC_TUNE_MOUSE</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT9</hButtonHash>			<ButtonInput>INPUT_FRONTEND_DELETE</ButtonInput>							<Contexts>*ALL*, CC_DELETE</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT33</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>							<Contexts>*ALL*, CC_EDITNAME</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_RETURNSP</hButtonHash>		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput>					<Contexts>*ALL*, CC_RETURNSP</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_NAVIGATE</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_BUMPERS</ButtonInputGroup>	<Contexts>*ALL*, CC_NAV</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_NAVIGATE_KM</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_DPAD_LR</ButtonInputGroup>	<Contexts>*ALL*, CC_NAV_KEYBOARD</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_CLEARS</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>							<Contexts>*ALL*, CC_CLEARSTATS</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT56</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_RSTICK_ALL</ButtonInputGroup>	<Contexts>*ALL*, CC_TURN_HEAD</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT56_KM</hButtonHash>		<ButtonInputGroup>INPUTGROUP_CURSOR</ButtonInputGroup>				<Contexts>*ALL*, CC_TURN_HEAD_MOUSE</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_TR</hButtonHash>			<ButtonInput>INPUT_FRONTEND_RB</ButtonInput>						<Contexts>*ALL*, CC_TURN_RIGHT</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_TL</hButtonHash>			<ButtonInput>INPUT_FRONTEND_LB</ButtonInput>						<Contexts>*ALL*, CC_TURN_LEFT</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACE_CCOL</hButtonHash>			<ButtonInputGroup>INPUTGROUP_FRONTEND_TRIGGERS</ButtonInputGroup>	<Contexts>*ALL*, CC_CHANGE_COLOUR</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>FACE_COPAC</hButtonHash>			<ButtonInputGroup>INPUTGROUP_FRONTEND_RSTICK_ALL</ButtonInputGroup>	<Contexts>*ALL*, CC_CHANGE_OPACITY</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACE_COPAC_KM</hButtonHash>		<ButtonInputGroup>INPUTGROUP_CURSOR</ButtonInputGroup>				<Contexts>*ALL*, CC_CHANGE_OPACITY_MOUSE</Contexts></CMenuButton>
				</ButtonPrompts>
			</ButtonList>
		</Item>

		<!--  ACTUAL SCREEN ID: 40 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CREATION_YOU</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CHAR_MOM_DAD</cGfxFilename>
			<depth>1</depth>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
			</MenuItems>
			<Flags>AlwaysUseButtons</Flags>
			<Flags>Sound_NoAccept</Flags>
			<ButtonList>
				<ButtonPrompts>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 					<Contexts>*ALL*, CC_BACK</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>CC_RETURN</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 					<Contexts>*ALL*, CC_RETURN</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>FACES_ACC</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>					<Contexts>*ALL*, CC_YES</Contexts></CMenuButton>	
					<CMenuButton> <hButtonHash>IB_QUIT</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 					<Contexts>*ALL*, CC_QUIT</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>					<Contexts>*ALL*, CC_SELECT</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT95</hButtonHash>		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>					<Contexts>*ALL*, CC_RETRY</Contexts></CMenuButton>					
					<CMenuButton> <hButtonHash>HUD_RANDOM</hButtonHash>			<ButtonInput>INPUT_FRONTEND_X</ButtonInput>							<Contexts>*ALL*, CC_RANDOM</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_RFEAT</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput>							<Contexts>*ALL*, CC_RANDOM_FEATURE</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_RAPP</hButtonHash>			<ButtonInput>INPUT_FRONTEND_X</ButtonInput>							<Contexts>*ALL*, CC_RANDOM_APPEARANCE</Contexts></CMenuButton>					
					<CMenuButton> <hButtonHash>FACES_TUNE</hButtonHash>			<ButtonInputGroup>INPUTGROUP_FRONTEND_RSTICK_ALL</ButtonInputGroup>	<Contexts>*ALL*, CC_TUNE</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_TUNE_KM</hButtonHash>		<ButtonInputGroup>INPUTGROUP_CURSOR</ButtonInputGroup>				<Contexts>*ALL*, CC_TUNE_MOUSE</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT9</hButtonHash>			<ButtonInput>INPUT_FRONTEND_DELETE</ButtonInput>					<Contexts>*ALL*, CC_DELETE</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT33</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>							<Contexts>*ALL*, CC_EDITNAME</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_RETURNSP</hButtonHash>		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput>					<Contexts>*ALL*, CC_RETURNSP</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>IB_NAVIGATE</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_BUMPERS</ButtonInputGroup>	<Contexts>*ALL*, CC_NAV</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_CLEARS</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>							<Contexts>*ALL*, CC_CLEARSTATS</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT56</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_RSTICK_ALL</ButtonInputGroup>	<Contexts>*ALL*, CC_TURN_HEAD</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT56_KM</hButtonHash>		<ButtonInputGroup>INPUTGROUP_CURSOR</ButtonInputGroup>				<Contexts>*ALL*, CC_TURN_HEAD_MOUSE</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_TR</hButtonHash>			<ButtonInput>INPUT_FRONTEND_RB</ButtonInput>						<Contexts>*ALL*, CC_TURN_RIGHT</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACES_TL</hButtonHash>			<ButtonInput>INPUT_FRONTEND_LB</ButtonInput>						<Contexts>*ALL*, CC_TURN_LEFT</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACE_CCOL</hButtonHash>			<ButtonInputGroup>INPUTGROUP_FRONTEND_TRIGGERS</ButtonInputGroup>	<Contexts>*ALL*, CC_CHANGE_COLOUR</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>FACE_COPAC</hButtonHash>			<ButtonInputGroup>INPUTGROUP_FRONTEND_RSTICK_ALL</ButtonInputGroup>	<Contexts>*ALL*, CC_CHANGE_OPACITY</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>FACE_COPAC_KM</hButtonHash>		<ButtonInputGroup>INPUTGROUP_CURSOR</ButtonInputGroup>				<Contexts>*ALL*, CC_CHANGE_OPACITY_MOUSE</Contexts></CMenuButton>
					
				</ButtonPrompts>
			</ButtonList>

		</Item>
		
		<!--  ACTUAL SCREEN ID: 41 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_MISSION_CREATOR_CATEGORY</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns</Flags>
		</Item>
		
		<!--  ACTUAL SCREEN ID: 42 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_MISSION_CREATOR_LISTITEM</MenuScreen>
			<depth>3</depth>
			<Flags>SF_NoClearRootColumns SF_NoMenuAdvance</Flags>
		</Item>
		
		<!--  ACTUAL SCREEN ID: 43 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_MISSION_CREATOR_STAT</MenuScreen>
			<depth>4</depth>
		</Item>
		
		<!--  ACTUAL SCREEN ID: 44 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CREW</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CREWS</cGfxFilename>
			<runtime>
				<type>CREWDETAILS</type>
				<params>
					<data key="sp_path">SP_MENUPED</data>
					<data key="canpersist">TRUE</data>
					
				</params>
			</runtime>
			<depth>1</depth>
			<Flags>HandlesDisplayDataSlot</Flags>
			<ScrollBarFlags>Width_1 ManualUpdate InitiallyInvisible Align_Right</ScrollBarFlags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
			 <ContextMenu>
				<TriggerMenuId>MENU_UNIQUE_ID_CREW_LIST</TriggerMenuId>
				<ContextMenuId>MENU_UNIQUE_ID_CREW_OPTIONS_LIST</ContextMenuId>
				<depth>2</depth>
				
				<contextOptions>
					<contextOption>JOIN_GAME</contextOption>
					<contextOption>JIP_GAME</contextOption>
					<contextOption>JIP_GAME_DIS</contextOption>
					<contextOption>JIP_GAME_TRANSITION</contextOption>
					<contextOption>SEND_GAME_INVITE</contextOption>
					<contextOption>SEND_GAME_INVITE_DIS</contextOption>
					<contextOption>SEND_PARTY_INVITE</contextOption>
					<contextOption>KICK_PARTY</contextOption>
					<contextOption>SEND_MESSAGE</contextOption>
					<contextOption>SHOW_GAMER_CARD</contextOption>
					<contextOption>GAMER_REVIEW</contextOption>
					<contextOption>SEND_CREW_INVITE</contextOption>
			</contextOptions>

			</ContextMenu>

		</Item>

		<Item>	<MenuScreen>MENU_UNIQUE_ID_CREW_LIST</MenuScreen>				<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>	<ScrollBarFlags>Width_1 ManualUpdate InitiallyInvisible Align_Right</ScrollBarFlags> <depth>1</depth>	</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_CREW_OPTIONS_LIST</MenuScreen>		<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>	<ScrollBarFlags>Width_1</ScrollBarFlags> <depth>1</depth>	</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_CREW_OPTIONS</MenuScreen>			<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>	<ScrollBarFlags>Width_1</ScrollBarFlags> <depth>1</depth>	</Item>
		
		<!--  ACTUAL SCREEN ID: 45 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_FRIENDS_MP</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_FRIENDS_MP</cGfxFilename>
			<runtime>
				<type>MPFRIENDS</type>
				<params>
					<data key="path">MP_MENUPED</data>
					<data key="canpersist">TRUE</data>
				</params>
			</runtime>
			<depth>1</depth>
			<Flags>HandlesDisplayDataSlot EnterMenuOnMouseClick</Flags>
			<ScrollBarFlags>Width_1 ManualUpdate InitiallyInvisible Align_Right</ScrollBarFlags>
			<ContextMenu>
				<TriggerMenuId>MENU_UNIQUE_ID_FRIENDS_LIST</TriggerMenuId>
				<ContextMenuId>MENU_UNIQUE_ID_FRIENDS_OPTIONS_LIST</ContextMenuId>
				<depth>2</depth>
				
				<contextOptions>
					<contextOption>JOIN_GAME</contextOption>
					<contextOption>JIP_GAME</contextOption>
					<contextOption>JIP_GAME_DIS</contextOption>
					<contextOption>JIP_GAME_TRANSITION</contextOption>
					<contextOption>SEND_GAME_INVITE</contextOption>
					<contextOption>SEND_GAME_INVITE_DIS</contextOption>
					<contextOption>SEND_PARTY_INVITE</contextOption>
					<contextOption>JOIN_PARTY</contextOption>
					<contextOption>MUTE</contextOption>
					<contextOption>KICK_PARTY</contextOption>
					<contextOption>SEND_MESSAGE</contextOption>
					<contextOption>SHOW_GAMER_CARD</contextOption>
					<contextOption>GAMER_REVIEW</contextOption>
					<contextOption>SPECTATE_OTHER_SESSION</contextOption>
					<contextOption>SPECTATE_OTHER_SESSION_DISABLED</contextOption>
					<contextOption>SEND_CREW_INVITE</contextOption>
					<contextOption>ADD_A_FRIEND</contextOption>
				</contextOptions>
			</ContextMenu>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
				
			<ButtonList>
				<ButtonPrompts IncludeDefaults="true">
					<CMenuButton> <hButtonHash>IB_SOCIAL_CLUB</hButtonHash> <ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*ALL*,NotInSocialClub,NAVIGATING_CONTENT,*NONE*,Offline,UpdateSocialClub</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_UPDATE_SOCIAL_CLUB</hButtonHash> <ButtonInput>INPUT_FRONTEND_Y</ButtonInput> <Contexts>*ALL*,UpdateSocialClub,NAVIGATING_CONTENT,*NONE*,Offline,NotInSocialClub</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_COMPARE</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,SyncingStats,Comparing,*ALL*,HasPlayers,NAVIGATING_CONTENT,HasPlayedMode</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_UNCOMPARE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,SyncingStats,*ALL*,HasPlayers,NAVIGATING_CONTENT,HasPlayedMode,Comparing</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_REFRESH</hButtonHash>	<ButtonInput>INPUT_FRONTEND_SELECT</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,SyncingStats,*ALL*,NAVIGATING_CONTENT,CanRefreshFriends</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
		</Item>
	
		<!--  ACTUAL SCREEN ID: 46 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_SETTINGS_FEED</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns</Flags>
			<ScrollBarFlags>Width_2 ManualUpdate InitiallyInvisible</ScrollBarFlags>
			<MenuItems>
				<Item> <cTextId>FEED_PHONE</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FEED_PHONE</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>FEED_STATS</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FEED_STATS</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>FEED_CREW</cTextId>			<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FEED_CREW</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>FEED_FRIENDS</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FEED_FRIENDS</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>FEED_SOCIAL</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FEED_SOCIAL</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>FEED_STORE</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FEED_STORE</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>FEED_TOOLTIP</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FEED_TOOLTIP</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>FEED_DELAY</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_FEED_DELAY</MenuPref> 			<MenuOption>MENU_OPTION_FEED_DELAY</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
			</MenuItems>
		</Item>

		<!--  ACTUAL SCREEN ID: 47 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_LOBBY_LIST</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_SAVE</cGfxFilename>
			<depth>1</depth>
			<Flags>Input_NoBack</Flags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
			</MenuItems>
			<ButtonList>
				<ButtonPrompts>
					<!--<CMenuButton> <hButtonHash>HUD_INPUT70</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_DPAD_LR</ButtonInputGroup>	 <Contexts>*ALL*,CHOOSE_NUMBER</Contexts>	</CMenuButton>-->
					<!--<CMenuButton> <hButtonHash>IB_NAVIGATE</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_DPAD_UD</ButtonInputGroup>		</CMenuButton>-->
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 				</CMenuButton>
					<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>				</CMenuButton>
				</ButtonPrompts>
			</ButtonList>

		</Item>


		<!--  ACTUAL SCREEN ID: 48 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CORONA_SETTINGS</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CORONA</cGfxFilename>
			<depth>1</depth>
			<Flags>Input_NoTabChange AlwaysUseButtons Input_NoBack Sound_NoAccept</Flags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_HEADER_CORONA_LOBBY</MenuUniqueId></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE</MenuUniqueId></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_MAP</MenuUniqueId></Item>
			</MenuItems>
			<ButtonList>
				<ButtonPrompts>
					<CMenuButton> <hButtonHash>HUD_INPUT68</hButtonHash>		<ButtonInput>INPUT_FRONTEND_LEADERBOARD</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, *NONE*,HEIST_SCREEN, AUTOFILL_CORONA, NO_LEADERBOARD, CORONA_HOST_SCREEN, FM_TUTORIAL</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT69</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, *NONE*,FM_TUTORIAL, AUTOFILL_CORONA, CORONA_SKYSWOOP</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT84</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, AUTOFILL_CORONA</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>				<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, *NONE*,FM_TUTORIAL, AUTOFILL_CORONA, CORONA_TOURNAMENT</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_CONTINUE</hButtonHash>		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>				<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, AUTOFILL_CONTINUE</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT85</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput>				<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_MAP_AVAIL, *NONE*, AUTOFILL_CORONA</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT98</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput>				<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_PLYS_AVAIL, *NONE*, AUTOFILL_CORONA</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>FM_COR_SHOW_BIGMAP</hButtonHash>	<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_BIGMAP_AVAIL, *NONE*</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>FM_COR_SHOW_BIGMAP</hButtonHash>	<ButtonInput>INPUT_FRONTEND_LS</ButtonInput>		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_BIGMAP_AVAIL_KM, *NONE*</Contexts> </CMenuButton>
					
					<CMenuButton> <hButtonHash>PM_DYNAMICSTRING</hButtonHash>	<RawButtonIcon>NOTHING</RawButtonIcon>				<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_DISPLAY_TIMER, *NONE*, CORONA_DISPLAY_LAUNCH</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_DYNAMICSTRING</hButtonHash>	<RawButtonIcon>NOTHING</RawButtonIcon>				<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_DISPLAY_LAUNCH, *NONE*, CORONA_DISPLAY_TIMER</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>FM_COR_INC_T</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>				<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_EXTEND_TIME</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>FM_COR_INC_T_KM</hButtonHash>	<ButtonInput>INPUT_FRONTEND_RB</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_EXTEND_TIME_KM</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_DYNAMICSTRING</hButtonHash>	<RawButtonIcon>NOTHING</RawButtonIcon>				<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, FM_TUTORIAL, *NONE*, CORONA_DISPLAY_WAIT </Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>ITEM_AMMO</hButtonHash>			<ButtonInput>INPUT_FRONTEND_RIGHT</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, TOGGLE_AMMO</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT100</hButtonHash>		<ButtonInput>INPUT_FRONTEND_RT</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_PLAYERS_R2, *NONE*, CORONA_PLAYERS_L2</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT100</hButtonHash>		<ButtonInput>INPUT_FRONTEND_LT</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_PLAYERS_L2, *NONE*, CORONA_PLAYERS_R2</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
		</Item>

		<!--  ACTUAL SCREEN ID: 49 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CORONA_INVITE</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CORONA_LOBBY</cGfxFilename>
			<runtime>
				<type>SCRIPT</type>
				<params>
					<data key="canpersist">TRUE</data>
				</params>
			</runtime>
			<depth>1</depth>
			<Flags>Input_NoTabChange Input_NoTabChange AlwaysUseButtons Input_NoBack Sound_NoAccept</Flags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_LAST_JOB_PLAYERS</MenuUniqueId></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_FRIENDS</MenuUniqueId></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_CREWS</MenuUniqueId></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_PLAYERS</MenuUniqueId></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_INVITE_MATCHED_PLAYERS</MenuUniqueId></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CORONA_JOINED_PLAYERS</MenuUniqueId></Item>
				
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_HEADER_CORONA_JOINED_PLAYERS</MenuUniqueId></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_HEADER_CORONA_INVITE_PLAYERS</MenuUniqueId></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_HEADER_CORONA_INVITE_FRIENDS</MenuUniqueId></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_HEADER_CORONA_INVITE_CREWS</MenuUniqueId></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_HEADER_CORONA_INVITE_MATCHED_PLAYERS</MenuUniqueId></Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_HEADER_CORONA_INVITE_LAST_JOB_PLAYERS</MenuUniqueId></Item>
			</MenuItems>
			<ButtonList>
				<ButtonPrompts>
					<CMenuButton> <hButtonHash>HUD_INPUT53</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, *NONE*, CORONA_LOCKED, AUTOFILL_CORONA</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT69</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_LOCKED, *NONE*, AUTOFILL_CORONA</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT84</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, AUTOFILL_CORONA</Contexts> </CMenuButton>					
					<CMenuButton> <hButtonHash>HUD_CONTINUE</hButtonHash>		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, AUTOFILL_CONTINUE</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>		    <Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, *NONE*, AUTOFILL_CORONA</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_DYNAMICSTRING</hButtonHash>	<RawButtonIcon>NOTHING</RawButtonIcon>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_DISPLAY_TIMER, *NONE*, CORONA_DISPLAY_LAUNCH</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_DYNAMICSTRING</hButtonHash>	<RawButtonIcon>NOTHING</RawButtonIcon>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_DISPLAY_LAUNCH, *NONE*, CORONA_DISPLAY_TIMER</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>FM_COR_INC_T</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_EXTEND_TIME</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>FM_COR_INC_T_KM</hButtonHash>		<ButtonInput>INPUT_FRONTEND_RB</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_EXTEND_TIME_KM</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT100</hButtonHash>		<ButtonInput>INPUT_FRONTEND_RT</ButtonInput>		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_PLAYERS_R2, *NONE*, CORONA_PLAYERS_L2</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT100</hButtonHash>		<ButtonInput>INPUT_FRONTEND_LT</ButtonInput>		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_PLAYERS_L2, *NONE*, CORONA_PLAYERS_R2</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
		</Item>
		
		<!--  ACTUAL SCREEN ID: 50 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CORONA_JOINED_PLAYERS</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CORONA_PLAYERS</cGfxFilename>
			<runtime>
				<type>CORONA_INVITE_JOINED</type>
				<params>
					<data key="canpersist">TRUE</data>
				</params>
			</runtime>
			<depth>1</depth>
      <MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
			<Flags>AlwaysUseButtons Input_NoTabChange Input_NoBack HandlesDisplayDataSlot</Flags>
			<ButtonList>
				<ButtonPrompts IncludeDefaults="true">
					<CMenuButton> <hButtonHash>IB_COMPARE</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,SyncingStats,Comparing,*ALL*,HasPlayers,NAVIGATING_CONTENT,HasPlayedMode</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_UNCOMPARE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,SyncingStats,*ALL*,HasPlayers,NAVIGATING_CONTENT,HasPlayedMode,Comparing</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>CM_SHOW_GAMER_CARD</hButtonHash>	<ButtonInput>INPUT_FRONTEND_SELECT</ButtonInput> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
			<ContextMenu>
				<TriggerMenuId>MENU_UNIQUE_ID_PLAYERS_LIST</TriggerMenuId>
				<ContextMenuId>MENU_UNIQUE_ID_PLAYERS_OPTIONS_LIST</ContextMenuId>
				<depth>2</depth>
			
				<contextOptions>
					<contextOption>SEND_PARTY_INVITE</contextOption>
					<contextOption>SEND_FRIEND_INVITE</contextOption>
					<contextOption>SEND_CREW_INVITE</contextOption>
					<contextOption>SEND_MESSAGE</contextOption>
					<contextOption>JOIN_PARTY</contextOption>
					<contextOption>MUTE</contextOption>
					<contextOption>CORONA_KICK</contextOption>
					<contextOption>REPORT</contextOption>
					<contextOption>REPORT_DISABLED</contextOption>
					<contextOption>COMMEND</contextOption>
					<contextOption>COMMEND_DISABLED</contextOption>
					<contextOption>SHOW_GAMER_CARD</contextOption>
					<contextOption>GAMER_REVIEW</contextOption>
				</contextOptions>
			</ContextMenu>					
		</Item>

		<!--  ACTUAL SCREEN ID: 51 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CORONA_INVITE_PLAYERS</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CORONA_PLAYERS</cGfxFilename>
			<runtime>
				<type>CORONA_INVITE_PLAYERS</type>
				<params>
					<data key="canpersist">TRUE</data>
				</params>
			</runtime>
			<depth>1</depth>
			<ScrollBarFlags>Width_1 ManualUpdate Align_Right InitiallyInvisible</ScrollBarFlags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
			<Flags>Input_NoBack Input_NoTabChange HandlesDisplayDataSlot</Flags>
			<ButtonList>
				<ButtonPrompts>
					<!--<CMenuButton> <hButtonHash>HUD_INPUT1C</hButtonHash>					<ButtonInputGroup>INPUTGROUP_FRONTEND_DPAD_LR</ButtonInputGroup> <Contexts>*NONE*,NAVIGATING_CONTENT</Contexts></CMenuButton>-->
					<!--<CMenuButton IsNavigate="true"> <hButtonHash>FE_HLP5</hButtonHash>		<RawButtonIcon>ARROW_UPDOWN</RawButtonIcon> 	  <Contexts>*ANY*,SINGLE_SCREEN,NAVIGATING_CONTENT</Contexts></CMenuButton>-->
					<CMenuButton> <hButtonHash>IB_INVITE</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,IsInTransition,IsInvitedToTransition,TutLock,*ALL*,HasPlayers,PlayerIsOnline</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_CANCLE_INVITE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,IsInTransition,*ALL*,IsInvitedToTransition,HasPlayers</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_KICK</hButtonHash>			<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,IsKickForceHidden,*ALL*,IsInTransition,HasPlayers</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> </CMenuButton>
					<CMenuButton> <hButtonHash>CM_MUTE</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>	<Contexts>*NONE*,IsLocalPlayer,MutedByOS,*ALL*,InLocalSession,HasPlayers</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_DYNAMICSTRING</hButtonHash>	<RawButtonIcon>NOTHING</RawButtonIcon>				<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_DISPLAY_TIMER, *NONE*, CORONA_DISPLAY_LAUNCH</Contexts> </CMenuButton>
          <CMenuButton> <hButtonHash>CM_SHOW_GAMER_CARD</hButtonHash> <ButtonInput>INPUT_FRONTEND_SELECT</ButtonInput> </CMenuButton>

				</ButtonPrompts>
			</ButtonList>
			<ContextMenu>
				<TriggerMenuId>MENU_UNIQUE_ID_PLAYERS_LIST</TriggerMenuId>
				<ContextMenuId>MENU_UNIQUE_ID_PLAYERS_OPTIONS_LIST</ContextMenuId>
				<depth>2</depth>
			</ContextMenu>
		</Item>

		<!--  ACTUAL SCREEN ID: 52 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CORONA_INVITE_FRIENDS</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CORONA_PLAYERS</cGfxFilename>
			<runtime>
				<type>CORONA_INVITE_FRIENDS</type>
				<params>
					<data key="canpersist">TRUE</data>
				</params>
			</runtime>
			
			<depth>1</depth>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
			<Flags>AlwaysUseButtons Input_NoTabChange Input_NoBack HandlesDisplayDataSlot</Flags>
			<ScrollBarFlags>Width_1 ManualUpdate Align_Right InitiallyInvisible</ScrollBarFlags>
			<ButtonList>
				<ButtonPrompts>
					<!--<CMenuButton> <hButtonHash>HUD_INPUT1C</hButtonHash>					<ButtonInputGroup>INPUTGROUP_FRONTEND_DPAD_LR</ButtonInputGroup> <Contexts>*NONE*,NAVIGATING_CONTENT</Contexts></CMenuButton>-->
					<!--<CMenuButton IsNavigate="true"> <hButtonHash>FE_HLP5</hButtonHash>		<RawButtonIcon>ARROW_UPDOWN</RawButtonIcon> 	  <Contexts>*ANY*,SINGLE_SCREEN,NAVIGATING_CONTENT</Contexts></CMenuButton>-->
					<CMenuButton> <hButtonHash>IB_INVITE</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,IsInTransition,IsInvitedToTransition,TutLock,*ALL*,HasPlayers,PlayerIsOnline</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_CANCLE_INVITE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,IsInTransition,*ALL*,IsInvitedToTransition,HasPlayers</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_KICK</hButtonHash>			<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,IsKickForceHidden,*ALL*,IsInTransition,HasPlayers</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> </CMenuButton>
					<CMenuButton> <hButtonHash>CM_MUTE</hButtonHash>			<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>	<Contexts>*NONE*,IsLocalPlayer,MutedByOS,*ALL*,InLocalSession,HasPlayers</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>CM_SHOW_GAMER_CARD</hButtonHash> <ButtonInput>INPUT_FRONTEND_SELECT</ButtonInput>
					<Contexts>*NONE*,NO_RESULTS</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_DYNAMICSTRING</hButtonHash>	<RawButtonIcon>NOTHING</RawButtonIcon>				<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_DISPLAY_TIMER, *NONE*, CORONA_DISPLAY_LAUNCH</Contexts> </CMenuButton>
        </ButtonPrompts>
			</ButtonList>
			<ContextMenu>
				<TriggerMenuId>MENU_UNIQUE_ID_PLAYERS_LIST</TriggerMenuId>
				<ContextMenuId>MENU_UNIQUE_ID_PLAYERS_OPTIONS_LIST</ContextMenuId>
				<depth>2</depth>
				
				<contextOptions>
					<contextOption>SEND_GAME_INVITE</contextOption>
					<contextOption>SEND_PARTY_INVITE</contextOption>
					<contextOption>SEND_CREW_INVITE</contextOption>
					<contextOption>JOIN_GAME</contextOption>
					<contextOption>JOIN_PARTY</contextOption>
					<contextOption>MUTE</contextOption>
					<contextOption>KICK_PARTY</contextOption>
					<contextOption>SHOW_GAMER_CARD</contextOption>
					<contextOption>GAMER_REVIEW</contextOption>
					<contextOption>SEND_MESSAGE</contextOption>
					<contextOption>ADD_A_FRIEND</contextOption>
				</contextOptions>
			</ContextMenu>
		</Item>
		

		<!--  ACTUAL SCREEN ID: 53 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CORONA_INVITE_CREWS</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CORONA_PLAYERS</cGfxFilename>
			<runtime>
				<type>CORONA_INVITE_CREWS</type>
				<params>
					<data key="canpersist">TRUE</data>
				</params>
			</runtime>
		
			<depth>1</depth>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
				<Item> <MenuAction>MENU_OPTION_ACTION_REFERENCE</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_CREWS</MenuUniqueId> </Item>
			</MenuItems>
			<Flags>AlwaysUseButtons Input_NoTabChange Input_NoBack HandlesDisplayDataSlot</Flags>
			<ScrollBarFlags>Width_1 ManualUpdate Align_Right InitiallyInvisible</ScrollBarFlags>
			<ButtonList>
				<ButtonPrompts>
					<!--<CMenuButton> <hButtonHash>HUD_INPUT1C</hButtonHash>					<ButtonInputGroup>INPUTGROUP_FRONTEND_DPAD_LR</ButtonInputGroup> <Contexts>*NONE*,NAVIGATING_CONTENT</Contexts></CMenuButton>-->
					<!--<CMenuButton IsNavigate="true"> <hButtonHash>FE_HLP5</hButtonHash>		<RawButtonIcon>ARROW_UPDOWN</RawButtonIcon> 	  <Contexts>*ANY*,SINGLE_SCREEN,NAVIGATING_CONTENT</Contexts></CMenuButton>-->
					<CMenuButton> <hButtonHash>IB_INVITE</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,IsInTransition,IsInvitedToTransition,TutLock,*ALL*,HasPlayers,PlayerIsOnline</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_CANCLE_INVITE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,IsInTransition,*ALL*,IsInvitedToTransition,HasPlayers</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_KICK</hButtonHash>			<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,IsKickForceHidden,*ALL*,IsInTransition,HasPlayers</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> </CMenuButton>
          <CMenuButton> <hButtonHash>CM_SHOW_GAMER_CARD</hButtonHash><ButtonInput>INPUT_FRONTEND_SELECT</ButtonInput></CMenuButton>
					<CMenuButton> <hButtonHash>CM_MUTE</hButtonHash>			<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>	<Contexts>*NONE*,IsLocalPlayer,MutedByOS,*ALL*,InLocalSession,HasPlayers</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_DYNAMICSTRING</hButtonHash>	<RawButtonIcon>NOTHING</RawButtonIcon>				<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_DISPLAY_TIMER, *NONE*, CORONA_DISPLAY_LAUNCH</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
			<ContextMenu>
				<TriggerMenuId>MENU_UNIQUE_ID_PLAYERS_LIST</TriggerMenuId>
				<ContextMenuId>MENU_UNIQUE_ID_PLAYERS_OPTIONS_LIST</ContextMenuId>
				<depth>2</depth>
				
				<contextOptions>
					<contextOption>SEND_GAME_INVITE</contextOption>
					<contextOption>SEND_PARTY_INVITE</contextOption>
					<contextOption>SEND_CREW_INVITE</contextOption>
					<contextOption>JOIN_GAME</contextOption>
					<contextOption>JOIN_PARTY</contextOption>
					<contextOption>MUTE</contextOption>
					<contextOption>KICK_PARTY</contextOption>
					<contextOption>SHOW_GAMER_CARD</contextOption>
					<contextOption>GAMER_REVIEW</contextOption>
					<contextOption>SEND_MESSAGE</contextOption>
					<contextOption>ADD_A_FRIEND</contextOption>
				</contextOptions>
			</ContextMenu>
		</Item>
	
		<!--  ACTUAL SCREEN ID: 54 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_STORE</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_STORE</cGfxFilename>
			<runtime>
				<type>STORETAB</type>
			</runtime>
			<depth>1</depth>
			<MenuItems>
				<Item><MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction></Item>
			</MenuItems>
			<Flags>AlwaysUseButtons SF_NoMenuAdvance SF_NoClearRootColumns Sound_NoAccept</Flags>
			<ScrollBarFlags>Width_3 InitiallyInvisible ManualUpdate Align_Right</ScrollBarFlags>
			<ButtonList>
				<ButtonPrompts>
					<CMenuButton> <hButtonHash>HUD_INPUT1C</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_BUMPERS</ButtonInputGroup> 	<Contexts>*NONE*,TABS_ARE_COLUMNS,NAVIGATING_CONTENT,MENU_OFF</Contexts>				</CMenuButton>
					<CMenuButton> <hButtonHash>IB_HIDEMENU</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> 						<Contexts>*ALL*,CAN_TOGGLE_MENU,		 *NONE*,MENU_OFF, NAVIGATING_CONTENT</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_SHOWMENU</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> 						<Contexts>*ALL*,CAN_TOGGLE_MENU,MENU_OFF,*NONE*,		  NAVIGATING_CONTENT</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_ENTER_STORE</hButtonHash>		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> 					<Contexts>*NONE*,MENU_OFF,HIDE_ACCEPTBUTTON</Contexts> 									</CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput>  																											</CMenuButton>
					<CMenuButton> <hButtonHash>IB_SCROLL</hButtonHash>			<ButtonInput>INPUT_FRONTEND_RIGHT_AXIS_Y</ButtonInput>				<Contexts>*ALL*,STORE_CanScroll,*NONE*,MENU_OFF</Contexts>								</CMenuButton>
				</ButtonPrompts>
			</ButtonList>
		</Item>
	
		<!--  ACTUAL SCREEN ID: 55 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CORONA_INVITE_MATCHED_PLAYERS</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CORONA_PLAYERS</cGfxFilename>
			<runtime>
				<type>CORONA_INVITE_MATCHED_PLAYERS</type>
				<params>
					<data key="canpersist">TRUE</data>
				</params>
			</runtime>			

			<depth>1</depth>
			<Flags>AlwaysUseButtons SF_NoMenuAdvance SF_NoClearRootColumns Input_NoBack HandlesDisplayDataSlot</Flags>
			<ScrollBarFlags>Width_1 ManualUpdate Align_Right InitiallyInvisible</ScrollBarFlags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
			<ButtonList>
				<ButtonPrompts>
					<!--<CMenuButton> <hButtonHash>HUD_INPUT1C</hButtonHash>					<ButtonInputGroup>INPUTGROUP_FRONTEND_DPAD_LR</ButtonInputGroup> <Contexts>*NONE*,NAVIGATING_CONTENT</Contexts></CMenuButton>-->
					<!--<CMenuButton IsNavigate="true"> <hButtonHash>FE_HLP5</hButtonHash>		<RawButtonIcon>ARROW_UPDOWN</RawButtonIcon> 	  <Contexts>*ANY*,SINGLE_SCREEN,NAVIGATING_CONTENT</Contexts></CMenuButton>-->
					<CMenuButton> <hButtonHash>IB_INVITE</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,IsInTransition,IsInvitedToTransition,TutLock,*ALL*,HasPlayers,PlayerIsOnline</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_CANCLE_INVITE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,IsInTransition,*ALL*,IsInvitedToTransition,HasPlayers</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_KICK</hButtonHash>			<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,IsKickForceHidden,*ALL*,IsInTransition,HasPlayers</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> </CMenuButton>
					<CMenuButton> <hButtonHash>CM_MUTE</hButtonHash>			<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>	<Contexts>*NONE*,IsLocalPlayer,MutedByOS,*ALL*,InLocalSession,HasPlayers</Contexts> </CMenuButton>
          <CMenuButton> <hButtonHash>CM_SHOW_GAMER_CARD</hButtonHash> <ButtonInput>INPUT_FRONTEND_SELECT</ButtonInput></CMenuButton>

          <CMenuButton> <hButtonHash>PM_DYNAMICSTRING</hButtonHash>	<RawButtonIcon>NOTHING</RawButtonIcon>				<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_DISPLAY_TIMER, *NONE*, CORONA_DISPLAY_LAUNCH</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
			<ContextMenu>
				<TriggerMenuId>MENU_UNIQUE_ID_PLAYERS_LIST</TriggerMenuId>
				<ContextMenuId>MENU_UNIQUE_ID_PLAYERS_OPTIONS_LIST</ContextMenuId>
				<depth>2</depth>
				
				<contextOptions>
					<contextOption>SEND_GAME_INVITE</contextOption>
					<contextOption>SEND_PARTY_INVITE</contextOption>
					<contextOption>SEND_CREW_INVITE</contextOption>
					<contextOption>JOIN_GAME</contextOption>
					<contextOption>JOIN_PARTY</contextOption>
					<contextOption>MUTE</contextOption>
					<contextOption>KICK_PARTY</contextOption>
					<contextOption>SHOW_GAMER_CARD</contextOption>
					<contextOption>GAMER_REVIEW</contextOption>
					<contextOption>SEND_MESSAGE</contextOption>
					<contextOption>ADD_A_FRIEND</contextOption>
				</contextOptions>
			</ContextMenu>
		</Item>
		
		<!--  ACTUAL SCREEN ID: 56 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_CORONA_INVITE_LAST_JOB_PLAYERS</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CORONA_PLAYERS</cGfxFilename>
			<runtime>
				<type>CORONA_INVITE_LAST_JOB_PLAYERS</type>
				<params>
					<data key="canpersist">TRUE</data>
				</params>
			</runtime>
		
			<depth>1</depth>
			<Flags>AlwaysUseButtons SF_NoMenuAdvance SF_NoClearRootColumns Input_NoBack HandlesDisplayDataSlot</Flags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
			<ButtonList>
				<ButtonPrompts>
          			<CMenuButton> <hButtonHash>IB_INVITE</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,IsInTransition,IsInvitedToTransition,TutLock,*ALL*,HasPlayers,PlayerIsOnline</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_CANCLE_INVITE</hButtonHash>	<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,IsInTransition,*ALL*,IsInvitedToTransition,HasPlayers</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_KICK</hButtonHash>			<ButtonInput>INPUT_FRONTEND_X</ButtonInput> <Contexts>*NONE*,IsLocalPlayer,IsKickForceHidden,*ALL*,IsInTransition,HasPlayers</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> </CMenuButton>
					<CMenuButton> <hButtonHash>CM_MUTE</hButtonHash>			<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>	<Contexts>*NONE*,IsLocalPlayer,MutedByOS,*ALL*,InLocalSession,HasPlayers</Contexts> </CMenuButton>
					<CMenuButton><hButtonHash>CM_SHOW_GAMER_CARD</hButtonHash><ButtonInput>INPUT_FRONTEND_SELECT</ButtonInput> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_DYNAMICSTRING</hButtonHash>	<RawButtonIcon>NOTHING</RawButtonIcon>				<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_DISPLAY_TIMER, *NONE*, CORONA_DISPLAY_LAUNCH</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
			<ContextMenu>
				<TriggerMenuId>MENU_UNIQUE_ID_PLAYERS_LIST</TriggerMenuId>
				<ContextMenuId>MENU_UNIQUE_ID_PLAYERS_OPTIONS_LIST</ContextMenuId>
				<depth>2</depth>
				
				<contextOptions>
					<contextOption>SEND_GAME_INVITE</contextOption>
					<contextOption>SEND_PARTY_INVITE</contextOption>
					<contextOption>SEND_CREW_INVITE</contextOption>
					<contextOption>JOIN_GAME</contextOption>
					<contextOption>JOIN_PARTY</contextOption>
					<contextOption>MUTE</contextOption>
					<contextOption>KICK_PARTY</contextOption>
					<contextOption>SHOW_GAMER_CARD</contextOption>
					<contextOption>GAMER_REVIEW</contextOption>
					<contextOption>SEND_MESSAGE</contextOption>
					<contextOption>ADD_A_FRIEND</contextOption>
				</contextOptions>
			</ContextMenu>
		</Item>
		
		<!--  ACTUAL SCREEN ID: 74 - USED FOR UNLOCKS -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_PARTY_LIST</MenuScreen>
			<runtime>
				<type>SCRIPT</type>
				<params>
					<data key="path">MP_Unlocks</data>
					<data key="continual">TRUE</data>
				</params>
			</runtime>
			
			<cGfxFilename>PAUSE_MENU_PAGES_STATS</cGfxFilename>		<!-- ??? -->
			<depth>1</depth>
			<!--<ScrollBarFlags>Width_2 Offset_1Right Arrows_UpDown Align_Right ManualUpdate InitiallyInvisible</ScrollBarFlags> -->	<!-- ??? -->
			<ButtonList>
				<ButtonPrompts IncludeDefaults="true" IncludeNavigate="false">			
					<!--<CMenuButton> <hButtonHash>IB_NAVIGATE</hButtonHash>	<RawButtonIcon>ARROW_ALL</RawButtonIcon><Contexts>*ALL*,IS_NAVIGATING_ALL,*NONE*,IS_NAVIGATING_UPDOWN</Contexts></CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT1</hButtonHash>	<RawButtonIcon>ARROW_UPDOWN</RawButtonIcon><Contexts>*ALL*,IS_NAVIGATING_UPDOWN,*NONE*,IS_NAVIGATING_ALL</Contexts></CMenuButton>-->
					<CMenuButton> <hButtonHash>PM_SCROLL</hButtonHash> <ButtonInput>INPUT_FRONTEND_RIGHT_AXIS_Y</ButtonInput> <Contexts>*ALL*,RScrollUpDown</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
			</MenuItems>
		</Item>
		
		<!--  ACTUAL SCREEN ID: ??? -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_SETTINGS_SIXAXIS</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns</Flags>
			<MenuItems>
				<Item> <cTextId>SIXAXIS_HELI</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_SIXAXIS_HELI</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>SIXAXIS_BIKE</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_SIXAXIS_BIKE</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>SIXAXIS_BOAT</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_SIXAXIS_BOAT</MenuPref> 			<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
				<Item> <cTextId>SIXAXIS_LOAD</cTextId>		<MenuAction>MENU_OPTION_ACTION_PREF_CHANGE</MenuAction>		<MenuPref>PREF_SIXAXIS_RELOAD</MenuPref> 		<MenuOption>MENU_OPTION_DISPLAY_ON_OFF</MenuOption>	  <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS_LIST</MenuUniqueId> </Item>
			</MenuItems>
		</Item>
		
		<!--  ACTUAL SCREEN ID: ??? -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HOME_OPEN_JOBS</MenuScreen>
			<depth>2</depth>
			<Flags>SF_NoClearRootColumns</Flags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
		</Item>

		<!--  RACE CORONA SCREENS  -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_RACE</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_SCR_VHI</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_RACE_INFO</MenuUniqueId> </Item>
				<Item> <cTextId>PM_EMPTY</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_RACE_INFO</MenuUniqueId> </Item>
				<Item> <cTextId>PM_EMPTY</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_RACE_INFO</MenuUniqueId> </Item>
			</MenuItems>
		</Item>

		<Item>
			<MenuScreen>MENU_UNIQUE_ID_RACE_INFO</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CORONA_RACE</cGfxFilename>
			<depth>1</depth>
			<Flags>Input_NoTabChange AlwaysUseButtons Input_NoBack Sound_NoAccept</Flags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
			</MenuItems>
			<ButtonList>
				<ButtonPrompts>
					<!--<CMenuButton> <hButtonHash>IB_ADJUST</hButtonHash>			<ButtonInputGroup>INPUTGROUP_FRONTEND_DPAD_LR</ButtonInputGroup>	<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS</Contexts> </CMenuButton>-->
					<CMenuButton> <hButtonHash>ITEM_MOV_CAM</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_RSTICK_ALL</ButtonInputGroup>		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, ROTATE_VEH</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>ITEM_MOV_CAM_MOUSE</hButtonHash>		<ButtonInputGroup>INPUTGROUP_CURSOR</ButtonInputGroup>		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, ROTATE_VEH_MOUSE</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT78</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_LB</ButtonInput> 		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, *NONE*, VIEW_CORONA_STATS, VIEWING_TUTORIAL_PED, VEHICLE_SCREEN, CORONA_HIDE_PLANNING</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT94</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_LB</ButtonInput> 		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, VIEW_CORONA_STATS, *NONE*, VEHICLE_SCREEN, CORONA_SHOW_PLANNING</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT69</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, *NONE*, FM_TUTORIAL</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT23</hButtonHash>		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CONFIRM_VEH</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT2</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CONTINUE_VEH, *NONE*, CORONA_SELECT, CORONA_HIDE_PLANNING</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT2</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_SELECT, *NONE*, CONTINUE_VEH, CORONA_HIDE_PLANNING</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT88</hButtonHash>		<ButtonInput>INPUT_FRONTEND_RB</ButtonInput>		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, VIEW_CORONA_STATS, VIEW_EXTRA_STATS</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT79</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, *NONE*, CORONA_THIRD_COLUMN, CORONA_HIDE_PLANNING</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT93</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_THIRD_COLUMN</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>PM_DYNAMICSTRING</hButtonHash>	<RawButtonIcon>NOTHING</RawButtonIcon>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_DISPLAY_TIMER</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT85</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_MAP_AVAIL, *NONE*</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT99</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_MAP_CLOSE, *NONE*</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_RANDOM</hButtonHash>			<ButtonInput>INPUT_FRONTEND_SELECT</ButtonInput>		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_RAND_VEH, *NONE*</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT91</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_TRIGGERS</ButtonInputGroup>	<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_ZOOM, *NONE*</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_SH_MASK</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_SHOW_MASK, *NONE*, CORONA_HIDE_PLANNING</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_HD_MASK</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_HIDE_MASK, *NONE*, CORONA_HIDE_PLANNING</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_SH_PLAN</hButtonHash>		<ButtonInput>INPUT_FRONTEND_RB</ButtonInput>		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_SHOW_PLANNING, *NONE*</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_HD_PLAN</hButtonHash>		<ButtonInput>INPUT_FRONTEND_RB</ButtonInput>		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_HIDE_PLANNING, *NONE*</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
		</Item>

		<!--  BETTING SCREENS  -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_BETTING</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_SCR_BET</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_BETTING</MenuUniqueId> </Item>
				<Item> <cTextId>PM_EMPTY</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_BETTING</MenuUniqueId> </Item>
				<Item> <cTextId>PM_EMPTY</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_BETTING</MenuUniqueId> </Item>
			</MenuItems>
		</Item>

		<Item>
			<MenuScreen>MENU_UNIQUE_ID_BETTING</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_CORONA_RACE</cGfxFilename>
			<depth>1</depth>
			<Flags>Input_NoTabChange AlwaysUseButtons Input_NoBack Sound_NoAccept</Flags>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT_FROM_SCRIPT</MenuAction> </Item>
			</MenuItems>
			<ButtonList>
				<ButtonPrompts>
					<!--<CMenuButton> <hButtonHash>IB_ADJUST</hButtonHash>			<ButtonInputGroup>INPUTGROUP_FRONTEND_DPAD_LR</ButtonInputGroup>	<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, *NONE*, BET_DISABLE_ADJ</Contexts> </CMenuButton>-->
					<!--<CMenuButton> <hButtonHash>HUD_INPUT1</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_DPAD_UD</ButtonInputGroup>		</CMenuButton>-->
					<CMenuButton> <hButtonHash>HUD_INPUT78</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_LB</ButtonInput> 		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, *NONE*, VIEW_CORONA_STATS, VIEWING_TUTORIAL_PED</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT94</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_LB</ButtonInput> 		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, VIEW_CORONA_STATS</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT69</hButtonHash> 		<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput> 		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, *NONE*, FM_TUTORIAL</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT2</hButtonHash>			<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, *NONE*, BET_LOCKED, BET_AVAILABLE, SCROLL_OPTION</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT76</hButtonHash>		<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, BET_AVAILABLE, *NONE*, BET_LOCKED</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT88</hButtonHash>		<ButtonInput>INPUT_FRONTEND_RB</ButtonInput>		<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, VIEW_CORONA_STATS, VIEW_EXTRA_STATS</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT92</hButtonHash>		<ButtonInput>INPUT_FRONTEND_X</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, PURCHASE_AMMO</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT79</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, *NONE*, CORONA_THIRD_COLUMN</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT93</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_THIRD_COLUMN</Contexts> </CMenuButton>			
					<CMenuButton> <hButtonHash>PM_DYNAMICSTRING</hButtonHash>	<RawButtonIcon>NOTHING</RawButtonIcon>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_DISPLAY_TIMER</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>ITEM_AMMO</hButtonHash>			<ButtonInput>INPUT_FRONTEND_RIGHT</ButtonInput>			<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, TOGGLE_AMMO</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>HUD_INPUT91</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_TRIGGERS</ButtonInputGroup>	<Contexts>*ALL*, DISPLAY_CORONA_BUTTONS, CORONA_ZOOM, *NONE*</Contexts> </CMenuButton>
				</ButtonPrompts>
			</ButtonList>
		</Item>

		<!-- <ScrollBarFlags>Width_1 DisplayOnlyOnFocus Offset_2Right</ScrollBarFlags> -->
		<!--  ACTUAL SCREEN ID: 9 -->
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_REPLAY_EDITOR</MenuScreen>
			<cGfxFilename>PAUSE_MENU_PAGES_STORE</cGfxFilename>
			<runtime>
				<type>VIDEOEDITORTAB</type>
			</runtime>
			<depth>1</depth>
			<Flags>LayoutChangedOnBack</Flags>
			
			<MenuItems>
				<Item><MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction></Item>
			</MenuItems>
			
			<ButtonList>
				<ButtonPrompts>
					<CMenuButton> <hButtonHash>HUD_INPUT1C</hButtonHash>		<ButtonInputGroup>INPUTGROUP_FRONTEND_BUMPERS</ButtonInputGroup> 	<Contexts>*NONE*,TABS_ARE_COLUMNS,NAVIGATING_CONTENT,MENU_OFF</Contexts>				</CMenuButton>
					<CMenuButton> <hButtonHash>IB_HIDEMENU</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> 						<Contexts>*ALL*,CAN_TOGGLE_MENU,		 *NONE*,MENU_OFF, NAVIGATING_CONTENT</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_SHOWMENU</hButtonHash>		<ButtonInput>INPUT_FRONTEND_Y</ButtonInput> 						<Contexts>*ALL*,CAN_TOGGLE_MENU,MENU_OFF,*NONE*,		  NAVIGATING_CONTENT</Contexts> </CMenuButton>
					<CMenuButton> <hButtonHash>IB_SELECT</hButtonHash>	<ButtonInput>INPUT_FRONTEND_ACCEPT</ButtonInput> 					<Contexts>*NONE*,MENU_OFF,HIDE_ACCEPTBUTTON</Contexts> 									</CMenuButton>
					<CMenuButton> <hButtonHash>IB_BACK</hButtonHash> 			<ButtonInput>INPUT_FRONTEND_CANCEL</ButtonInput>  																											</CMenuButton>
					<CMenuButton> <hButtonHash>IB_SCROLL</hButtonHash>			<ButtonInput>INPUT_FRONTEND_RIGHT_AXIS_Y</ButtonInput>				<Contexts>*ALL*,STORE_CanScroll,*NONE*,MENU_OFF</Contexts>								</CMenuButton>
				</ButtonPrompts>
			</ButtonList>
		</Item>

		<Item>
			<MenuScreen>MENU_UNIQUE_ID_HEADER_LANDING_PAGE</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_SCR_SET</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_SETTINGS</MenuUniqueId> </Item>
			</MenuItems>
		</Item>

		<Item platform="pc_only">
			<MenuScreen>MENU_UNIQUE_ID_HEADER_LANDING_KEYMAPPING</MenuScreen>
			<depth>0</depth>
			<MenuItems>
				<Item> <cTextId>PM_PANE_KEYS_UC</cTextId> <MenuAction>MENU_OPTION_ACTION_LINK</MenuAction> <MenuUniqueId>MENU_UNIQUE_ID_KEYMAP</MenuUniqueId> </Item>
			</MenuItems>
		</Item>
		
		<Item>
			<MenuScreen>MENU_UNIQUE_ID_PROCESS_SAVEGAME</MenuScreen>
			<depth>2</depth>
			<MenuItems>
				<Item> <MenuAction>MENU_OPTION_ACTION_FILL_CONTENT</MenuAction> </Item>
			</MenuItems>
			<Flags>SF_NoClearRootColumns</Flags>
		</Item>

		<Item>
			<MenuScreen>MENU_UNIQUE_ID_IMPORT_SAVEGAME</MenuScreen>
			<depth>2</depth>
			<MenuItems>
				<ScrollBarFlags>Width_1 ManualUpdate Align_Right InitiallyInvisible</ScrollBarFlags>
			</MenuItems>
			<Flags>SF_NoClearRootColumns</Flags>
		</Item>

		<!-- These are here to replace the metadata lost in the Great Scaleform Emigration -->
		<!-- these need to have everything as TRUE-->
		<Item>	<MenuScreen>MENU_UNIQUE_ID_CREWS_CARD</MenuScreen>				<Flags>NeverGenerateMenuData </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_SAVE_GAME_LIST</MenuScreen>			<Flags>NeverGenerateMenuData </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_PROCESS_SAVEGAME_LIST</MenuScreen>	<Flags>NeverGenerateMenuData </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_HOME</MenuScreen>					<Flags>NeverGenerateMenuData </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_HEADER_MY_MP</MenuScreen>			<Flags>NeverGenerateMenuData </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_FREEMODE</MenuScreen>				<Flags>NeverGenerateMenuData </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_CREATION_LIFESTYLE_LIST</MenuScreen>	<Flags>NeverGenerateMenuData SF_NoClearRootColumns</Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_LOST</MenuScreen>					<Flags>NeverGenerateMenuData </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_HOME_DIALOG</MenuScreen>				<Flags>NeverGenerateMenuData SF_NoClearRootColumns</Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_PLACEHOLDER</MenuScreen>				<Flags>NeverGenerateMenuData </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_VAGOS</MenuScreen>					<Flags>NeverGenerateMenuData </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_CREATION_HERITAGE_LIST</MenuScreen>	<Flags>NeverGenerateMenuData SF_NoClearRootColumns</Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_TEAM_SELECT</MenuScreen>				<Flags>NeverGenerateMenuData </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_LEAVE_GAME</MenuScreen>				<Flags>NeverGenerateMenuData </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_GALLERY_ITEM</MenuScreen>			<Flags>NeverGenerateMenuData </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_AVAILABLE</MenuScreen>				<Flags>NeverGenerateMenuData </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_COPS</MenuScreen>					<Flags>NeverGenerateMenuData </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_NEW_GAME</MenuScreen>				<Flags>NeverGenerateMenuData </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_MOVIE_EDITOR</MenuScreen>			<Flags>NeverGenerateMenuData </Flags>		</Item>		
		<Item>	<MenuScreen>MENU_UNIQUE_ID_SPECTATOR</MenuScreen>				<Flags>NeverGenerateMenuData </Flags>		</Item>
	<!--	<Item>  <MenuScreen>MENU_UNIQUE_ID_REPLAY_RANDOM</MenuScreen>			<Flags>NeverGenerateMenuData </Flags>		</Item>-->

		<!-- these need to have most things as false-->
		<Item>	<MenuScreen>MENU_UNIQUE_ID_STATS_CATEGORY</MenuScreen>			<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns</Flags>		</Item>
		
		<Item>	<MenuScreen>MENU_UNIQUE_ID_PLAYERS_LIST</MenuScreen>			<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_PLAYERS_OPTIONS</MenuScreen>			<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_PLAYERS_OPTIONS_LIST</MenuScreen>	<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_SETTINGS_LIST</MenuScreen>			<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_RACE_LOBBYLIST</MenuScreen>			<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_RACE_INFOLIST</MenuScreen>			<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_CORONA_DETAILS_LIST</MenuScreen>		<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_CORONA_JOINED_LIST</MenuScreen>		<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_CORONA_SETTINGS_LIST</MenuScreen>	<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_CORONA_INVITE_LIST</MenuScreen>		<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>

		<Item>	<MenuScreen>MENU_UNIQUE_ID_PARTY_OPTIONS</MenuScreen>			<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_PARTY_OPTIONS_LIST</MenuScreen>		<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_FRIENDS_OPTIONS_LIST</MenuScreen>	<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_LOBBY_LIST_ITEM</MenuScreen>			<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_BRIGHTNESS_CALIBRATION</MenuScreen>	<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_GALLERY_OPTIONS</MenuScreen>			<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_GALLERY_OPTIONS_LIST</MenuScreen>	<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_BETTING_INFOLIST</MenuScreen>		<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_BETTING_LOBBYLIST</MenuScreen>		<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>

		
		<Item>	<MenuScreen>MENU_UNIQUE_ID_INCEPT_TRIGGER</MenuScreen>			<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		
		<!-- are *probably* not used anywhere. Here in case they are
		<Item>	<MenuScreen>MENU_UNIQUE_ID_CREWS_CATEGORY</MenuScreen>			<Flags>NeverGenerateMenuData </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_PARTY_LIST</MenuScreen>				<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_FRIENDS_OPTIONS</MenuScreen>			<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		<Item>	<MenuScreen>MENU_UNIQUE_ID_CREWS_FILTER</MenuScreen>			<Flags>NeverGenerateMenuData SF_NoMenuAdvance SF_NoClearRootColumns </Flags>		</Item>
		-->
	</MenuScreens>
	
	<ContextMenuOptions>
		<Item>
			<contextOption>JOIN_GAME</contextOption>
			<text>CM_JOIN_GAME</text>
			<displayCondition>*ALL*, GameInvitePending, *NONE*, IsLocalPlayer, JIPLocked</displayCondition>
		</Item>

		<Item>
			<contextOption>JIP_GAME</contextOption>
			<text>CM_JIP_GAME</text>
			<displayCondition>*ANY*, IsFriendPlayingMP, IsPlatPartyInSession, OnlineCrew, *NONE*, InLocalSession, GameInvitePending, JIPLocked, IsLocalPlayer</displayCondition>
		</Item>

		<Item>
			<contextOption>JIP_GAME_DIS</contextOption>
			<text>CM_JIP_GAME_DIS</text>
			<displayCondition>*ANY*, IsFriendPlayingMP, IsPlatPartyInSession, OnlineCrew, *ALL*, JIPLocked, *NONE*, InLocalSession, GameInvitePending, IsLocalPlayer</displayCondition>
		</Item>
	
		<Item>
			<contextOption>JIP_GAME_TRANSITION</contextOption>
			<text>CM_JIP_GAME</text>
			<displayCondition>*ALL*, InLocalSession, PlayerInATransition, *NONE*, GameInvitePending, PlayerInLocalTransition, JIPLocked, IsLocalPlayer</displayCondition>
		</Item>		
	
		<Item>
			<contextOption>SPECTATE_OTHER_SESSION</contextOption>
			<text>CM_SPECTATE</text>
			<displayCondition>*NONE*, IsLocalPlayer, JIPLocked, RemoteInTutorialSession, DisableSpectate, DisableSpectateScript, LocalInActivitySession, RemoteIsSpectating, *ALL*, PlayerIsSameTitleOnline, RemoteAllowsSpectating</displayCondition>
<!--		<displayCondition>*ALL*,SHOULD_NEVER_MATCH</displayCondition>	-->
		</Item>
		
		<Item>
			<contextOption>SPECTATE_OTHER_SESSION_DISABLED</contextOption>
			<text>CM_SPECTATE_DISABLED</text>
			<displayCondition>*NONE*, IsLocalPlayer, JIPLocked, RemoteInTutorialSession, DisableSpectate, DisableSpectateScript, LocalInActivitySession, RemoteIsSpectating, RemoteAllowsSpectating, *ALL*, PlayerIsSameTitleOnline</displayCondition>
			<bIsSelectable value="false"/>
		</Item>
		
		<Item>
			<contextOption>JOIN_PARTY</contextOption>
			<text>CM_JOIN_PARTY</text>
			<displayCondition>*ALL*, PartyInvitePending, *NONE*, IsLocalPlayer</displayCondition>
		</Item>

		<Item>
			<contextOption>SEND_MESSAGE</contextOption>
			<text>CM_SEND_MESSAGE</text>
			<displayCondition>*ALL*, CanSendMessage, *NONE*, IsLocalPlayer, NO_RESULTS</displayCondition>
		</Item>

		<Item>
			<contextOption>SEND_FRIEND_INVITE</contextOption>
			<text>CM_SEND_FRIEND_INVITE</text>
			<displayCondition>*ALL*, CanFriend, *NONE*, durango, IsLocalPlayer</displayCondition>
		</Item>

		<Item>
			<contextOption>SEND_CREW_INVITE</contextOption>
			<text>CM_SEND_CREW_INVITE</text>
			<displayCondition>*ALL*, CanInviteToCrew, *NONE*, IsLocalPlayer, DisableCrewInvite</displayCondition>
		</Item>

		<Item>
			<contextOption>SEND_PARTY_INVITE</contextOption>
			<text>CM_SEND_PARTY_INVITE</text>
<!--			<displayCondition>*ALL*, InMP, PlayerIsOnline, *NONE*, IsLocalPlayer, IsMemberOfParty, Xbox360</displayCondition>-->
			<displayCondition>*ALL*,SHOULD_NEVER_MATCH</displayCondition>
		</Item>

		<Item>
			<contextOption>KICK_PARTY</contextOption>
			<text>CM_KICK_PARTY</text>
<!--			<displayCondition>*ALL*, IsMemberOfParty, PartyLeader, *NONE*, IsLocalPlayer, Xbox360</displayCondition>-->
			<displayCondition>*ALL*,SHOULD_NEVER_MATCH</displayCondition>
		</Item>

		<Item>
			<contextOption>SEND_GAME_INVITE</contextOption>
			<text>CM_SEND_GAME_INVITE</text>
			<displayCondition>*ALL*, InMP, PlayerIsOnline, *NONE*, Lobby, InLocalSession, IsLocalPlayer, JIPLocked</displayCondition>
		</Item>

		<Item>
			<contextOption>SEND_GAME_INVITE_DIS</contextOption>
			<text>CM_SEND_GAME_INVITE_DIS</text>
			<displayCondition>*ALL*, InMP, PlayerIsOnline, JIPLocked, *NONE*, Lobby, InLocalSession, IsLocalPlayer</displayCondition>
		</Item>
	
		<Item>
			<contextOption>KICK</contextOption>
			<text>CM_KICK</text>
			<displayCondition>*ALL*, InMP, IsKickEnabled, CanKickVote, InLocalSession, *NONE*, IsLocalPlayer, InKickCooldown, InTournament, RemoteIsRockstarDev</displayCondition>
		</Item>

		<Item>
			<contextOption>CORONA_KICK</contextOption>
			<text>CM_KICK</text>
			<displayCondition>*ALL*, IsInTransition, *NONE*, IsLocalPlayer, IsKickForceHidden</displayCondition>
		</Item>
		
		<Item>
			<contextOption>KICK_DISABLED</contextOption>
			<text>CM_KICK_DIS</text>
			<displayCondition>*ALL*, InMP, IsKickEnabled, CanKickVote, InKickCooldown, *NONE*, IsLocalPlayer, InTournament</displayCondition>
		</Item>		
		
		<Item>
			<contextOption>UNKICK</contextOption>
			<text>CM_UNKICK</text>
			<displayCondition>*ALL*, InMP, IsKickEnabled, InLocalSession, *NONE*, CanKickVote, *NONE*, IsLocalPlayer</displayCondition>
		</Item>

		<Item>
			<contextOption>REPORT</contextOption>
			<text>CM_REPORT</text>
			<displayCondition>*ALL*, InMP, *NONE*, DisableReport, IsLocalPlayer, InReportCooldown, IsSpectating, LocalIsBadSport</displayCondition>
		</Item>

		<Item>
			<contextOption>REPORT_DISABLED</contextOption>
			<text>CM_REPORT_DIS</text>
			<displayCondition>*ALL*, InMP, *ANY*, DisableReport, InReportCooldown, IsSpectating, *NONE*,IsLocalPlayer, LocalIsBadSport</displayCondition>
    </Item>		
	
		<Item>
			<contextOption>COMMEND</contextOption>
			<text>CM_COMMEND</text>
			<displayCondition>*ALL*, InMP, *NONE*, DisableCommend, IsLocalPlayer, InCommendCooldown, LocalIsBadSport</displayCondition>
		</Item>

		<Item>
			<contextOption>COMMEND_DISABLED</contextOption>
			<text>CM_COMMEND_DIS</text>
			<displayCondition>*ALL*, InMP, *ANY*, DisableCommend, InCommendCooldown, *NONE*, IsLocalPlayer, LocalIsBadSport</displayCondition>
    </Item>	

		<Item>
			<contextOption>MUTE</contextOption>
			<text>CM_MUTE</text>
			<displayCondition>*ALL*, InMP, CanMute, InLocalSession, *NONE*, IsLocalPlayer,MutedByOS</displayCondition>
		</Item>

		<Item>
			<contextOption>SHOW_GAMER_CARD</contextOption>
			<text>CM_SHOW_GAMER_CARD</text>
			<displayCondition>*ANY*, IsFriend, InLocalSession, IsLocalPlayer, Xbox360, PC, x64, ShowGamerCard, *NONE*, NO_RESULTS</displayCondition>
		</Item>

		<Item>
			<contextOption>ADD_A_FRIEND</contextOption>
			<text>CM_ADD_A_FRIEND</text>
			<displayCondition>*ALL*, Xbox360, NO_RESULTS</displayCondition>
		</Item>

		<Item>
			<contextOption>GAMER_REVIEW</contextOption>
			<text>CM_GAMER_REVIEW</text>
			<displayCondition>*ALL*, Xbox360, *NONE*, IsLocalPlayer, NO_RESULTS</displayCondition>
		</Item>
		
		<Item>
			<contextOption>JOIN_CREW</contextOption>
			<text>IB_JOINCREW</text>
			<displayCondition>*NONE*, IN_CREW, VARIANT_INVITE,*ALL*,CAN_JOIN</displayCondition>
		</Item>
		<Item>
			<contextOption>LEAVE_CREW</contextOption>
			<text>IB_LEAVECREW</text>
			<displayCondition>*ANY*, IN_CREW</displayCondition>
		</Item>
		<Item>
			<contextOption>REQUEST_INVITE</contextOption>
			<text>IB_REQUESTINVITE</text>
			<displayCondition>*NONE*, IN_CREW, VARIANT_INVITE,*ALL*,CAN_REQUEST</displayCondition>
		</Item>
		<Item>
			<contextOption>SET_PRIMARY</contextOption>
			<text>IB_SETPRIMARY</text>
			<displayCondition>*ANY*, SET_AS_PRIMARY_CREW</displayCondition>
		</Item>
		<Item>
			<contextOption>VIEW_MEMBERS</contextOption>
			<text>IB_VIEWMEMBERS</text>
			<displayCondition>*NONE*, HAS_NO_MEMBERS</displayCondition>
		</Item>
		<Item>
			<contextOption>INVITE_ACCEPT</contextOption>
			<text>FE_HLP48</text>
			<displayCondition>*NONE*,NO_RESULTS,*ALL*,VARIANT_INVITE</displayCondition>
		</Item>
		<Item>
			<contextOption>INVITE_DECLINE</contextOption>
			<text>FE_HLP49</text>
			<displayCondition>*NONE*,NO_RESULTS,*ALL*,VARIANT_INVITE</displayCondition>
		</Item>
		
		<Item>
			<contextOption>REQUEST_ACCEPT</contextOption>
			<text>CRW_REQACCEPT</text>
			<displayCondition>*NONE*,CREW_RequestHandled</displayCondition>
		</Item>

		<Item>
			<contextOption>REQUEST_REJECT</contextOption>
			<text>CRW_REQDENY</text>
			<displayCondition>*NONE*,CREW_RequestHandled</displayCondition>
		</Item>
	 
	</ContextMenuOptions>

</CMenuArray>
