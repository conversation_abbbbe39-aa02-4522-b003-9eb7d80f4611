<?xml version="1.0" encoding="UTF-8"?>

<rage__ControlInput__ControlSettings>
	<Mappings>
		<Item>
			<Input>INPUT_NEXT_CAMERA</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_V</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_INTERACTION_MENU</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_M</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_WEAPON_WHEEL_NEXT</Input>
			<Source>IOMS_MOUSE_WHEEL</Source>
			<Parameters>
				<Item>IOM_WHEEL_DOWN</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_WEAPON_WHEEL_PREV</Input>
			<Source>IOMS_MOUSE_WHEEL</Source>
			<Parameters>
				<Item>IOM_WHEEL_UP</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_DROP_WEAPON</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_F9</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_DROP_AMMO</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_F10</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SELECT_NEXT_WEAPON</Input>
			<Source>IOMS_MOUSE_WHEEL</Source>
			<Parameters>
				<Item>IOM_WHEEL_DOWN</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SELECT_PREV_WEAPON</Input>
			<Source>IOMS_MOUSE_WHEEL</Source>
			<Parameters>
				<Item>IOM_WHEEL_UP</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CHARACTER_WHEEL</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_LMENU</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SPRINT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_LSHIFT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_JUMP</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_SPACE</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_ENTER</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_F</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_ATTACK</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_LEFT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_AIM</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_RIGHT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_LOOK_BEHIND</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_C</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_PHONE</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_UP</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_PHONE</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_MIDDLE</Item>
			</Parameters>
		</Item>  
		<Item>
			<Input>INPUT_MOVE_UP_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_W</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_MOVE_DOWN_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_S</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_MOVE_LEFT_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_A</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_MOVE_RIGHT_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_D</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_DUCK</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_LCONTROL</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_DUCK</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_X</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SELECT_WEAPON</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_TAB</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SNIPER_ZOOM_IN_ONLY</Input>
			<Source>IOMS_MOUSE_WHEEL</Source>
			<Parameters>
				<Item>IOM_WHEEL_UP</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SNIPER_ZOOM_IN_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_RBRACKET</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SNIPER_ZOOM_OUT_ONLY</Input>
			<Source>IOMS_MOUSE_WHEEL</Source>
			<Parameters>
				<Item>IOM_WHEEL_DOWN</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SNIPER_ZOOM_OUT_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_LBRACKET</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_COVER</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_Q</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_RELOAD</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_R</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_DETONATE</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_G</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_HUD_SPECIAL</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_Z</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_ARREST</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_F</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CONTEXT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_E</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_MOVE_UP_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_LSHIFT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_MOVE_UP_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_NUMPAD8</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_MOVE_DOWN_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_LCONTROL</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_MOVE_DOWN_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_NUMPAD5</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_MOVE_LEFT_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_A</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_MOVE_RIGHT_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_D</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_AIM</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_RIGHT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_ATTACK</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_LEFT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_ATTACK2</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_RCONTROL</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_ATTACK2</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_RIGHT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_ACCELERATE</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_W</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_PUSHBIKE_SPRINT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_CAPITAL</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_PUSHBIKE_FRONT_BRAKE</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_Q</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_BRAKE</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_S</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_HEADLIGHT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_H</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SWITCH_VISOR</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_F11</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_EXIT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_F</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_HANDBRAKE</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_SPACE</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_HOTWIRE_LEFT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_W</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_HOTWIRE_RIGHT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_S</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_LOOK_BEHIND</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_C</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_CIN_CAM</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_R</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_NEXT_RADIO</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_PERIOD</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SELECT_NEXT_WEAPON</Input>
			<Source>IOMS_MOUSE_WHEEL</Source>
			<Parameters>
				<Item>IOM_WHEEL_UP</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_PREV_RADIO</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_COMMA</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_RADIO_WHEEL</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_Q</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_NEXT_RADIO</Input>
			<Source>IOMS_MOUSE_WHEEL</Source>
			<Parameters>
				<Item>IOM_WHEEL_DOWN</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_HORN</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_E</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_BOOST</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_LSHIFT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_THROTTLE_UP</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_W</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_THROTTLE_DOWN</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_S</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_YAW_LEFT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_A</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_YAW_RIGHT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_D</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SELECT_NEXT_WEAPON</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_TAB</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SELECT_PREV_WEAPON</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_LBRACKET</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_GRAPPLING_HOOK</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_E</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_CINEMATIC_UP_ONLY</Input>
			<Source>IOMS_MOUSE_WHEEL</Source>
			<Parameters>
				<Item>IOM_WHEEL_UP</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_CINEMATIC_UP_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_ADD</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_CINEMATIC_DOWN_ONLY</Input>
			<Source>IOMS_MOUSE_WHEEL</Source>
			<Parameters>
				<Item>IOM_WHEEL_DOWN</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_CINEMATIC_DOWN_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_SUBTRACT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SLOWMO_UP_ONLY</Input>
			<Source>IOMS_MOUSE_WHEEL</Source>
			<Parameters>
				<Item>IOM_WHEEL_UP</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SLOWMO_UP_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_PAGEUP</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SLOWMO_DOWN_ONLY</Input>
			<Source>IOMS_MOUSE_WHEEL</Source>
			<Parameters>
				<Item>IOM_WHEEL_DOWN</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SLOWMO_DOWN_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_PAGEDOWN</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CINEMATIC_SLOWMO</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_L</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_ROLL_LEFT_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_NUMPAD4</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_ROLL_RIGHT_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_NUMPAD6</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_PITCH_UP_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_NUMPAD8</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_PITCH_DOWN_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_NUMPAD5</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_UNDERCARRIAGE</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_G</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_ATTACK</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_SPACE</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_ATTACK</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_RIGHT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_SELECT_TARGET_LEFT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_NUMPAD7</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_SELECT_TARGET_RIGHT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_NUMPAD9</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SUB_TURN_HARD_LEFT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_A</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SUB_TURN_LEFT_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_NUMPAD4</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SUB_TURN_HARD_RIGHT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_D</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SUB_TURN_RIGHT_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_NUMPAD6</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SUB_PITCH_UP_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_NUMPAD8</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SUB_PITCH_DOWN_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_NUMPAD5</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SUB_THROTTLE_UP</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_W</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SUB_THROTTLE_DOWN</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_S</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SUB_ASCEND</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_LSHIFT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SUB_DESCEND</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_LCONTROL</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_MELEE_ATTACK_LIGHT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_R</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_MELEE_ATTACK_HEAVY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_Q</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_MELEE_BLOCK</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_SPACE</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_PARACHUTE_DEPLOY</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_LEFT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_PARACHUTE_DEPLOY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_F</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_PARACHUTE_DETACH</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_F</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_PARACHUTE_TURN_LEFT_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_A</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_PARACHUTE_TURN_RIGHT_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_D</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_PARACHUTE_PITCH_UP_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_W</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_PARACHUTE_PITCH_DOWN_ONLY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_S</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_PARACHUTE_BRAKE_LEFT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_Q</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_PARACHUTE_BRAKE_RIGHT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_E</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_PARACHUTE_SMOKE</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_X</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_PARACHUTE_PRECISION_LANDING</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_LSHIFT</Item>
			</Parameters>
		</Item>
		<!-- Don't think this is needed as pause screen starts off on map, plus I need the key for the multiplayer info
		<Item>
			<Input>INPUT_MAP</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_M</Item>
			</Parameters>
		</Item>
		-->
		<Item>
			<Input>INPUT_SELECT_WEAPON_UNARMED</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_1</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SELECT_WEAPON_MELEE</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_2</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SELECT_WEAPON_SHOTGUN</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_3</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SELECT_WEAPON_HEAVY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_4</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SELECT_WEAPON_SPECIAL</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_5</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SELECT_WEAPON_HANDGUN</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_6</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SELECT_WEAPON_SMG</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_7</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SELECT_WEAPON_AUTO_RIFLE</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_8</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SELECT_WEAPON_SNIPER</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_9</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_WEAPON_SPECIAL_TWO</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_E</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SELECT_CHARACTER_MICHAEL</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_F5</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SELECT_CHARACTER_FRANKLIN</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_F6</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SELECT_CHARACTER_TREVOR</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_F7</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SELECT_CHARACTER_MULTIPLAYER</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_F8</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_REPLAY_START_STOP_RECORDING</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_F1</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_REPLAY_START_STOP_RECORDING_SECONDARY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_F2</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SAVE_REPLAY_CLIP</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_F3</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SPECIAL_ABILITY_PC</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_CAPITAL</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_UP</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_UP</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_DOWN</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_DOWN</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_LEFT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_LEFT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_RIGHT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_RIGHT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_SELECT</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_RETURN</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_SELECT</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_LEFT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_CANCEL</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_BACK</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_CANCEL</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_RIGHT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_OPTION</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_DELETE</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_EXTRA_OPTION</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_SPACE</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_CAMERA_FOCUS_LOCK</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_L</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_CAMERA_GRID</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_G</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_CAMERA_SELFIE</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_MIDDLE</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_CAMERA_SELFIE</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_E</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_CAMERA_DOF</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_F</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_CAMERA_EXPRESSION</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_X</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_EXTRA_OPTION</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_MIDDLE</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_SCROLL_BACKWARD</Input>
			<Source>IOMS_MOUSE_WHEEL</Source>
			<Parameters>
				<Item>IOM_WHEEL_UP</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_CELLPHONE_SCROLL_FORWARD</Input>
			<Source>IOMS_MOUSE_WHEEL</Source>
			<Parameters>
				<Item>IOM_WHEEL_DOWN</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_MP_TEXT_CHAT_ALL</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_T</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_MP_TEXT_CHAT_TEAM</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_Y</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_PUSH_TO_TALK</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_N</Item>
			</Parameters>
		</Item>
		<Item>	
			<Input>INPUT_VEH_DRIVE_LOOK</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_LEFT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_DRIVE_LOOK2</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_RIGHT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_ATTACK2</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_SPACE</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_ATTACK2</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_RIGHT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_SPECIAL_ABILITY_SECONDARY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_B</Item>
			</Parameters>
		</Item>	
		<Item>
			<Input>INPUT_FRONTEND_PAUSE</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_P</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_ATTACK_CAMERA</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_INSERT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_MOUSE_CONTROL_OVERRIDE</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_LEFT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_MOUSE_CONTROL_OVERRIDE</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_LEFT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_SUB_MOUSE_CONTROL_OVERRIDE</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_LEFT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_NEXT_RADIO_TRACK</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_PLUS</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_PREV_RADIO_TRACK</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_MINUS</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_FRONTEND_SOCIAL_CLUB_SECONDARY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_HOME</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_HYDRAULICS_CONTROL_TOGGLE</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_X</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_BIKE_WINGS</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_X</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_TRANSFORM</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_X</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_MELEE_HOLD</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_X</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_CAR_JUMP</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_E</Item>
			</Parameters>
		</Item>	
		<Item>
			<Input>INPUT_VEH_ROCKET_BOOST</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_E</Item>
			</Parameters>
		</Item>	
		<Item>
			<Input>INPUT_VEH_PARACHUTE</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_SPACE</Item>
			</Parameters>
		</Item>	
		<Item>
			<Input>INPUT_VEH_MELEE_RIGHT</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_RIGHT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_MELEE_LEFT</Input>
			<Source>IOMS_MOUSE_BUTTON</Source>
			<Parameters>
				<Item>MOUSE_LEFT</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_BOMB_BAY</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_E</Item>
			</Parameters>
		</Item>
		<Item>
			<Input>INPUT_VEH_FLY_COUNTER</Input>
			<Source>IOMS_KEYBOARD</Source>
			<Parameters>
				<Item>KEY_E</Item>
			</Parameters>
		</Item>	
	</Mappings>
</rage__ControlInput__ControlSettings>