# File Server API

Server API Node.js để phục vụ files từ thư mục `files` với đầy đủ cấu trúc thư mục.

## Cài đặt

1. Cài đặt dependencies:
```bash
npm install
```

2. Chạy server:
```bash
npm start
```

Hoặc chạy ở chế độ development:
```bash
npm run dev
```

Server sẽ chạy trên port 3000 (hoặc port được định nghĩa trong biến môi trường PORT).

## API Endpoints

### 1. Health Check
- **GET** `/api/health`
- Kiểm tra server có hoạt động không

### 2. Lấy danh sách tất cả files và folders
- **GET** `/api/files`
- Trả về danh sách tất cả files và folders trong thư mục `files` với cấu trúc đầy đủ

Response example:
```json
{
  "success": true,
  "message": "Files retrieved successfully",
  "data": {
    "totalItems": 150,
    "items": [
      {
        "type": "file",
        "name": "CitiLaunch_TLSDummy.dll",
        "path": "CitiLaunch_TLSDummy.dll",
        "size": 1024,
        "modified": "2024-01-01T00:00:00.000Z"
      },
      {
        "type": "directory",
        "name": "bin",
        "path": "bin"
      }
    ]
  }
}
```

### 3. Tạo thư mục test và lấy files
- **POST** `/api/create-test-folder`
- Tạo thư mục `test` và trả về danh sách tất cả files từ thư mục `files`

### 4. Tải xuống file cụ thể (qua API)
- **GET** `/api/download/:path`
- Tải xuống một file cụ thể qua API
- Example: `/api/download/bin/some-file.dll`

### 5. Truy cập file trực tiếp (Static Files)
- **GET** `/files/:path`
- Truy cập và tải xuống file trực tiếp từ thư mục files
- Example: `/files/CitiLaunch_TLSDummy.dll`
- Giữ nguyên cấu trúc thư mục gốc

### 6. Browse thư mục/file
- **GET** `/api/browse/:path`
- Browse nội dung thư mục hoặc download file
- Nếu là thư mục: trả về danh sách files/folders bên trong
- Nếu là file: download trực tiếp
- Example: `/api/browse/bin` hoặc `/api/browse/some-file.dll`

### 7. Tải xuống tất cả files dưới dạng ZIP
- **GET** `/api/download-all-zip`
- Tải xuống tất cả files và folders trong thư mục `files` dưới dạng file ZIP
- Giữ nguyên cấu trúc thư mục

## Cách sử dụng từ Client

### JavaScript/Fetch API:

```javascript
// Tạo thư mục test và lấy danh sách files
async function createTestAndGetFiles() {
  try {
    const response = await fetch('http://localhost:3000/api/create-test-folder', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    console.log('Files:', data.data.items);
    return data.data.items;
  } catch (error) {
    console.error('Error:', error);
  }
}

// Lấy danh sách files
async function getFiles() {
  try {
    const response = await fetch('http://localhost:3000/api/files');
    const data = await response.json();
    return data.data.items;
  } catch (error) {
    console.error('Error:', error);
  }
}

// Tải xuống file cụ thể qua API
function downloadFileViaAPI(filePath) {
  window.open(`http://localhost:3000/api/download/${filePath}`, '_blank');
}

// Tải xuống file trực tiếp
function downloadFileDirect(filePath) {
  window.open(`http://localhost:3000/files/${filePath}`, '_blank');
}

// Browse thư mục
async function browseDirectory(dirPath) {
  const response = await fetch(`http://localhost:3000/api/browse/${dirPath}`);
  const data = await response.json();
  return data.data.items;
}

// Tải xuống tất cả files dưới dạng ZIP
function downloadAllFilesZip() {
  window.open('http://localhost:3000/api/download-all-zip', '_blank');
}
```

### cURL Examples:

```bash
# Health check
curl http://localhost:3000/api/health

# Lấy danh sách files
curl http://localhost:3000/api/files

# Tạo thư mục test và lấy files
curl -X POST http://localhost:3000/api/create-test-folder

# Tải xuống file cụ thể qua API
curl -O http://localhost:3000/api/download/CitiLaunch_TLSDummy.dll

# Tải xuống file trực tiếp
curl -O http://localhost:3000/files/CitiLaunch_TLSDummy.dll

# Browse thư mục
curl http://localhost:3000/api/browse/bin

# Browse và download file
curl -O http://localhost:3000/api/browse/CitiLaunch_TLSDummy.dll

# Tải xuống tất cả files dưới dạng ZIP
curl -O http://localhost:3000/api/download-all-zip
```

## Cấu trúc Project

```
.
├── server.js          # Main server file
├── package.json       # Dependencies và scripts
├── README.md         # Documentation
├── files/            # Thư mục chứa files cần phục vụ
└── test/             # Thư mục được tạo khi gọi API
```

## Features

- ✅ Lấy danh sách tất cả files và folders với cấu trúc đầy đủ
- ✅ Tải xuống file cụ thể qua API hoặc trực tiếp
- ✅ Truy cập files trực tiếp qua static file serving
- ✅ Browse thư mục và download files nguyên bản
- ✅ Tải xuống tất cả files dưới dạng ZIP (optional)
- ✅ Tạo thư mục test theo yêu cầu
- ✅ CORS enabled cho cross-origin requests
- ✅ Error handling đầy đủ
- ✅ Giữ nguyên cấu trúc thư mục gốc
- ✅ Multiple download methods (API, Direct, Browse)
