<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Server API Client Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background-color: #0056b3;
        }

        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        .file-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
        }

        .file-item {
            padding: 5px;
            margin: 2px 0;
            border-radius: 3px;
            cursor: pointer;
        }

        .file-item:hover {
            background-color: #e9ecef;
        }

        .file-item.directory {
            background-color: #e3f2fd;
            font-weight: bold;
        }

        .file-item.file {
            background-color: #f3e5f5;
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }

        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>

<body>
    <h1>File Server API Client Example</h1>

    <div class="container">
        <h2>Server Controls</h2>
        <button onclick="checkHealth()">Check Server Health</button>
        <button onclick="createTestFolder()">Create Test Folder & Get Files</button>
        <button onclick="getFiles()">Get Files List</button>
        <button onclick="downloadAllFilesZip()">Download All Files (ZIP)</button>
        <button onclick="openFilesDirectory()">Browse Files Directory</button>

        <div id="status"></div>
    </div>

    <div class="container">
        <h2>Files & Folders</h2>
        <div id="fileCount"></div>
        <div id="fileList" class="file-list"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        let currentFiles = [];

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function checkHealth() {
            try {
                showStatus('Checking server health...', 'info');
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();

                if (data.success) {
                    showStatus(`✅ Server is running! Timestamp: ${data.timestamp}`, 'success');
                } else {
                    showStatus('❌ Server health check failed', 'error');
                }
            } catch (error) {
                showStatus(`❌ Error connecting to server: ${error.message}`, 'error');
            }
        }

        async function createTestFolder() {
            try {
                showStatus('Creating test folder and getting files...', 'info');
                const response = await fetch(`${API_BASE}/create-test-folder`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    currentFiles = data.data.items;
                    displayFiles(currentFiles);
                    showStatus(`✅ Test folder created! Found ${data.data.totalItems} items`, 'success');
                } else {
                    showStatus(`❌ Error: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function getFiles() {
            try {
                showStatus('Getting files list...', 'info');
                const response = await fetch(`${API_BASE}/files`);
                const data = await response.json();

                if (data.success) {
                    currentFiles = data.data.items;
                    displayFiles(currentFiles);
                    showStatus(`✅ Found ${data.data.totalItems} items`, 'success');
                } else {
                    showStatus(`❌ Error: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ Error: ${error.message}`, 'error');
            }
        }

        function displayFiles(files) {
            const fileListDiv = document.getElementById('fileList');
            const fileCountDiv = document.getElementById('fileCount');

            fileCountDiv.innerHTML = `<strong>Total items: ${files.length}</strong>`;

            if (files.length === 0) {
                fileListDiv.innerHTML = '<p>No files found</p>';
                return;
            }

            let html = '';
            files.forEach(file => {
                const icon = file.type === 'directory' ? '📁' : '📄';
                const sizeInfo = file.type === 'file' ? ` (${formatFileSize(file.size)})` : '';

                html += `
                    <div class="file-item ${file.type}" onclick="handleFileClick('${file.path}', '${file.type}')">
                        ${icon} ${file.path}${sizeInfo}
                    </div>
                `;
            });

            fileListDiv.innerHTML = html;
        }

        function handleFileClick(filePath, fileType) {
            if (fileType === 'file') {
                const options = [
                    'Download via API',
                    'Download Direct Link',
                    'Browse via API'
                ];

                const choice = prompt(`Choose download method for: ${filePath}\n\n1. ${options[0]}\n2. ${options[1]}\n3. ${options[2]}\n\nEnter 1, 2, or 3:`);

                switch (choice) {
                    case '1':
                        downloadFileViaAPI(filePath);
                        break;
                    case '2':
                        downloadFileDirectLink(filePath);
                        break;
                    case '3':
                        browseFile(filePath);
                        break;
                    default:
                        showStatus('Download cancelled', 'info');
                }
            } else {
                if (confirm(`Browse directory: ${filePath}?`)) {
                    browseDirectory(filePath);
                }
            }
        }

        function downloadFileViaAPI(filePath) {
            try {
                showStatus(`Downloading via API: ${filePath}`, 'info');
                window.open(`${API_BASE}/download/${filePath}`, '_blank');
                showStatus(`✅ Download started: ${filePath}`, 'success');
            } catch (error) {
                showStatus(`❌ Download error: ${error.message}`, 'error');
            }
        }

        function downloadFileDirectLink(filePath) {
            try {
                showStatus(`Downloading direct: ${filePath}`, 'info');
                window.open(`${API_BASE.replace('/api', '')}/files/${filePath}`, '_blank');
                showStatus(`✅ Direct download started: ${filePath}`, 'success');
            } catch (error) {
                showStatus(`❌ Download error: ${error.message}`, 'error');
            }
        }

        function browseFile(filePath) {
            try {
                showStatus(`Browsing: ${filePath}`, 'info');
                window.open(`${API_BASE}/browse/${filePath}`, '_blank');
            } catch (error) {
                showStatus(`❌ Browse error: ${error.message}`, 'error');
            }
        }

        async function browseDirectory(dirPath) {
            try {
                showStatus(`Browsing directory: ${dirPath}`, 'info');
                const response = await fetch(`${API_BASE}/browse/${dirPath}`);
                const data = await response.json();

                if (data.success) {
                    currentFiles = data.data.items;
                    displayFiles(currentFiles);
                    showStatus(`✅ Browsed directory: ${dirPath} (${data.data.totalItems} items)`, 'success');
                } else {
                    showStatus(`❌ Error browsing directory: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ Browse error: ${error.message}`, 'error');
            }
        }

        function downloadAllFilesZip() {
            try {
                showStatus('Downloading all files as ZIP...', 'info');
                window.open(`${API_BASE}/download-all-zip`, '_blank');
                showStatus('✅ ZIP download started', 'success');
            } catch (error) {
                showStatus(`❌ Download error: ${error.message}`, 'error');
            }
        }

        function openFilesDirectory() {
            try {
                showStatus('Opening files directory...', 'info');
                window.open(`${API_BASE.replace('/api', '')}/files/`, '_blank');
                showStatus('✅ Files directory opened', 'success');
            } catch (error) {
                showStatus(`❌ Error: ${error.message}`, 'error');
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Auto check health when page loads
        window.onload = function () {
            checkHealth();
        };
    </script>
</body>

</html>