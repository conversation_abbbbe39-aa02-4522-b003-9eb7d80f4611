const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');

const API_BASE = 'http://localhost:3000';
const DOWNLOAD_DIR = './downloaded_files';

// <PERSON><PERSON><PERSON> thư mục download nếu chưa có
if (!fs.existsSync(DOWNLOAD_DIR)) {
    fs.mkdirSync(DOWNLOAD_DIR, { recursive: true });
    console.log(`✅ Created download directory: ${DOWNLOAD_DIR}`);
}

// Function để download file
function downloadFile(url, outputPath) {
    return new Promise((resolve, reject) => {
        const protocol = url.startsWith('https') ? https : http;
        
        // Tạo thư mục cha nếu chưa có
        const dir = path.dirname(outputPath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        
        const file = fs.createWriteStream(outputPath);
        
        protocol.get(url, (response) => {
            if (response.statusCode === 200) {
                response.pipe(file);
                
                file.on('finish', () => {
                    file.close();
                    const stats = fs.statSync(outputPath);
                    resolve({
                        success: true,
                        size: stats.size,
                        path: outputPath
                    });
                });
            } else {
                file.close();
                if (fs.existsSync(outputPath)) {
                    fs.unlinkSync(outputPath);
                }
                reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
            }
        }).on('error', (err) => {
            file.close();
            if (fs.existsSync(outputPath)) {
                fs.unlinkSync(outputPath);
            }
            reject(err);
        });
    });
}

// Function để lấy danh sách tất cả files
async function getAllFilesForDownload() {
    return new Promise((resolve, reject) => {
        http.get(`${API_BASE}/api/download-all-files`, (response) => {
            let data = '';
            
            response.on('data', (chunk) => {
                data += chunk;
            });
            
            response.on('end', () => {
                try {
                    const result = JSON.parse(data);
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            });
        }).on('error', reject);
    });
}

// Function để tạo thư mục
function createDirectories(directories) {
    console.log('📁 Creating directories...');
    let createdCount = 0;
    
    directories.forEach(dir => {
        const dirPath = path.join(DOWNLOAD_DIR, dir.path);
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            createdCount++;
        }
    });
    
    console.log(`✅ Created ${createdCount} directories`);
}

// Main function
async function downloadAllFiles() {
    console.log('🚀 Starting download of ALL files and directories...\n');
    
    try {
        // 1. Lấy danh sách tất cả files
        console.log('📋 Getting complete files list...');
        const response = await getAllFilesForDownload();
        
        if (!response.success) {
            throw new Error('Failed to get files list');
        }
        
        const { files, directories, totalFiles, totalDirectories } = response.data;
        
        console.log(`✅ Found ${totalFiles} files and ${totalDirectories} directories\n`);
        
        // 2. Tạo tất cả thư mục trước
        createDirectories(directories);
        console.log('');
        
        // 3. Download tất cả files
        console.log('🔽 Starting file downloads...');
        
        let successCount = 0;
        let failCount = 0;
        let totalSize = 0;
        
        // Download files theo batch để không quá tải server
        const batchSize = 5; // Download 5 files cùng lúc
        
        for (let i = 0; i < files.length; i += batchSize) {
            const batch = files.slice(i, i + batchSize);
            
            console.log(`\n📦 Batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(files.length/batchSize)} (${batch.length} files)`);
            
            const promises = batch.map(async (file) => {
                const outputPath = path.join(DOWNLOAD_DIR, file.path);
                
                try {
                    console.log(`  Downloading: ${file.path}`);
                    const result = await downloadFile(file.downloadUrl, outputPath);
                    console.log(`  ✅ ${file.name} (${result.size} bytes)`);
                    return { success: true, size: result.size, file: file.path };
                } catch (error) {
                    console.log(`  ❌ ${file.name} - ${error.message}`);
                    return { success: false, file: file.path, error: error.message };
                }
            });
            
            const results = await Promise.all(promises);
            
            // Tính toán kết quả batch
            results.forEach(result => {
                if (result.success) {
                    successCount++;
                    totalSize += result.size;
                } else {
                    failCount++;
                }
            });
            
            // Hiển thị progress
            const progress = ((i + batch.length) / files.length * 100).toFixed(1);
            console.log(`  📊 Progress: ${progress}% (${successCount + failCount}/${files.length})`);
        }
        
        // 4. Hiển thị kết quả cuối cùng
        console.log('\n' + '='.repeat(50));
        console.log('🎉 Download completed!');
        console.log('='.repeat(50));
        console.log(`📁 Total directories created: ${directories.length}`);
        console.log(`✅ Successful downloads: ${successCount}`);
        console.log(`❌ Failed downloads: ${failCount}`);
        console.log(`📦 Total size downloaded: ${formatFileSize(totalSize)}`);
        console.log(`💾 Files saved to: ${path.resolve(DOWNLOAD_DIR)}`);
        
        if (failCount > 0) {
            console.log(`\n⚠️  ${failCount} files failed to download. Check the logs above for details.`);
        }
        
    } catch (error) {
        console.error('❌ Download failed:', error.message);
    }
}

// Helper function để format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Chạy download
downloadAllFiles();
