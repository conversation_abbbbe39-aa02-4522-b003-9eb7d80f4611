<?xml version="1.0" encoding="UTF-8"?>

<fwAllConfigs>
	<ConfigArray>

		<!-- Base configuration, applies to all platforms and builds. -->
		<Item>
			<Build>Any</Build>
			<Platforms>Any</Platforms>
			<Config type="CGameConfig">
				<PoolSizes>
					<Entries>
						<!-- Keep these sorted -->
						<Item>
							<PoolName>CNonPhysicalPlayerData</PoolName>
							<PoolSize value="512"/>
						</Item>
						<Item>
							<PoolName>CPlayerInfo</PoolName>
							<PoolSize value="256"/>
						</Item>
						<Item>
							<PoolName>CNetViewPortWrapper</PoolName>
							<PoolSize value="256"/>
						</Item>
						<Item>
							<PoolName>CNetworkDamageTracker</PoolName>
							<PoolSize value="256"/>
						</Item>
						<Item>
							<PoolName>CPickupData</PoolName>
							<PoolSize value="300"/>
						</Item>
						<Item>
							<PoolName>CNetObjPed</PoolName>
							<PoolSize value="238"/> <!-- 110 + player count -->
						</Item>
						<Item>
							<PoolName>CNetBlenderPed</PoolName>
							<PoolSize value="238"/> <!-- 110 + player count -->
						</Item>
						<Item>
							<PoolName>CNetObjPed::CNetTennisMotionData</PoolName>
							<PoolSize value="256"/>
						</Item>
						<Item>
							<PoolName>TaskSequenceInfo</PoolName>
							<PoolSize value="2400"/>
						</Item>
						<Item>
							<PoolName>AnimatedBuilding</PoolName>
							<PoolSize value="600"/>
						</Item>
						<Item>
							<PoolName>AttachmentExtension</PoolName>
							<PoolSize value="860"/>
						</Item>
						<Item>
						  <PoolName>AudioHeap</PoolName>
						  <PoolSize value="195"/>
						</Item>
						<Item>
							<PoolName>BlendshapeStore</PoolName>
							<PoolSize value="75"/>
						</Item>
						<Item>
							<PoolName>Building</PoolName>
							<PoolSize value="65000"/>
						</Item>
						<Item>
							<PoolName>StreamPed req data</PoolName>
							<PoolSize value="250"/>
						</Item>
						<Item>
							<PoolName>StreamPed render data</PoolName>
							<PoolSize value="400"/>
						</Item>
						<Item>
							<PoolName>PedProp req data</PoolName>
							<PoolSize value="250"/>
						</Item>
						<Item>
							<PoolName>PedProp render data</PoolName>
							<PoolSize value="560"/>
						</Item>
						<Item>
							<!-- Size of CVehicleRecordingMgr's streaming module. -->
							<PoolName>carrec</PoolName>
							<PoolSize value="4000"/>
						</Item>
						<Item>
							<PoolName>CBoatChaseDirector</PoolName>
							<PoolSize value="4"/>
						</Item>
						<Item>
							<PoolName>CVehicleCombatAvoidanceArea</PoolName>
							<PoolSize value="5"/>
						</Item>
						<Item>
							<PoolName>CCargen</PoolName>
							<PoolSize value="800"/>
						</Item>
						<Item>
							<PoolName>CCargenForScenarios</PoolName>
							<PoolSize value="500"/>
						</Item>
						<Item>
							<PoolName>CCombatDirector</PoolName>
							<PoolSize value="5"/>
						</Item>
						<Item>
							<PoolName>CCombatInfo</PoolName>
							<PoolSize value="64"/>
						</Item>
						<Item>
							<PoolName>CCombatSituation</PoolName>
							<PoolSize value="6"/>
						</Item>
						<Item>
							<PoolName>CCoverFinder</PoolName>
							<PoolSize value="40"/>
						</Item>
						<Item>
							<PoolName>CDefaultCrimeInfo</PoolName>
							<PoolSize value="13"/>
						</Item>
						<Item>
							<PoolName>CTacticalAnalysis</PoolName>
							<PoolSize value="3"/>
						</Item>
						<Item>
							<PoolName>CTaskUseScenarioEntityExtension</PoolName>
							<PoolSize value="256"/>
						</Item>
						<Item>
							<PoolName>CBullet::sBulletInstance</PoolName>
							<PoolSize value="192"/>
						</Item>
						<Item>
							<PoolName>AnimStore</PoolName>
							<PoolSize value="21000"/>
						</Item>
						<Item>
							<PoolName>CGameScriptResource</PoolName>
							<PoolSize value="3067"/>
						</Item>
						<Item>
							<PoolName>CGameScriptHandlerNetwork</PoolName>
							<PoolSize value="750"/>
						</Item>
						<Item>
							<PoolName>CGameScriptHandlerNetComponent</PoolName>
							<PoolSize value="850"/>
						</Item>
						<Item>
							<PoolName>CGameScriptHandler</PoolName>
							<PoolSize value="750"/>
						</Item>
						<Item>
							<PoolName>ClothStore</PoolName>
							<PoolSize value="600"/>
						</Item>
						<Item>
							<PoolName>CombatMeleeManager_Groups</PoolName>
							<PoolSize value="3"/>
						</Item>
						<Item>
							<PoolName>CombatMeleeManager_GroupsMP</PoolName>
							<PoolSize value="20"/>
						</Item>
						<Item>
							<PoolName>CombatMountedManager_Attacks</PoolName>
							<PoolSize value="1"/>
						</Item>
						<Item>
							<PoolName>CompEntity</PoolName>
							<PoolSize value="39"/>
						</Item>
						<Item>
							<PoolName>CPrioritizedClipSetBucket</PoolName>
							<PoolSize value="22"/>
						</Item>
						<Item>
							<PoolName>CPrioritizedClipSetRequest</PoolName>
							<PoolSize value="116"/>
						</Item>
						<Item>
							<PoolName>CRoadBlock</PoolName>
							<PoolSize value="3"/>
						</Item>
						<Item>
							<PoolName>CStuntJump</PoolName>
							<PoolSize value="64"/>
						</Item>
						<Item>
							<PoolName>CScenarioInfo</PoolName>
							<PoolSize value="840"/>
						</Item>
						<Item>
							<PoolName>CScenarioPointExtraData</PoolName>
							<PoolSize value="420"/>
						</Item>
						<Item>
							<PoolName>CutsceneStore</PoolName>
							<PoolSize value="1400"/>
						</Item>
						<Item>
							<PoolName>CNetworkPtFXWorldStateData</PoolName>
							<PoolSize value="240"/>
						</Item>
						<Item>
							<PoolName>CScriptEntityExtension</PoolName>
							<PoolSize value="10000"/>
						</Item>
						<Item>
							<PoolName>CVehicleChaseDirector</PoolName>
							<PoolSize value="4"/>
						</Item>
						<Item>
							<PoolName>CVehicleClipRequestHelper</PoolName>
							<PoolSize value="45"/>
						</Item>
						<Item>
							<PoolName>CPathNodeRouteSearchHelper</PoolName>
							<PoolSize value="256"/>
						</Item>
						<Item>
							<PoolName>CGrabHelper</PoolName>
							<PoolSize value="8"/>
						</Item>
						<Item>
							<PoolName>CGpsNumNodesStored</PoolName>
							<PoolSize value="1024"/>
						</Item>
						<Item>
							<PoolName>CClimbHandHoldDetected</PoolName>
							<PoolSize value="32"/>
						</Item>
						<Item>
							<PoolName>CAmbientLookAt</PoolName>
							<PoolSize value="40"/>
						</Item>
						<Item>
							<PoolName>DecoratorExtension</PoolName>
							<PoolSize value="1500"/>
						</Item>
						<Item>
							<PoolName>Decorator</PoolName>
							<PoolSize value="1500"/>
						</Item>
						<Item>
							<PoolName>DrawableStore</PoolName>
							<PoolSize value="234900"/>
						</Item>
						<Item>
							<PoolName>Dummy Object</PoolName>
							<PoolSize value="60000"/>
						</Item>
						<Item>
							<PoolName>DwdStore</PoolName>
							<PoolSize value="240000"/>
						</Item>
						<Item>
							<PoolName>EntityBatch</PoolName>
							<PoolSize value="5000"/>
						</Item>
						<Item>
							<PoolName>GrassBatch</PoolName>
							<PoolSize value="30000"/>
						</Item>
						<Item>
							<PoolName>ExprDictStore</PoolName>
							<PoolSize value="290"/>
						</Item>
						<Item>
							<PoolName>FrameFilterStore</PoolName>
							<PoolSize value="14"/>
						</Item>
						<Item>
							<PoolName>FragmentStore</PoolName>
							<PoolSize value="59000"/>
						</Item>
						<Item>
							<PoolName>GamePlayerBroadcastDataHandler_Remote</PoolName>
							<PoolSize value="1550"/>
						</Item>
						<Item>
							<PoolName>InstanceBuffer</PoolName>
							<PoolSize value="20000"/>
						</Item>
						<Item>
							<PoolName>InteriorInst</PoolName>
							<PoolSize value="900"/>
						</Item>
						<Item>
							<PoolName>fwPortalSceneGraphNode</PoolName>
							<PoolSize value="1500"/>
						</Item>
						<Item>
							<PoolName>InteriorProxy</PoolName>
							<PoolSize value="9060"/>
						</Item>
						<Item>
							<PoolName>IplStore</PoolName>
							<PoolSize value="3000"/>
						</Item>
						<Item>
							<PoolName>MaxLoadedInfo</PoolName>
							<PoolSize value="32768"/>
						</Item>
						<Item>
							<PoolName>MaxLoadRequestedInfo</PoolName>
							<PoolSize value="3000"/>
						</Item>
						<Item>
							<PoolName>ActiveLoadedInfo</PoolName>
							<PoolSize value="3000"/>
						</Item>
						<Item>
							<PoolName>ActivePersistentLoadedInfo</PoolName>
							<PoolSize value="1439"/>
						</Item>
						<Item>
							<PoolName>Known Refs</PoolName>
							<PoolSize value="24000"/>
						</Item>
						<Item>
							<PoolName>LightEntity</PoolName>
							<PoolSize value="4700"/>
						</Item>
						<Item>
							<PoolName>MapDataLoadedNode</PoolName>
							<PoolSize value="1100"/>
						</Item>
						<Item>
							<PoolName>MapDataStore</PoolName>
							<PoolSize value="12000"/>
						</Item>
						<Item>
							<PoolName>MapTypesStore</PoolName>
							<PoolSize value="3000"/>
						</Item>
						<Item>
							<PoolName>MetaDataStore</PoolName>
							<PoolSize value="6000"/>
						</Item>
						<Item>
							<PoolName>NavMeshes</PoolName>
							<PoolSize value="10100"/>
						</Item>
						<Item>
							<PoolName>NetworkDefStore</PoolName>
							<PoolSize value="118"/>
						</Item>
						<Item>
							<PoolName>NetworkCrewDataMgr</PoolName>
							<PoolSize value="16"/>
						</Item>
						<Item>
							<PoolName>Object</PoolName>
							<PoolSize value="3300"/>
						</Item>
						<Item>
							<PoolName>OcclusionInteriorInfo</PoolName>
							<PoolSize value="70"/>
						</Item>
						<Item>
							<PoolName>OcclusionPathNode</PoolName>
							<PoolSize value="20000"/>
						</Item>
						<Item>
							<PoolName>OcclusionPortalEntity</PoolName>
							<PoolSize value="3000"/>
						</Item>
						<Item>
							<PoolName>OcclusionPortalInfo</PoolName>
							<PoolSize value="3000"/>
						</Item>
						<Item>
							<PoolName>Peds</PoolName>
							<PoolSize value="256"/>
						</Item>
						<Item>
							<PoolName>CWeapon</PoolName>
							<PoolSize value="1024"/>
						</Item>
						<Item>
							<PoolName>CWeaponComponentInfo</PoolName>
							<PoolSize value="2048"/>
						</Item>
						<Item>
							<PoolName>phInstGta</PoolName>
							<PoolSize value="16384"/>
						</Item>
						<Item>
							<PoolName>PhysicsBounds</PoolName>
							<PoolSize value="1750"/>
						</Item>
						<Item>
							<PoolName>CPickup</PoolName>
							<PoolSize value="73"/>
						</Item>
						<Item>
							<PoolName>CPickupPlacement</PoolName>
							<PoolSize value="280"/>
						</Item>
						<Item>
							<PoolName>CPickupPlacementCustomScriptData</PoolName>
							<PoolSize value="200"/>
						</Item>
						<Item>
							<PoolName>CRegenerationInfo</PoolName>
							<PoolSize value="56"/>
						</Item>
						<Item>
							<PoolName>PortalInst</PoolName>
							<PoolSize value="900" />
						</Item>
						<Item>
							<PoolName>PoseMatcherStore</PoolName>
							<PoolSize value="28" />
						</Item>
						<Item>
							<PoolName>PMStore</PoolName>
							<PoolSize value="100"/>
						</Item>
						<Item>
							<PoolName>PtFxSortedEntity</PoolName>
							<PoolSize value="384"/>
						</Item>
						<Item>
							<PoolName>PtFxAssetStore</PoolName>
							<PoolSize value="400"/>
						</Item>
						<Item>
							<PoolName>QuadTreeNodes</PoolName>
							<PoolSize value="1500"/>
						</Item>
						<Item>
							<PoolName>ScaleformStore</PoolName>
							<PoolSize value="852"/>
						</Item>
						<Item>
							<PoolName>ScaleformMgrArray</PoolName>
							<PoolSize value="40"/>
						</Item>
						<Item>
							<PoolName>ScriptStore</PoolName>
							<PoolSize value="1150"/>
						</Item>
						<Item>
							<PoolName>StaticBounds</PoolName>
							<PoolSize value="20000"/>
						</Item>
						<Item>
							<PoolName>tcBox</PoolName>
							<PoolSize value="6000"/>
						</Item>
						<Item>
							<PoolName>TrafficLightInfos</PoolName>
							<PoolSize value="512"/>
						</Item>
						<Item>
							<PoolName>TxdStore</PoolName>
							<PoolSize value="105500"/>
						</Item>
						<Item>
							<PoolName>CNetObjVehicle</PoolName>
							<PoolSize value="250"/>
						</Item>
						<Item>
							<PoolName>CVehicleSyncData</PoolName>
							<PoolSize value="255"/>
						</Item>
						<Item>
							<PoolName>Vehicles</PoolName>
							<PoolSize value="300"/>
						</Item>
						<Item>
							<PoolName>VehicleStreamRequest</PoolName>
							<PoolSize value="256"/>
						</Item>
						<Item>
							<PoolName>VehicleStreamRender</PoolName>
							<PoolSize value="256"/>
						</Item>
						<Item>
							<PoolName>VehicleStruct</PoolName>
							<PoolSize value="200"/>
						</Item>
        					<Item>
							<PoolName>HandlingData</PoolName>
							<PoolSize value="16000"/>
						</Item>
						<Item>
							<!-- Size of the CWaypointRecording streaming module. -->
							<PoolName>wptrec</PoolName>
							<PoolSize value="850"/>
						</Item>
						<Item>
							<PoolName>fwLodNode</PoolName>
							<PoolSize value="12500"/>
						</Item>
						<Item>
							<PoolName>CTask</PoolName>
							<PoolSize value="8192"/>
						</Item>
						<Item>
							<PoolName>CEvent</PoolName>
							<PoolSize value="1000"/>
						</Item>
						<Item>
							<PoolName>netGameEvent</PoolName>
							<PoolSize value="511"/>
						</Item>
						<Item>
							<PoolName>atDNetEventNode</PoolName>
							<PoolSize value="511"/>
						</Item>

						<Item>
							<PoolName>CMoveObject</PoolName>
							<PoolSize value="300"/>
						</Item>
						<Item>
							<PoolName>CMoveAnimatedBuilding</PoolName>
							<PoolSize value="161"/>
						</Item>
						<Item>
							<PoolName>atDScriptObjectNode</PoolName>
							<PoolSize value="10000"/>
						</Item>
						<Item>
							<PoolName>fwDynamicArchetypeComponent</PoolName>
							<PoolSize value="40000"/>
						</Item>
						<Item>
							<PoolName>fwDynamicEntityComponent</PoolName>
							<PoolSize value="10500"/>
						</Item>
						<Item>
							<PoolName>fwEntityContainer</PoolName>
							<PoolSize value="615"/>
						</Item>
						<Item>
							<PoolName>fwMatrixTransform</PoolName>
							<PoolSize value="30000"/>
						</Item>
						<Item>
							<PoolName>fwQuaternionTransform</PoolName>
							<PoolSize value="12958"/>
						</Item>
						<Item>
							<PoolName>fwSimpleTransform</PoolName>
							<PoolSize value="37000"/>
						</Item>
						<Item>
							<PoolName>fwScriptGuid</PoolName>
							<PoolSize value="40000"/>
						</Item>
						<Item>
							<PoolName>ScenarioCarGensPerRegion</PoolName>
							<PoolSize value="80"/>
						</Item>
						<Item>
							<PoolName>ScenarioPointsAndEdgesPerRegion</PoolName>
							<PoolSize value="1000"/>
						</Item>
						<Item>
							<PoolName>ScenarioPoint</PoolName>
							<PoolSize value="900"/>
						</Item>
						<Item>
							<PoolName>ScenarioPointEntity</PoolName>
							<PoolSize value="350"/>
						</Item>
						<Item>
							<PoolName>ScenarioPointWorld</PoolName>
							<PoolSize value="1400"/>
						</Item>
						<Item>
							<PoolName>MaxNonRegionScenarioPointSpatialObjects</PoolName>
							<PoolSize value="900"/>
						</Item>
						<Item>
							<PoolName>ObjectIntelligence</PoolName>
							<PoolSize value="120"/>
						</Item>
						<Item>
							<PoolName>VehicleScenarioAttractors</PoolName>
							<PoolSize value="64"/>
						</Item>
						<Item>
							<PoolName>AircraftFlames</PoolName>
							<PoolSize value="34"/>
						</Item>
						<Item>
							<PoolName>CScenarioPointChainUseInfo</PoolName>
							<PoolSize value="96"/>
						</Item>
						<Item>
							<PoolName>CScenarioClusterSpawnedTrackingData</PoolName>
							<PoolSize value="190"/>
						</Item>
						<Item>
							<PoolName>CSPClusterFSMWrapper</PoolName>
							<PoolSize value="192"/>
						</Item>
						<Item>
							<PoolName>fwArchetypePooledMap</PoolName>
							<PoolSize value="65535"/>
						</Item>
						<Item>
							<PoolName>CTaskConversationHelper</PoolName>
							<PoolSize value="4"/>
						</Item>
						<Item>
							<PoolName>SyncedScenes</PoolName>
							<PoolSize value="50"/>
						</Item>
						<Item>
							<PoolName>SyncedScenesMP</PoolName>
							<PoolSize value="64"/>
					</Item>
						<Item>
						  <PoolName>AnimScenes</PoolName>
						  <PoolSize value="10"/>
						</Item>
						<Item>
							<PoolName>CPropManagementHelper</PoolName>
							<PoolSize value="256"/>
						</Item>
						<Item>
							<PoolName>ActionTable_Definitions</PoolName>
							<PoolSize value="336"/>
						</Item>
						<Item>
							<PoolName>ActionTable_Results</PoolName>
							<PoolSize value="503"/>
						</Item>
						<Item>
							<PoolName>ActionTable_Impulses</PoolName>
							<PoolSize value="20"/>
						</Item>
						<Item>
							<PoolName>ActionTable_Interrelations</PoolName>
							<PoolSize value="61"/>
						</Item>
						<Item>
							<PoolName>ActionTable_Homings</PoolName>
							<PoolSize value="167"/>
						</Item>
						<Item>
							<PoolName>ActionTable_Damages</PoolName>
							<PoolSize value="24"/>
						</Item>
						<Item>
							<PoolName>ActionTable_StrikeBones</PoolName>
							<PoolSize value="19"/>
						</Item>
						<Item>
							<PoolName>ActionTable_Rumbles</PoolName>
							<PoolSize value="5"/>
						</Item>
						<Item>
							<PoolName>ActionTable_Branches</PoolName>
							<PoolSize value="27"/>
						</Item>
						<Item>
							<PoolName>ActionTable_StealthKills</PoolName>
							<PoolSize value="10"/>
						</Item>
						<Item>
							<PoolName>ActionTable_Vfx</PoolName>
							<PoolSize value="35"/>
						</Item>
						<Item>
							<PoolName>ActionTable_FacialAnimSets</PoolName>
							<PoolSize value="5"/>
						</Item>
						<Item>
							<PoolName>NetworkEntityAreas</PoolName>
							<PoolSize value="200"/>
						</Item>
						<Item>
							<PoolName>NavMeshRoute</PoolName>
							<PoolSize value="1024"/>
						</Item>
						<Item>
							<PoolName>scrGlobals</PoolName>
							<PoolSize value="15080000"/>
						</Item>
						<Item>
							<PoolName>ropeData</PoolName>
							<PoolSize value="64"/>
						</Item>
					</Entries>
				</PoolSizes>

				<ConfigPopulation>
					<ScenarioPedsMultiplier_Base value="70"/>
					<ScenarioPedsMultiplier value="100"/>
					<AmbientPedsMultiplier_Base value="70"/>
					<AmbientPedsMultiplier value="100"/>
					<MaxTotalPeds_Base value="70"/>
					<MaxTotalPeds value="200"/>
					<PedMemoryMultiplier value="500"/>
					<PedsForVehicles_Base value="75"/>
					<PedsForVehicles value="75"/>
					<VehicleTimesliceMaxUpdatesPerFrame_Base value="4"/>
					<VehicleTimesliceMaxUpdatesPerFrame value="10"/>
					<VehicleAmbientDensityMultiplier_Base value="66"/>
					<VehicleAmbientDensityMultiplier value="100"/>
					<VehicleMemoryMultiplier value="500"/>
					<VehicleParkedDensityMultiplier_Base value="50"/>
					<VehicleParkedDensityMultiplier value="100"/>
					<VehicleLowPrioParkedDensityMultiplier_Base value="50"/>
					<VehicleLowPrioParkedDensityMultiplier value="100"/>
					<VehicleUpperLimit_Base value="40"/>
					<VehicleUpperLimit value="130"/>
					<VehicleUpperLimitMP value="60"/>
					<VehicleParkedUpperLimit_Base value="40"/>
					<VehicleParkedUpperLimit value="100"/>
					<VehicleKeyholeShapeInnerThickness_Base value="20"/>
					<VehicleKeyholeShapeInnerThickness value="20"/>
					<VehicleKeyholeShapeOuterThickness_Base value="40"/>
					<VehicleKeyholeShapeOuterThickness value="40"/>
					<VehicleKeyholeShapeInnerRadius_Base value="80"/>
					<VehicleKeyholeShapeInnerRadius value="120"/>
					<VehicleKeyholeShapeOuterRadius_Base value="185"/>
					<VehicleKeyholeShapeOuterRadius value="215"/>
					<VehicleKeyholeSideWallThickness_Base value="60"/>
					<VehicleKeyholeSideWallThickness value="60"/>
					<VehicleMaxCreationDistance_Base value="225"/>
					<VehicleMaxCreationDistance value="255"/>
					<VehicleMaxCreationDistanceOffscreen_Base value="50"/>
					<VehicleMaxCreationDistanceOffscreen value="50"/>
					<VehicleCullRange_Base value="251"/>
					<VehicleCullRange value="311"/>
					<VehicleCullRangeOnScreenScale_Base value="140"/>
					<VehicleCullRangeOnScreenScale value="140"/>
					<VehicleCullRangeOffScreen_Base value="100"/>
					<VehicleCullRangeOffScreen value="150"/>
					<DensityBasedRemovalRateScale_Base value="36"/>
					<DensityBasedRemovalRateScale value="36"/>
					<DensityBasedRemovalTargetHeadroom_Base value="10"/>
					<DensityBasedRemovalTargetHeadroom value="10"/>
					<VehicleSpacing_Base>
						<VehicleSpacing_0_Base value="1"/>
						<VehicleSpacing_1_Base value="172"/>
						<VehicleSpacing_2_Base value="150"/>
						<VehicleSpacing_3_Base value="129"/>
						<VehicleSpacing_4_Base value="110"/>
						<VehicleSpacing_5_Base value="88"/>
						<VehicleSpacing_6_Base value="55"/>
						<VehicleSpacing_7_Base value="52"/>
						<VehicleSpacing_8_Base value="45"/>
						<VehicleSpacing_9_Base value="40"/>
						<VehicleSpacing_10_Base value="34"/>
						<VehicleSpacing_11_Base value="27"/>
						<VehicleSpacing_12_Base value="22"/>
						<VehicleSpacing_13_Base value="20"/>
						<VehicleSpacing_14_Base value="19"/>
						<VehicleSpacing_15_Base value="18"/>
					</VehicleSpacing_Base>
					<VehicleSpacing>
						<VehicleSpacing_0 value="1"/>
						<VehicleSpacing_1 value="172"/>
						<VehicleSpacing_2 value="150"/>
						<VehicleSpacing_3 value="129"/>
						<VehicleSpacing_4 value="110"/>
						<VehicleSpacing_5 value="88"/>
						<VehicleSpacing_6 value="55"/>
						<VehicleSpacing_7 value="52"/>
						<VehicleSpacing_8 value="45"/>
						<VehicleSpacing_9 value="40"/>
						<VehicleSpacing_10 value="34"/>
						<VehicleSpacing_11 value="27"/>
						<VehicleSpacing_12 value="22"/>
						<VehicleSpacing_13 value="20"/>
						<VehicleSpacing_14 value="19"/>
						<VehicleSpacing_15 value="18"/>
					</VehicleSpacing>
					<PlayersRoadScanDistance_Base value="300"/>
					<PlayersRoadScanDistance value="330"/>
					<PlayerRoadDensityInc_Base>
						<PlayerRoadDensityInc_0_Base value="0"/>
						<PlayerRoadDensityInc_1_Base value="0"/>
						<PlayerRoadDensityInc_2_Base value="0"/>
						<PlayerRoadDensityInc_3_Base value="0"/>
						<PlayerRoadDensityInc_4_Base value="0"/>
						<PlayerRoadDensityInc_5_Base value="0"/>
						<PlayerRoadDensityInc_6_Base value="0"/>
						<PlayerRoadDensityInc_7_Base value="0"/>
						<PlayerRoadDensityInc_8_Base value="0"/>
						<PlayerRoadDensityInc_9_Base value="0"/>
						<PlayerRoadDensityInc_10_Base value="0"/>
						<PlayerRoadDensityInc_11_Base value="0"/>
						<PlayerRoadDensityInc_12_Base value="0"/>
						<PlayerRoadDensityInc_13_Base value="0"/>
						<PlayerRoadDensityInc_14_Base value="0"/>
						<PlayerRoadDensityInc_15_Base value="0"/>
					</PlayerRoadDensityInc_Base>
					<PlayerRoadDensityInc>
						<PlayerRoadDensityInc_0 value="0"/>
						<PlayerRoadDensityInc_1 value="0"/>
						<PlayerRoadDensityInc_2 value="0"/>
						<PlayerRoadDensityInc_3 value="0"/>
						<PlayerRoadDensityInc_4 value="0"/>
						<PlayerRoadDensityInc_5 value="0"/>
						<PlayerRoadDensityInc_6 value="2"/>
						<PlayerRoadDensityInc_7 value="2"/>
						<PlayerRoadDensityInc_8 value="2"/>
						<PlayerRoadDensityInc_9 value="2"/>
						<PlayerRoadDensityInc_10 value="2"/>
						<PlayerRoadDensityInc_11 value="1"/>
						<PlayerRoadDensityInc_12 value="0"/>
						<PlayerRoadDensityInc_13 value="0"/>
						<PlayerRoadDensityInc_14 value="0"/>
						<PlayerRoadDensityInc_15 value="0"/>
					</PlayerRoadDensityInc>
					<NonPlayerRoadDensityDec_Base>
						<NonPlayerRoadDensityDec_0_Base value="0"/>
						<NonPlayerRoadDensityDec_1_Base value="0"/>
						<NonPlayerRoadDensityDec_2_Base value="0"/>
						<NonPlayerRoadDensityDec_3_Base value="1"/>
						<NonPlayerRoadDensityDec_4_Base value="1"/>
						<NonPlayerRoadDensityDec_5_Base value="1"/>
						<NonPlayerRoadDensityDec_6_Base value="1"/>
						<NonPlayerRoadDensityDec_7_Base value="2"/>
						<NonPlayerRoadDensityDec_8_Base value="2"/>
						<NonPlayerRoadDensityDec_9_Base value="2"/>
						<NonPlayerRoadDensityDec_10_Base value="2"/>
						<NonPlayerRoadDensityDec_11_Base value="2"/>
						<NonPlayerRoadDensityDec_12_Base value="3"/>
						<NonPlayerRoadDensityDec_13_Base value="3"/>
						<NonPlayerRoadDensityDec_14_Base value="3"/>
						<NonPlayerRoadDensityDec_15_Base value="3"/>
					</NonPlayerRoadDensityDec_Base>
					<NonPlayerRoadDensityDec>
						<NonPlayerRoadDensityDec_0 value="0"/>
						<NonPlayerRoadDensityDec_1 value="0"/>
						<NonPlayerRoadDensityDec_2 value="0"/>
						<NonPlayerRoadDensityDec_3 value="0"/>
						<NonPlayerRoadDensityDec_4 value="0"/>
						<NonPlayerRoadDensityDec_5 value="0"/>
						<NonPlayerRoadDensityDec_6 value="0"/>
						<NonPlayerRoadDensityDec_7 value="0"/>
						<NonPlayerRoadDensityDec_8 value="0"/>
						<NonPlayerRoadDensityDec_9 value="0"/>
						<NonPlayerRoadDensityDec_10 value="0"/>
						<NonPlayerRoadDensityDec_11 value="1"/>
						<NonPlayerRoadDensityDec_12 value="2"/>
						<NonPlayerRoadDensityDec_13 value="3"/>
						<NonPlayerRoadDensityDec_14 value="4"/>
						<NonPlayerRoadDensityDec_15 value="5"/>
					</NonPlayerRoadDensityDec>
					<VehiclePopulationFrameRate_Base value="20"/>
					<VehiclePopulationFrameRate value="25"/>
					<VehiclePopulationCyclesPerFrame_Base value="1"/>
					<VehiclePopulationCyclesPerFrame value="1"/>
					<VehiclePopulationFrameRateMP_Base value="15"/>
					<VehiclePopulationFrameRateMP value="20"/>
					<VehiclePopulationCyclesPerFrameMP_Base value="1"/>
					<VehiclePopulationCyclesPerFrameMP value="1"/>
					<PedPopulationFrameRate_Base value="20"/>
					<PedPopulationFrameRate value="25"/>
					<PedPopulationCyclesPerFrame_Base value="1"/>
					<PedPopulationCyclesPerFrame value="1"/>
					<PedPopulationFrameRateMP_Base value="15"/>
					<PedPopulationFrameRateMP value="20"/>
					<PedPopulationCyclesPerFrameMP_Base value="1"/>
					<PedPopulationCyclesPerFrameMP value="1"/>
				</ConfigPopulation>

				<Config2dEffects>
					<MaxAttrsAudio value="280"/>
					<MaxAttrsBuoyancy value="100"/>
					<MaxAttrsDecal value="50"/>
					<MaxAttrsExplosion value="50"/>
					<MaxAttrsLadder value="122"/>
					<MaxAttrsLightShaft value="600"/>
					<MaxAttrsParticle value="2500"/>
					<MaxAttrsProcObj value="100"/>
					<MaxAttrsScrollBar value="1"/>
					<MaxAttrsSpawnPoint value="500"/>
					<MaxAttrsWindDisturbance value="50"/>
					<MaxAttrsWorldPoint value="600"/>
					<MaxEffectsWorld2d value="600"/>
				</Config2dEffects>

				<ConfigModelInfo>
					<defaultPlayerName>player_zero</defaultPlayerName>
					<defaultProloguePlayerName>csb_prolsec</defaultProloguePlayerName>
					<MaxBaseModelInfos value="2"/>
					<MaxCompEntityModelInfos value="100"/>
					<MaxMloInstances value="18000"/>
					<MaxMloModelInfos value="220"/>
					<MaxPedModelInfos value="825"/>
					<MaxTimeModelInfos value="1800"/>
					<MaxVehicleModelInfos value="1024"/>
					<MaxWeaponModelInfos value="115"/>
					<MaxExtraPedModelInfos value="1024"/>
					<MaxExtraVehicleModelInfos value="2048"/>
					<MaxExtraWeaponModelInfos value="2048"/>
				</ConfigModelInfo>

				<ConfigExtensions>
					<MaxDoorExtensions value="75"/>
					<MaxLightExtensions value="1024"/>
					<MaxSpawnPointOverrideExtensions value="255"/>
					<MaxExpressionExtensions value="255"/>
				</ConfigExtensions>

				<ConfigStreamingEngine>
					<ArchiveCount value="4184"/>
					<PhysicalStreamingBuffer value="252"/>
					<VirtualStreamingBuffer value="1000"/>
				</ConfigStreamingEngine>

				<UseVehicleBurnoutTexture>CB_TRUE</UseVehicleBurnoutTexture>
				<AllowCrouchedMovement>CB_FALSE</AllowCrouchedMovement>
				<AllowParachuting>CB_TRUE</AllowParachuting>
				<AllowStealthMovement>CB_TRUE</AllowStealthMovement>
				<DebugScriptsPath>x:\gta5\script\dev\singleplayer\sco\DEBUG\</DebugScriptsPath>

				<Threads>
					<Item key="Streamer">
						<Priority>PRIO_NORMAL</Priority>
					</Item>
					<Item key="Update">
						<Priority>PRIO_NORMAL</Priority>
					</Item>
					<Item key="Render">
						<Priority>PRIO_NORMAL</Priority>
					</Item>
					<Item key="PathServer">
						<Priority>PRIO_LOWEST</Priority>
					</Item>
					<Item key="SysTask">
						<Priority>PRIO_ABOVE_NORMAL</Priority>
					</Item>
					<Item key="ControlMgrUpdateThread">
						<Priority>PRIO_ABOVE_NORMAL</Priority>
					</Item>
				</Threads>

				<ConfigOnlineServices>
					<RosTitleName>gta5</RosTitleName>
					<RosTitleVersion value="11"/>
					<RosScVersion value="11"/>
					<SteamAppId value="271590"/>
					<TitleDirectoryName>GTA V</TitleDirectoryName>
					<MultiplayerSessionTemplateName>SsnTemplateOpen</MultiplayerSessionTemplateName>
					<ScAuthTitleId>gtav</ScAuthTitleId>
				</ConfigOnlineServices>

				<ConfigUGCDescriptions>
					<MaxDescriptionLength value="255"/>
					<MaxNumDescriptions value="640"/>
					<SizeOfDescriptionBuffer value="60000"/>
				</ConfigUGCDescriptions>

				<ConfigNetScriptBroadcastData>
					<HostBroadcastData>
						<Item>
							<SizeOfData value="14000"/>
							<MaxParticipants value="32"/>
							<NumInstances value="5"/>
						</Item>
						<Item>
							<SizeOfData value="6500"/>
							<MaxParticipants value="32"/>
							<NumInstances value="6"/>
						</Item>
						<Item>
							<SizeOfData value="4000"/>
							<MaxParticipants value="32"/>
							<NumInstances value="6"/>
						</Item>
						<Item>
							<SizeOfData value="2000"/>
							<MaxParticipants value="32"/>
							<NumInstances value="2"/>
						</Item>
						<Item>
							<SizeOfData value="1500"/>
							<MaxParticipants value="32"/>
							<NumInstances value="4"/>
						</Item>
						<Item>
							<SizeOfData value="1000"/>
							<MaxParticipants value="32"/>
							<NumInstances value="10"/>
						</Item>
						<Item>
							<SizeOfData value="500"/>
							<MaxParticipants value="32"/>
							<NumInstances value="4"/>
						</Item>
						<Item>
							<SizeOfData value="250"/>
							<MaxParticipants value="32"/>
							<NumInstances value="4"/>
						</Item>
						<Item>
							<SizeOfData value="120"/>
							<MaxParticipants value="32"/>
							<NumInstances value="6"/>
						</Item>
						<Item>
							<SizeOfData value="60"/>
							<MaxParticipants value="32"/>
							<NumInstances value="6"/>
						</Item>
					</HostBroadcastData>
					<PlayerBroadcastData>
						<Item>
							<SizeOfData value="3600"/>
							<MaxParticipants value="32"/>
							<NumInstances value="1"/>
						</Item>
						<Item>
							<SizeOfData value="2500"/>
							<MaxParticipants value="32"/>
							<NumInstances value="2"/>
						</Item>
						<Item>
							<SizeOfData value="1400"/>
							<MaxParticipants value="32"/>
							<NumInstances value="6"/>
						</Item>
						<Item>
							<SizeOfData value="600"/>
							<MaxParticipants value="32"/>
							<NumInstances value="2"/>
						</Item>
						<Item>
							<SizeOfData value="500"/>
							<MaxParticipants value="32"/>
							<NumInstances value="3"/>
						</Item>
						<Item>
							<SizeOfData value="400"/>
							<MaxParticipants value="32"/>
							<NumInstances value="8"/>
						</Item>
						<Item>
							<SizeOfData value="150"/>
							<MaxParticipants value="32"/>
							<NumInstances value="6"/>
						</Item>
						<Item>
							<SizeOfData value="84"/>
							<MaxParticipants value="32"/>
							<NumInstances value="5"/>
						</Item>
						<Item>
							<SizeOfData value="40"/>
							<MaxParticipants value="32"/>
							<NumInstances value="8"/>
						</Item>
						<Item>
							<SizeOfData value="16"/>
							<MaxParticipants value="32"/>
							<NumInstances value="16"/>
						</Item>
					</PlayerBroadcastData>
				</ConfigNetScriptBroadcastData>

				<ConfigScriptStackSizes>
					<StackSizeData>
						<Item>
							<StackName>MICRO</StackName>
							<SizeOfStack value="128"/>
							<NumberOfStacksOfThisSize value="20"/>
						</Item>
						<Item>
							<StackName>MINI</StackName>
							<SizeOfStack value="512"/>
							<NumberOfStacksOfThisSize value="20"/>
						</Item>
						<Item>
							<StackName>DEFAULT</StackName>
							<SizeOfStack value="1424"/>
							<NumberOfStacksOfThisSize value="68"/>
						</Item>
						<Item>
							<StackName>SPECIAL_ABILITY</StackName>
							<SizeOfStack value="1828"/>
							<NumberOfStacksOfThisSize value="13"/>
						</Item>
						<Item>
							<StackName>FRIEND</StackName>
							<SizeOfStack value="2050"/>
							<NumberOfStacksOfThisSize value="12"/>
						</Item>
						<Item>
							<StackName>SHOP</StackName>
							<SizeOfStack value="2324"/>
							<NumberOfStacksOfThisSize value="6"/>
						</Item>
						<Item>
							<StackName>CELLPHONE</StackName>
							<SizeOfStack value="2552"/>
							<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
							<StackName>VEHICLE_SPAWN</StackName>
							<SizeOfStack value="3368"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>CAR_MOD_SHOP</StackName>
							<SizeOfStack value="3472"/>
							<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
							<StackName>PAUSE_MENU_SCRIPT</StackName>
							<SizeOfStack value="3076"/>
							<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
							<StackName>APP_INTERNET</StackName>
							<SizeOfStack value="4592"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>MULTIPLAYER_MISSION</StackName>
							<SizeOfStack value="4500"/>
							<NumberOfStacksOfThisSize value="19"/>
						</Item>
						<Item>
							<StackName>CONTACTS_APP</StackName>
							<SizeOfStack value="4000"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>INTERACTION_MENU</StackName>
							<SizeOfStack value="9800"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>SCRIPT_XML</StackName>
							<SizeOfStack value="8344"/>
							<NumberOfStacksOfThisSize value="4"/>
						</Item>
						<Item>
							<StackName>PROPERTY_INT</StackName>
							<SizeOfStack value="19400"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>ACTIVITY_CREATOR_INT</StackName>
							<SizeOfStack value="14900"/>
							<NumberOfStacksOfThisSize value="3"/>
						</Item>
						<Item>
							<StackName>SMPL_INTERIOR</StackName>
							<SizeOfStack value="2000"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>WAREHOUSE</StackName>
							<SizeOfStack value="14000"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>IE_DELIVERY</StackName>
							<SizeOfStack value="2024"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>SHOP_CONTROLLER</StackName>
							<SizeOfStack value="2200"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>AM_MP_YACHT</StackName>
							<SizeOfStack value="5000"/>
							<NumberOfStacksOfThisSize value="3"/>
						</Item>
						<Item>
							<StackName>INGAMEHUD</StackName>
							<SizeOfStack value="4600"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>TRANSITION</StackName>
							<SizeOfStack value="8032"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>FMMC_LAUNCHER</StackName>
							<SizeOfStack value="16000"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>MULTIPLAYER_FREEMODE</StackName>
							<SizeOfStack value="55000"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>MISSION</StackName>
							<SizeOfStack value="25000"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>MP_LAUNCH_SCRIPT</StackName>
							<SizeOfStack value="22500"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
					</StackSizeData>
				</ConfigScriptStackSizes>

				<ConfigScriptTextLines>
					<ArrayOfMaximumTextLines>
						<Item>
							<NameOfScriptTextLine>ScriptTextLinesZeroOrOneNumbers</NameOfScriptTextLine>
							<MaximumNumber value="160"/>
						</Item>
						<Item>
							<NameOfScriptTextLine>ScriptTextLinesOneSubstring</NameOfScriptTextLine>
							<MaximumNumber value="144"/>
						</Item>
						<Item>
							<NameOfScriptTextLine>ScriptTextLinesFourSubstringsThreeNumbers</NameOfScriptTextLine>
							<MaximumNumber value="25"/>
						</Item>
					</ArrayOfMaximumTextLines>
				</ConfigScriptTextLines>

			</Config>
		</Item>

		<!-- Debug configuration overrides -->
		<Item>
			<Build>debug</Build>
			<Platforms>Any</Platforms>
			<Config type="CGameConfig">
				<ConfigStreamingEngine>
					<PhysicalStreamingBuffer value="252"/>
				</ConfigStreamingEngine>
			</Config>
		</Item>

		<!-- Beta configuration overrides -->
		<Item>
			<Build>beta</Build>
			<Platforms>Any</Platforms>
			<Config type="CGameConfig">
				<PoolSizes>
					<Entries>
						<!-- test_startup creates lots of peds and cars around Pershing Square -->
						<Item>
							<PoolName>CScriptEntityExtension</PoolName>
							<PoolSize value="700"/>
						</Item>
					</Entries>
				</PoolSizes>
				<ConfigStreamingEngine>
					<PhysicalStreamingBuffer value="252"/>
				</ConfigStreamingEngine>
			</Config>
		</Item>

		<!-- Nonfinal param configuration overrides -->
		<Item>
			<Build>nonfinal</Build>
			<Platforms>Any</Platforms>
			<Param>usefatcuts</Param>
			<Config type="CGameConfig">
				<PoolSizes>
					<Entries>
						<!-- fatcuts needs a bigger animation pool -->
							<Item>
								<PoolName>AnimStore</PoolName>
								<PoolSize value="15000"/>
							</Item>
							<Item>
								<PoolName>CutSceneStore</PoolName>
								<PoolSize value="15000"/>
							</Item>
					</Entries>
				</PoolSizes>
			</Config>
		</Item>

    <Item>
			<Build>any</Build>
			<Platforms>xboxone</Platforms>
			<Config type="CGameConfig">
			<PoolSizes>
				<Entries>
					<Item>
						<PoolName>OcclusionPathNode</PoolName>
						<PoolSize value="3450"/>
					</Item>
					<Item>
						<PoolName>OcclusionPortalInfo</PoolName>
						<PoolSize value="400"/>
					</Item>
				</Entries>
			</PoolSizes>
				<ConfigStreamingEngine>
					<PhysicalStreamingBuffer value="2354"/>
					<VirtualStreamingBuffer value="0"/>
				</ConfigStreamingEngine>
        <ConfigMediaTranscoding>
          <TranscodingSmallObjectBuffer value="4096"/>
          <TranscodingSmallObjectMaxPointers value="6144"/>
          <TranscodingBuffer value="24000"/>
          <TranscodingMaxPointers value="256"/>
        </ConfigMediaTranscoding>
        <Threads>
          <Item key="Update">
            <Priority>PRIO_HIGHEST</Priority>
            <CpuAffinity>
                <Core value="0"/>
			</CpuAffinity>
          </Item>
          <Item key="AsyncShapeTestMgr">
            <Priority>PRIO_HIGHEST</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
          <Item key="RmptfxUpdateThread">
            <Priority>PRIO_HIGHEST</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
          <Item key="ControlMgrUpdateThread">
            <Priority>PRIO_HIGHEST</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
          <Item key="RecordingThreadDefault">
            <Priority>PRIO_ABOVE_NORMAL</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
          <Item key="ReplayQuantizeThread">
            <Priority>PRIO_BELOW_NORMAL</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
          <Item key="RecordingThreadGameAndObject">
            <Priority>PRIO_ABOVE_NORMAL</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
          <Item key="MediaEncoderThreadAudio">
            <Priority>PRIO_LOWEST</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
          <Item key="RageDvdReader">
            <Priority>PRIO_TIME_CRITICAL</Priority>
            <CpuAffinity>
                <Core value="2"/>
			</CpuAffinity>
          </Item>
          <Item key="RageHddReader">
            <Priority>PRIO_TIME_CRITICAL</Priority>
            <CpuAffinity>
                <Core value="2"/>
			</CpuAffinity>
          </Item>
          <Item key="RecorderWorkerThread">
            <Priority>PRIO_HIGHEST</Priority>
            <CpuAffinity>
                <Core value="2"/>
			</CpuAffinity>
          </Item>
          <Item key="RecordingThreadPed">
            <Priority>PRIO_ABOVE_NORMAL</Priority>
            <CpuAffinity>
                <Core value="2"/>
			</CpuAffinity>
          </Item>
          <Item key="PathServer">
            <Priority>PRIO_LOWEST</Priority>
            <CpuAffinity>
                <Core value="2"/>
			</CpuAffinity>
          </Item>
          <Item key="Render">
            <Priority>PRIO_NORMAL</Priority>
            <CpuAffinity>
                <Core value="3"/>
			</CpuAffinity>
          </Item>
          <Item key="NetLogSpooler">
            <Priority>PRIO_NORMAL</Priority>
            <CpuAffinity>
                <Core value="3"/>
			</CpuAffinity>
          </Item>
          <Item key="NetRelay">
            <Priority>PRIO_NORMAL</Priority>
            <CpuAffinity>
                <Core value="3"/>
			</CpuAffinity>
          </Item>
          <Item key="RageLogfile">
            <Priority>PRIO_NORMAL</Priority>
            <CpuAffinity>
                <Core value="3"/>
			</CpuAffinity>
          </Item>
          <Item key="RageNetTcpWorker">
            <Priority>PRIO_NORMAL</Priority>
            <CpuAffinity>
                <Core value="3"/>
			</CpuAffinity>
          </Item>
          <Item key="RageAudioMixThread">
            <Priority>PRIO_TIME_CRITICAL</Priority>
            <CpuAffinity>
                <Core value="4"/>
			</CpuAffinity>
          </Item>
          <Item key="RageAudioEngineThread">
            <Priority>PRIO_NORMAL</Priority>
            <CpuAffinity>
                <Core value="4"/>
			</CpuAffinity>
          </Item>
          <Item key="SaveBlockDataThread">
            <Priority>PRIO_BELOW_NORMAL</Priority>
            <CpuAffinity>
                <Core value="4"/>
			</CpuAffinity>
          </Item>
          <Item key="RageDvdStreamer">
            <Priority>PRIO_BELOW_NORMAL</Priority>
            <CpuAffinity>
                <Core value="4"/>
			</CpuAffinity>
          </Item>
          <Item key="RageHddStreamer">
            <Priority>PRIO_LOWEST</Priority>
            <CpuAffinity>
                <Core value="4"/>
			</CpuAffinity>
          </Item>
          <Item key="RecordingThreadVehicle">
            <Priority>PRIO_ABOVE_NORMAL</Priority>
            <CpuAffinity>
                <Core value="5"/>
			</CpuAffinity>
          </Item>
          <Item key="NorthAudioUpdate">
            <Priority>PRIO_NORMAL</Priority>
            <CpuAffinity>
                <Core value="5"/>
			</CpuAffinity>
          </Item>
          <Item key="RageNetSend">
            <Priority>PRIO_BELOW_NORMAL</Priority>
            <CpuAffinity>
                <Core value="5"/>
			</CpuAffinity>
          </Item>
          <Item key="RageNetRecv">
            <Priority>PRIO_BELOW_NORMAL</Priority>
            <CpuAffinity>
                <Core value="5"/>
			</CpuAffinity>
          </Item>
          <Item key="RageVoiceChatWorker">
            <Priority>PRIO_BELOW_NORMAL</Priority>
            <CpuAffinity>
                <Core value="5"/>
			</CpuAffinity>
          </Item>
          <Item key="MediaEncoderThreadVideo">
            <Priority>PRIO_LOWEST</Priority>
            <CpuAffinity>
                <Core value="5"/>
			</CpuAffinity>
          </Item>
          <Item key="MediaDecoderThread">
            <Priority>PRIO_LOWEST</Priority>
            <CpuAffinity>
                <Core value="5"/>
			</CpuAffinity>
          </Item>
          <Item key="ReplayFileManager">
            <Priority>PRIO_LOWEST</Priority>
            <CpuAffinity>
                <Core value="5"/>
			</CpuAffinity>
          </Item>
          <Item key="ResourcePlacementThread">
            <Priority>PRIO_LOWEST</Priority>
            <CpuAffinity>
                <Core value="5"/>
			</CpuAffinity>
          </Item>
        </Threads>
			</Config>
		</Item>

    <Item>
			<Build>any</Build>
			<Platforms>ps4</Platforms>
      <Config type="CGameConfig">
				<PoolSizes>
          <Entries>
            <Item>
              <PoolName>CTask</PoolName>
              <PoolSize value="2100"/>
            </Item>
			<Item>
				<PoolName>OcclusionPathNode</PoolName>
				<PoolSize value="3450"/>
			</Item>
			<Item>
				<PoolName>OcclusionPortalInfo</PoolName>
				<PoolSize value="400"/>
			</Item>
          </Entries>
        </PoolSizes>
				<ConfigStreamingEngine>
					<PhysicalStreamingBuffer value="2354"/>
					<VirtualStreamingBuffer value="0"/>
				</ConfigStreamingEngine>
        <Threads>
          <Item key="Update">
            <Priority>PRIO_HIGHEST</Priority>
            <CpuAffinity>
                <Core value="0"/>
			</CpuAffinity>
          </Item>
          <Item key="ControlMgrUpdateThread">
            <Priority>PRIO_HIGHEST</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
          <Item key="AsyncShapeTestMgr">
            <Priority>PRIO_HIGHEST</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
          <Item key="RageDvdReader">
            <Priority>PRIO_TIME_CRITICAL</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
          <Item key="RecordingThreadDefault">
            <Priority>PRIO_ABOVE_NORMAL</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
          <Item key="ReplayQuantizeThread">
            <Priority>PRIO_BELOW_NORMAL</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
          <Item key="RecordingThreadGameAndObject">
            <Priority>PRIO_ABOVE_NORMAL</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
		  <Item key="MediaEncoderThreadAudio">
            <Priority>PRIO_LOWEST</Priority>
			<CpuAffinity>
                <Core value="2"/>
			</CpuAffinity>
          </Item>
          <Item key="RageAudioEngineThread">
            <Priority>PRIO_NORMAL</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
          <Item key="RageHddReader">
            <Priority>PRIO_TIME_CRITICAL</Priority>
            <CpuAffinity>
                <Core value="2"/>
			</CpuAffinity>
          </Item>
          <Item key="RmptfxUpdateThread">
            <Priority>PRIO_HIGHEST</Priority>
            <CpuAffinity>
                <Core value="2"/>
			</CpuAffinity>
          </Item>
          <Item key="RecordingThreadPed">
            <Priority>PRIO_ABOVE_NORMAL</Priority>
            <CpuAffinity>
                <Core value="2"/>
			</CpuAffinity>
          </Item>
          <Item key="PathServer">
            <Priority>PRIO_LOWEST</Priority>
            <CpuAffinity>
                <Core value="2"/>
			</CpuAffinity>
          </Item>
          <Item key="RageGpuSubmit">
            <Priority>PRIO_TIME_CRITICAL</Priority>
            <CpuAffinity>
                <Core value="3"/>
			</CpuAffinity>
          </Item>
          <Item key="RecordingThreadVehicle">
            <Priority>PRIO_ABOVE_NORMAL</Priority>
            <CpuAffinity>
                <Core value="3"/>
			</CpuAffinity>
          </Item>
          <Item key="NetLogSpooler">
            <Priority>PRIO_NORMAL</Priority>
            <CpuAffinity>
                <Core value="3"/>
			</CpuAffinity>
          </Item>
          <Item key="NetRelay">
            <Priority>PRIO_NORMAL</Priority>
            <CpuAffinity>
                <Core value="3"/>
			</CpuAffinity>
          </Item>
          <Item key="RageLogfile">
            <Priority>PRIO_NORMAL</Priority>
            <CpuAffinity>
                <Core value="3"/>
			</CpuAffinity>
          </Item>
          <Item key="RageNetTcpWorker">
            <Priority>PRIO_NORMAL</Priority>
            <CpuAffinity>
                <Core value="3"/>
			</CpuAffinity>
          </Item>
          <Item key="ResourcePlacementThread">
            <Priority>PRIO_LOWEST</Priority>
            <CpuAffinity>
                <Core value="3"/>
			</CpuAffinity>
          </Item>
          <Item key="RageAudioMixThread">
            <Priority>PRIO_TIME_CRITICAL</Priority>
            <CpuAffinity>
                <Core value="4"/>
			</CpuAffinity>
          </Item>
          <Item key="SaveBlockDataThread">
            <Priority>PRIO_NORMAL</Priority>
            <CpuAffinity>
                <Core value="4"/>
			</CpuAffinity>
          </Item>
          <Item key="RageDvdStreamer">
            <Priority>PRIO_BELOW_NORMAL</Priority>
            <CpuAffinity>
                <Core value="4"/>
			</CpuAffinity>
          </Item>
          <Item key="RageHddStreamer">
            <Priority>PRIO_LOWEST</Priority>
            <CpuAffinity>
                <Core value="4"/>
			</CpuAffinity>
          </Item>
          <Item key="Render">
            <Priority>PRIO_HIGHEST</Priority>
            <CpuAffinity>
                <Core value="5"/>
			</CpuAffinity>
          </Item>
          <Item key="NorthAudioUpdate">
            <Priority>PRIO_NORMAL</Priority>
            <CpuAffinity>
                <Core value="5"/>
			</CpuAffinity>
          </Item>
          <Item key="RageNetSend">
            <Priority>PRIO_BELOW_NORMAL</Priority>
            <CpuAffinity>
                <Core value="5"/>
			</CpuAffinity>
          </Item>
          <Item key="RageNetRecv">
            <Priority>PRIO_BELOW_NORMAL</Priority>
            <CpuAffinity>
                <Core value="5"/>
			</CpuAffinity>
          </Item>
          <Item key="RageVoiceChatWorker">
            <Priority>PRIO_BELOW_NORMAL</Priority>
            <CpuAffinity>
                <Core value="5"/>
			</CpuAffinity>
          </Item>
		  <Item key="MediaEncoderThreadVideo">
            <Priority>PRIO_LOWEST</Priority>
            <CpuAffinity>
                <Core value="5"/>
			</CpuAffinity>
          </Item>
          <Item key="ReplayFileManager">
            <Priority>PRIO_LOWEST</Priority>
            <CpuAffinity>
                <Core value="5"/>
			</CpuAffinity>
          </Item>
        </Threads>
			</Config>
		</Item>
    <Item>
      <Build>Debug</Build>
			<Platforms>ps4</Platforms>
			<Config type="CGameConfig">
        <Threads>
          <Item key="RageNetSend">
            <Priority>PRIO_BELOW_NORMAL</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
          <Item key="RageNetRecv">
            <Priority>PRIO_BELOW_NORMAL</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
          <Item key="RageVoiceChatWorker">
            <Priority>PRIO_BELOW_NORMAL</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
          <Item key="PathServer">
            <Priority>PRIO_IDLE</Priority>
            <CpuAffinity>
                <Core value="1"/>
			</CpuAffinity>
          </Item>
        </Threads>
			</Config>
		</Item>

    <Item>
      <Build>any</Build>
      <Platforms>x64</Platforms>
      <Config type="CGameConfig">
        <Threads>
          <Item key="Update">
            <Priority>PRIO_TIME_CRITICAL</Priority>
          </Item>
          <Item key="ControlMgrUpdateThread">
            <Priority>PRIO_HIGHEST</Priority>
          </Item>
          <Item key="AsyncShapeTestMgr">
            <Priority>PRIO_HIGHEST</Priority>
          </Item>
          <Item key="RageDvdReader">
            <Priority>PRIO_TIME_CRITICAL</Priority>
          </Item>
          <Item key="RecordingThreadDefault">
            <Priority>PRIO_ABOVE_NORMAL</Priority>
          </Item>
          <Item key="ReplayQuantizeThread">
            <Priority>PRIO_BELOW_NORMAL</Priority>
          </Item>
          <Item key="RecordingThreadGameAndObject">
            <Priority>PRIO_ABOVE_NORMAL</Priority>
          </Item>
          <Item key="RageAudioEngineThread">
            <Priority>PRIO_NORMAL</Priority>
          </Item>
          <Item key="RageHddReader">
            <Priority>PRIO_TIME_CRITICAL</Priority>
          </Item>
          <Item key="RmptfxUpdateThread">
            <Priority>PRIO_HIGHEST</Priority>
          </Item>
          <Item key="RecordingThreadPed">
            <Priority>PRIO_ABOVE_NORMAL</Priority>
          </Item>
          <Item key="PathServer">
            <Priority>PRIO_LOWEST</Priority>
          </Item>
          <Item key="RecordingThreadVehicle">
            <Priority>PRIO_ABOVE_NORMAL</Priority>
          </Item>
          <Item key="ResourcePlacementThread">
            <Priority>PRIO_NORMAL</Priority>
          </Item>
          <Item key="RageAudioMixThread">
            <Priority>PRIO_TIME_CRITICAL</Priority>
          </Item>
          <Item key="SaveBlockDataThread">
            <Priority>PRIO_NORMAL</Priority>
          </Item>
          <Item key="RageDvdStreamer">
            <Priority>PRIO_ABOVE_NORMAL</Priority>
          </Item>
          <Item key="RageHddStreamer">
            <Priority>PRIO_ABOVE_NORMAL</Priority>
          </Item>
          <Item key="Render">
            <Priority>PRIO_ABOVE_NORMAL</Priority>
          </Item>
          <Item key="NorthAudioUpdate">
            <Priority>PRIO_NORMAL</Priority>
          </Item>
          <Item key="RageNetSend">
            <Priority>PRIO_BELOW_NORMAL</Priority>
          </Item>
          <Item key="RageNetRecv">
            <Priority>PRIO_BELOW_NORMAL</Priority>
          </Item>
          <Item key="RageVoiceChatWorker">
            <Priority>PRIO_BELOW_NORMAL</Priority>
          </Item>
          <Item key="ReplayFileManager">
            <Priority>PRIO_LOWEST</Priority>
          </Item>
        </Threads>
      </Config>
    </Item>
    <Item>
			<Build>b2189</Build>
			<Platforms>any</Platforms>
			<Config type="CGameConfig">
				<ConfigScriptStackSizes>
					<StackSizeData>
							<Item>
									<StackName>MICRO</StackName>
									<SizeOfStack value="128"/>
									<NumberOfStacksOfThisSize value="20"/>
							</Item>
							<Item>
									<StackName>MINI</StackName>
									<SizeOfStack value="512"/>
									<NumberOfStacksOfThisSize value="20"/>
							</Item>
							<Item>
									<StackName>DEFAULT</StackName>
									<SizeOfStack value="1424"/>
									<NumberOfStacksOfThisSize value="68"/>
							</Item>
							<Item>
									<StackName>SPECIAL_ABILITY</StackName>
									<SizeOfStack value="1828"/>
									<NumberOfStacksOfThisSize value="13"/>
							</Item>
							<Item>
									<StackName>FRIEND</StackName>
									<SizeOfStack value="2050"/>
									<NumberOfStacksOfThisSize value="12"/>
							</Item>
							<Item>
									<StackName>SHOP</StackName>
									<SizeOfStack value="2324"/>
									<NumberOfStacksOfThisSize value="6"/>
							</Item>
							<Item>
									<StackName>CELLPHONE</StackName>
									<SizeOfStack value="2552"/>
									<NumberOfStacksOfThisSize value="2"/>
							</Item>
							<Item>
									<StackName>VEHICLE_SPAWN</StackName>
									<SizeOfStack value="3568"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>CAR_MOD_SHOP</StackName>
									<SizeOfStack value="3472"/>
									<NumberOfStacksOfThisSize value="2"/>
							</Item>
							<Item>
									<StackName>PAUSE_MENU_SCRIPT</StackName>
									<SizeOfStack value="3076"/>
									<NumberOfStacksOfThisSize value="2"/>
							</Item>
							<Item>
									<StackName>APP_INTERNET</StackName>
									<SizeOfStack value="4592"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>MULTIPLAYER_MISSION</StackName>
									<SizeOfStack value="4500"/>
									<NumberOfStacksOfThisSize value="19"/>
							</Item>
							<Item>
									<StackName>CONTACTS_APP</StackName>
									<SizeOfStack value="4000"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>INTERACTION_MENU</StackName>
									<SizeOfStack value="9800"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>SCRIPT_XML</StackName>
									<SizeOfStack value="8344"/>
									<NumberOfStacksOfThisSize value="4"/>
							</Item>
							<Item>
									<StackName>PROPERTY_INT</StackName>
									<SizeOfStack value="19400"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>ACTIVITY_CREATOR_INT</StackName>
									<SizeOfStack value="15900"/>
									<NumberOfStacksOfThisSize value="3"/>
							</Item>
							<Item>
									<StackName>SMPL_INTERIOR</StackName>
									<SizeOfStack value="2512"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>WAREHOUSE</StackName>
									<SizeOfStack value="14000"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>IE_DELIVERY</StackName>
									<SizeOfStack value="2324"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>SHOP_CONTROLLER</StackName>
									<SizeOfStack value="3200"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>AM_MP_YACHT</StackName>
									<SizeOfStack value="5000"/>
									<NumberOfStacksOfThisSize value="3"/>
							</Item>
							<Item>
									<StackName>INGAMEHUD</StackName>
									<SizeOfStack value="4600"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>TRANSITION</StackName>
									<SizeOfStack value="8032"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>FMMC_LAUNCHER</StackName>
									<SizeOfStack value="20000"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>MULTIPLAYER_FREEMODE</StackName>
									<SizeOfStack value="65000"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>MISSION</StackName>
									<SizeOfStack value="37500"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>MP_LAUNCH_SCRIPT</StackName>
									<SizeOfStack value="30750"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
					</StackSizeData>
				</ConfigScriptStackSizes>
			</Config>
		</Item>
    <Item>
			<Build>b2372</Build>
			<Platforms>any</Platforms>
			<Config type="CGameConfig">
				<ConfigScriptStackSizes>
					<StackSizeData>
							<Item>
									<StackName>MICRO</StackName>
									<SizeOfStack value="128"/>
									<NumberOfStacksOfThisSize value="20"/>
							</Item>
							<Item>
									<StackName>MINI</StackName>
									<SizeOfStack value="512"/>
									<NumberOfStacksOfThisSize value="20"/>
							</Item>
							<Item>
									<StackName>DEFAULT</StackName>
									<SizeOfStack value="1424"/>
									<NumberOfStacksOfThisSize value="68"/>
							</Item>
							<Item>
									<StackName>SPECIAL_ABILITY</StackName>
									<SizeOfStack value="1828"/>
									<NumberOfStacksOfThisSize value="13"/>
							</Item>
							<Item>
									<StackName>FRIEND</StackName>
									<SizeOfStack value="2050"/>
									<NumberOfStacksOfThisSize value="12"/>
							</Item>
							<Item>
									<StackName>SHOP</StackName>
									<SizeOfStack value="2324"/>
									<NumberOfStacksOfThisSize value="6"/>
							</Item>
							<Item>
									<StackName>CELLPHONE</StackName>
									<SizeOfStack value="2552"/>
									<NumberOfStacksOfThisSize value="2"/>
							</Item>
							<Item>
									<StackName>VEHICLE_SPAWN</StackName>
									<SizeOfStack value="3568"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>CAR_MOD_SHOP</StackName>
									<SizeOfStack value="3600"/>
									<NumberOfStacksOfThisSize value="2"/>
							</Item>
							<Item>
									<StackName>PAUSE_MENU_SCRIPT</StackName>
									<SizeOfStack value="3076"/>
									<NumberOfStacksOfThisSize value="2"/>
							</Item>
							<Item>
									<StackName>APP_INTERNET</StackName>
									<SizeOfStack value="4592"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>MULTIPLAYER_MISSION</StackName>
									<SizeOfStack value="4500"/>
									<NumberOfStacksOfThisSize value="19"/>
							</Item>
							<Item>
									<StackName>CONTACTS_APP</StackName>
									<SizeOfStack value="4000"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>INTERACTION_MENU</StackName>
									<SizeOfStack value="9800"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>SCRIPT_XML</StackName>
									<SizeOfStack value="8344"/>
									<NumberOfStacksOfThisSize value="4"/>
							</Item>
							<Item>
									<StackName>PROPERTY_INT</StackName>
									<SizeOfStack value="19400"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>ACTIVITY_CREATOR_INT</StackName>
									<SizeOfStack value="15900"/>
									<NumberOfStacksOfThisSize value="3"/>
							</Item>
							<Item>
									<StackName>SMPL_INTERIOR</StackName>
									<SizeOfStack value="2512"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>WAREHOUSE</StackName>
									<SizeOfStack value="14000"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>IE_DELIVERY</StackName>
									<SizeOfStack value="2324"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>SHOP_CONTROLLER</StackName>
									<SizeOfStack value="3200"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>AM_MP_YACHT</StackName>
									<SizeOfStack value="5000"/>
									<NumberOfStacksOfThisSize value="3"/>
							</Item>
							<Item>
									<StackName>INGAMEHUD</StackName>
									<SizeOfStack value="4600"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>TRANSITION</StackName>
									<SizeOfStack value="8032"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>FMMC_LAUNCHER</StackName>
									<SizeOfStack value="20000"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>MULTIPLAYER_FREEMODE</StackName>
									<SizeOfStack value="66000"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>MISSION</StackName>
									<SizeOfStack value="41500"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>MP_LAUNCH_SCRIPT</StackName>
									<SizeOfStack value="30750"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
					</StackSizeData>
				</ConfigScriptStackSizes>
			</Config>
		</Item>
		<Item>
			<Build>b2545</Build>
			<Platforms>any</Platforms>
			<Config type="CGameConfig">
				<ConfigScriptStackSizes>
					<StackSizeData>
						<Item>
								<StackName>MICRO</StackName>
								<SizeOfStack value="128"/>
								<NumberOfStacksOfThisSize value="20"/>
						</Item>
						<Item>
								<StackName>MINI</StackName>
								<SizeOfStack value="512"/>
								<NumberOfStacksOfThisSize value="20"/>
						</Item>
						<Item>
								<StackName>DEFAULT</StackName>
								<SizeOfStack value="1424"/>
								<NumberOfStacksOfThisSize value="68"/>
						</Item>
						<Item>
								<StackName>SPECIAL_ABILITY</StackName>
								<SizeOfStack value="1828"/>
								<NumberOfStacksOfThisSize value="13"/>
						</Item>
						<Item>
								<StackName>FRIEND</StackName>
								<SizeOfStack value="2050"/>
								<NumberOfStacksOfThisSize value="12"/>
						</Item>
						<Item>
								<StackName>SHOP</StackName>
								<SizeOfStack value="2324"/>
								<NumberOfStacksOfThisSize value="6"/>
						</Item>
						<Item>
								<StackName>CELLPHONE</StackName>
								<SizeOfStack value="2552"/>
								<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
								<StackName>VEHICLE_SPAWN</StackName>
								<SizeOfStack value="3568"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>CAR_MOD_SHOP</StackName>
								<SizeOfStack value="3600"/>
								<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
								<StackName>PAUSE_MENU_SCRIPT</StackName>
								<SizeOfStack value="3076"/>
								<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
								<StackName>APP_INTERNET</StackName>
								<SizeOfStack value="4592"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>MULTIPLAYER_MISSION</StackName>
								<SizeOfStack value="4500"/>
								<NumberOfStacksOfThisSize value="19"/>
						</Item>
						<Item>
								<StackName>CONTACTS_APP</StackName>
								<SizeOfStack value="4000"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>INTERACTION_MENU</StackName>
								<SizeOfStack value="9800"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>SCRIPT_XML</StackName>
								<SizeOfStack value="8344"/>
								<NumberOfStacksOfThisSize value="4"/>
						</Item>
						<Item>
								<StackName>PROPERTY_INT</StackName>
								<SizeOfStack value="19400"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>ACTIVITY_CREATOR_INT</StackName>
								<SizeOfStack value="15900"/>
								<NumberOfStacksOfThisSize value="3"/>
						</Item>
						<Item>
								<StackName>SMPL_INTERIOR</StackName>
								<SizeOfStack value="2512"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>WAREHOUSE</StackName>
								<SizeOfStack value="14000"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>IE_DELIVERY</StackName>
								<SizeOfStack value="2324"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>SHOP_CONTROLLER</StackName>
								<SizeOfStack value="3200"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>AM_MP_YACHT</StackName>
								<SizeOfStack value="5000"/>
								<NumberOfStacksOfThisSize value="3"/>
						</Item>
						<Item>
								<StackName>INGAMEHUD</StackName>
								<SizeOfStack value="4600"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>TRANSITION</StackName>
								<SizeOfStack value="8032"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>FMMC_LAUNCHER</StackName>
								<SizeOfStack value="21000"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>MULTIPLAYER_FREEMODE</StackName>
								<SizeOfStack value="66000"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>MISSION</StackName>
								<SizeOfStack value="44500"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>MP_LAUNCH_SCRIPT</StackName>
								<SizeOfStack value="30750"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
					</StackSizeData>
				</ConfigScriptStackSizes>
			</Config>
		</Item>
		<Item>
			<Build>b2612</Build>
			<Platforms>any</Platforms>
			<Config type="CGameConfig">
				<ConfigScriptStackSizes>
					<StackSizeData>
						<Item>
								<StackName>MICRO</StackName>
								<SizeOfStack value="128"/>
								<NumberOfStacksOfThisSize value="20"/>
						</Item>
						<Item>
								<StackName>MINI</StackName>
								<SizeOfStack value="512"/>
								<NumberOfStacksOfThisSize value="20"/>
						</Item>
						<Item>
								<StackName>DEFAULT</StackName>
								<SizeOfStack value="1424"/>
								<NumberOfStacksOfThisSize value="68"/>
						</Item>
						<Item>
								<StackName>SPECIAL_ABILITY</StackName>
								<SizeOfStack value="1828"/>
								<NumberOfStacksOfThisSize value="13"/>
						</Item>
						<Item>
								<StackName>FRIEND</StackName>
								<SizeOfStack value="2050"/>
								<NumberOfStacksOfThisSize value="12"/>
						</Item>
						<Item>
								<StackName>SHOP</StackName>
								<SizeOfStack value="2324"/>
								<NumberOfStacksOfThisSize value="6"/>
						</Item>
						<Item>
								<StackName>CELLPHONE</StackName>
								<SizeOfStack value="2552"/>
								<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
								<StackName>VEHICLE_SPAWN</StackName>
								<SizeOfStack value="3568"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>CAR_MOD_SHOP</StackName>
								<SizeOfStack value="3600"/>
								<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
								<StackName>PAUSE_MENU_SCRIPT</StackName>
								<SizeOfStack value="3076"/>
								<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
								<StackName>APP_INTERNET</StackName>
								<SizeOfStack value="4592"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>MULTIPLAYER_MISSION</StackName>
								<SizeOfStack value="4500"/>
								<NumberOfStacksOfThisSize value="19"/>
						</Item>
						<Item>
								<StackName>CONTACTS_APP</StackName>
								<SizeOfStack value="4000"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>INTERACTION_MENU</StackName>
								<SizeOfStack value="9800"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>SCRIPT_XML</StackName>
								<SizeOfStack value="8344"/>
								<NumberOfStacksOfThisSize value="4"/>
						</Item>
						<Item>
								<StackName>PROPERTY_INT</StackName>
								<SizeOfStack value="19400"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>ACTIVITY_CREATOR_INT</StackName>
								<SizeOfStack value="15900"/>
								<NumberOfStacksOfThisSize value="3"/>
						</Item>
						<Item>
								<StackName>SMPL_INTERIOR</StackName>
								<SizeOfStack value="2512"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>WAREHOUSE</StackName>
								<SizeOfStack value="14100"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>IE_DELIVERY</StackName>
								<SizeOfStack value="2324"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>SHOP_CONTROLLER</StackName>
								<SizeOfStack value="3200"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>AM_MP_YACHT</StackName>
								<SizeOfStack value="5000"/>
								<NumberOfStacksOfThisSize value="3"/>
						</Item>
						<Item>
								<StackName>INGAMEHUD</StackName>
								<SizeOfStack value="4600"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>TRANSITION</StackName>
								<SizeOfStack value="8032"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>FMMC_LAUNCHER</StackName>
								<SizeOfStack value="21000"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>MULTIPLAYER_FREEMODE</StackName>
								<SizeOfStack value="66000"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>MISSION</StackName>
								<SizeOfStack value="44500"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
								<StackName>MP_LAUNCH_SCRIPT</StackName>
								<SizeOfStack value="32750"/>
								<NumberOfStacksOfThisSize value="1"/>
						</Item>
					</StackSizeData>
			</ConfigScriptStackSizes>
			</Config>
		</Item>
		<Item>
			<Build>b2699</Build>
			<Platforms>any</Platforms>
			<Config type="CGameConfig">
				<ConfigScriptStackSizes>
					<StackSizeData>
							<Item>
									<StackName>MICRO</StackName>
									<SizeOfStack value="128"/>
									<NumberOfStacksOfThisSize value="20"/>
							</Item>
							<Item>
									<StackName>MINI</StackName>
									<SizeOfStack value="512"/>
									<NumberOfStacksOfThisSize value="20"/>
							</Item>
							<Item>
									<StackName>DEFAULT</StackName>
									<SizeOfStack value="1424"/>
									<NumberOfStacksOfThisSize value="68"/>
							</Item>
							<Item>
									<StackName>SPECIAL_ABILITY</StackName>
									<SizeOfStack value="1828"/>
									<NumberOfStacksOfThisSize value="13"/>
							</Item>
							<Item>
									<StackName>FRIEND</StackName>
									<SizeOfStack value="2050"/>
									<NumberOfStacksOfThisSize value="12"/>
							</Item>
							<Item>
									<StackName>SHOP</StackName>
									<SizeOfStack value="2324"/>
									<NumberOfStacksOfThisSize value="6"/>
							</Item>
							<Item>
									<StackName>CELLPHONE</StackName>
									<SizeOfStack value="2552"/>
									<NumberOfStacksOfThisSize value="2"/>
							</Item>
							<Item>
									<StackName>VEHICLE_SPAWN</StackName>
									<SizeOfStack value="3568"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>CAR_MOD_SHOP</StackName>
									<SizeOfStack value="3600"/>
									<NumberOfStacksOfThisSize value="2"/>
							</Item>
							<Item>
									<StackName>PAUSE_MENU_SCRIPT</StackName>
									<SizeOfStack value="3076"/>
									<NumberOfStacksOfThisSize value="2"/>
							</Item>
							<Item>
									<StackName>APP_INTERNET</StackName>
									<SizeOfStack value="4592"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>MULTIPLAYER_MISSION</StackName>
									<SizeOfStack value="5050"/>
									<NumberOfStacksOfThisSize value="19"/>
							</Item>
							<Item>
									<StackName>CONTACTS_APP</StackName>
									<SizeOfStack value="4000"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>INTERACTION_MENU</StackName>
									<SizeOfStack value="9800"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>SCRIPT_XML</StackName>
									<SizeOfStack value="8344"/>
									<NumberOfStacksOfThisSize value="4"/>
							</Item>
							<Item>
									<StackName>PROPERTY_INT</StackName>
									<SizeOfStack value="19400"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>ACTIVITY_CREATOR_INT</StackName>
									<SizeOfStack value="15900"/>
									<NumberOfStacksOfThisSize value="3"/>
							</Item>
							<Item>
									<StackName>SMPL_INTERIOR</StackName>
									<SizeOfStack value="2512"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>WAREHOUSE</StackName>
									<SizeOfStack value="14100"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>IE_DELIVERY</StackName>
									<SizeOfStack value="2324"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>SHOP_CONTROLLER</StackName>
									<SizeOfStack value="3200"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>AM_MP_YACHT</StackName>
									<SizeOfStack value="5000"/>
									<NumberOfStacksOfThisSize value="3"/>
							</Item>
							<Item>
									<StackName>INGAMEHUD</StackName>
									<SizeOfStack value="4600"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>TRANSITION</StackName>
									<SizeOfStack value="8032"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>FMMC_LAUNCHER</StackName>
									<SizeOfStack value="23000"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>MULTIPLAYER_FREEMODE</StackName>
									<SizeOfStack value="68500"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>MISSION</StackName>
									<SizeOfStack value="51000"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>MP_LAUNCH_SCRIPT</StackName>
									<SizeOfStack value="32750"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
						</StackSizeData>
				</ConfigScriptStackSizes>
			</Config>
		</Item>
		<Item>
			<Build>b2802</Build>
			<Platforms>any</Platforms>
			<Config type="CGameConfig">
					<ConfigScriptStackSizes>
						<StackSizeData>
							<Item>
									<StackName>MICRO</StackName>
									<SizeOfStack value="128"/>
									<NumberOfStacksOfThisSize value="20"/>
							</Item>
							<Item>
									<StackName>MINI</StackName>
									<SizeOfStack value="512"/>
									<NumberOfStacksOfThisSize value="20"/>
							</Item>
							<Item>
									<StackName>DEFAULT</StackName>
									<SizeOfStack value="1424"/>
									<NumberOfStacksOfThisSize value="68"/>
							</Item>
							<Item>
									<StackName>SPECIAL_ABILITY</StackName>
									<SizeOfStack value="1828"/>
									<NumberOfStacksOfThisSize value="13"/>
							</Item>
							<Item>
									<StackName>FRIEND</StackName>
									<SizeOfStack value="2050"/>
									<NumberOfStacksOfThisSize value="12"/>
							</Item>
							<Item>
									<StackName>SHOP</StackName>
									<SizeOfStack value="2324"/>
									<NumberOfStacksOfThisSize value="6"/>
							</Item>
							<Item>
									<StackName>CELLPHONE</StackName>
									<SizeOfStack value="2552"/>
									<NumberOfStacksOfThisSize value="2"/>
							</Item>
							<Item>
									<StackName>VEHICLE_SPAWN</StackName>
									<SizeOfStack value="3568"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>CAR_MOD_SHOP</StackName>
									<SizeOfStack value="3650"/>
									<NumberOfStacksOfThisSize value="2"/>
							</Item>
							<Item>
									<StackName>PAUSE_MENU_SCRIPT</StackName>
									<SizeOfStack value="3076"/>
									<NumberOfStacksOfThisSize value="2"/>
							</Item>
							<Item>
									<StackName>APP_INTERNET</StackName>
									<SizeOfStack value="4592"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>MULTIPLAYER_MISSION</StackName>
									<SizeOfStack value="5050"/>
									<NumberOfStacksOfThisSize value="19"/>
							</Item>
							<Item>
									<StackName>CONTACTS_APP</StackName>
									<SizeOfStack value="4000"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>INTERACTION_MENU</StackName>
									<SizeOfStack value="9800"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>SCRIPT_XML</StackName>
									<SizeOfStack value="8344"/>
									<NumberOfStacksOfThisSize value="4"/>
							</Item>
							<Item>
									<StackName>PROPERTY_INT</StackName>
									<SizeOfStack value="19400"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>ACTIVITY_CREATOR_INT</StackName>
									<SizeOfStack value="15900"/>
									<NumberOfStacksOfThisSize value="3"/>
							</Item>
							<Item>
									<StackName>SMPL_INTERIOR</StackName>
									<SizeOfStack value="2512"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>WAREHOUSE</StackName>
									<SizeOfStack value="14100"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>IE_DELIVERY</StackName>
									<SizeOfStack value="2324"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>SHOP_CONTROLLER</StackName>
									<SizeOfStack value="3800"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>AM_MP_YACHT</StackName>
									<SizeOfStack value="5000"/>
									<NumberOfStacksOfThisSize value="3"/>
							</Item>
							<Item>
									<StackName>INGAMEHUD</StackName>
									<SizeOfStack value="4600"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>TRANSITION</StackName>
									<SizeOfStack value="8032"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>FMMC_LAUNCHER</StackName>
									<SizeOfStack value="24000"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>MULTIPLAYER_FREEMODE</StackName>
									<SizeOfStack value="72500"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>MISSION</StackName>
									<SizeOfStack value="54000"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
							<Item>
									<StackName>MP_LAUNCH_SCRIPT</StackName>
									<SizeOfStack value="33750"/>
									<NumberOfStacksOfThisSize value="1"/>
							</Item>
					</StackSizeData>
				</ConfigScriptStackSizes>
			</Config>
		</Item>
		<Item>
			<Build>b2944</Build>
			<Platforms>any</Platforms>
			<Config type="CGameConfig">
  			<ConfigScriptStackSizes>
          <StackSizeData>
            <Item>
              <StackName>MICRO</StackName>
              <SizeOfStack value="128"/>
              <NumberOfStacksOfThisSize value="20"/>
            </Item>
            <Item>
              <StackName>MINI</StackName>
              <SizeOfStack value="512"/>
              <NumberOfStacksOfThisSize value="20"/>
            </Item>
            <Item>
              <StackName>DEFAULT</StackName>
              <SizeOfStack value="1424"/>
              <NumberOfStacksOfThisSize value="68"/>
            </Item>
            <Item>
              <StackName>SPECIAL_ABILITY</StackName>
              <SizeOfStack value="1828"/>
              <NumberOfStacksOfThisSize value="13"/>
            </Item>
            <Item>
              <StackName>FRIEND</StackName>
              <SizeOfStack value="2050"/>
              <NumberOfStacksOfThisSize value="12"/>
            </Item>
            <Item>
              <StackName>SHOP</StackName>
              <SizeOfStack value="2324"/>
              <NumberOfStacksOfThisSize value="6"/>
            </Item>
            <Item>
              <StackName>CELLPHONE</StackName>
              <SizeOfStack value="2552"/>
              <NumberOfStacksOfThisSize value="2"/>
            </Item>
            <Item>
              <StackName>VEHICLE_SPAWN</StackName>
              <SizeOfStack value="3568"/>
              <NumberOfStacksOfThisSize value="1"/>
            </Item>
            <Item>
              <StackName>CAR_MOD_SHOP</StackName>
              <SizeOfStack value="3650"/>
              <NumberOfStacksOfThisSize value="2"/>
            </Item>
            <Item>
              <StackName>PAUSE_MENU_SCRIPT</StackName>
              <SizeOfStack value="3076"/>
              <NumberOfStacksOfThisSize value="2"/>
            </Item>
            <Item>
              <StackName>APP_INTERNET</StackName>
              <SizeOfStack value="4592"/>
              <NumberOfStacksOfThisSize value="1"/>
            </Item>
            <Item>
              <StackName>MULTIPLAYER_MISSION</StackName>
              <SizeOfStack value="5050"/>
              <NumberOfStacksOfThisSize value="19"/>
            </Item>
            <Item>
              <StackName>CONTACTS_APP</StackName>
              <SizeOfStack value="4000"/>
              <NumberOfStacksOfThisSize value="1"/>
            </Item>
            <Item>
              <StackName>INTERACTION_MENU</StackName>
              <SizeOfStack value="9800"/>
              <NumberOfStacksOfThisSize value="1"/>
            </Item>
            <Item>
              <StackName>SCRIPT_XML</StackName>
              <SizeOfStack value="8344"/>
              <NumberOfStacksOfThisSize value="4"/>
            </Item>
            <Item>
              <StackName>PROPERTY_INT</StackName>
              <SizeOfStack value="19400"/>
              <NumberOfStacksOfThisSize value="1"/>
            </Item>
            <Item>
              <StackName>ACTIVITY_CREATOR_INT</StackName>
              <SizeOfStack value="15900"/>
              <NumberOfStacksOfThisSize value="3"/>
            </Item>
            <Item>
              <StackName>SMPL_INTERIOR</StackName>
              <SizeOfStack value="2512"/>
              <NumberOfStacksOfThisSize value="1"/>
            </Item>
            <Item>
              <StackName>WAREHOUSE</StackName>
              <SizeOfStack value="14100"/>
              <NumberOfStacksOfThisSize value="1"/>
            </Item>
            <Item>
              <StackName>IE_DELIVERY</StackName>
              <SizeOfStack value="2324"/>
              <NumberOfStacksOfThisSize value="1"/>
            </Item>
            <Item>
              <StackName>SHOP_CONTROLLER</StackName>
              <SizeOfStack value="3800"/>
              <NumberOfStacksOfThisSize value="1"/>
            </Item>
            <Item>
              <StackName>AM_MP_YACHT</StackName>
              <SizeOfStack value="5000"/>
              <NumberOfStacksOfThisSize value="3"/>
            </Item>
            <Item>
              <StackName>INGAMEHUD</StackName>
              <SizeOfStack value="4600"/>
              <NumberOfStacksOfThisSize value="1"/>
            </Item>
            <Item>
            	<StackName>TRANSITION</StackName>
            	<SizeOfStack value="8032"/>
            	<NumberOfStacksOfThisSize value="1"/>
            </Item>
            <Item>
              <StackName>FMMC_LAUNCHER</StackName>
              <SizeOfStack value="24000"/>
              <NumberOfStacksOfThisSize value="1"/>
            </Item>
            <Item>
              <StackName>MULTIPLAYER_FREEMODE</StackName>
              <SizeOfStack value="80000"/>
              <NumberOfStacksOfThisSize value="1"/>
            </Item>
            <Item>
              <StackName>MISSION</StackName>
              <SizeOfStack value="57500"/>
              <NumberOfStacksOfThisSize value="1"/>
            </Item>
            <Item>
            	<StackName>MP_LAUNCH_SCRIPT</StackName>
            	<SizeOfStack value="33750"/>
            	<NumberOfStacksOfThisSize value="1"/>
            </Item>
          </StackSizeData>
       	</ConfigScriptStackSizes>
			</Config>
		</Item>
		<Item>
			<Build>b3095</Build>
			<Platforms>any</Platforms>
			<Config type="CGameConfig">
				<ConfigScriptStackSizes>
					<StackSizeData>
						<Item>
							<StackName>MICRO</StackName>
							<SizeOfStack value="128"/>
							<NumberOfStacksOfThisSize value="20"/>
						</Item>
						<Item>
							<StackName>MINI</StackName>
							<SizeOfStack value="512"/>
							<NumberOfStacksOfThisSize value="20"/>
						</Item>
						<Item>
							<StackName>DEFAULT</StackName>
							<SizeOfStack value="1424"/>
							<NumberOfStacksOfThisSize value="68"/>
						</Item>
						<Item>
							<StackName>SPECIAL_ABILITY</StackName>
							<SizeOfStack value="1828"/>
							<NumberOfStacksOfThisSize value="13"/>
						</Item>
						<Item>
							<StackName>FRIEND</StackName>
							<SizeOfStack value="2050"/>
							<NumberOfStacksOfThisSize value="12"/>
						</Item>
						<Item>
							<StackName>SHOP</StackName>
							<SizeOfStack value="2324"/>
							<NumberOfStacksOfThisSize value="6"/>
						</Item>
						<Item>
							<StackName>CELLPHONE</StackName>
							<SizeOfStack value="2552"/>
							<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
							<StackName>VEHICLE_SPAWN</StackName>
							<SizeOfStack value="3568"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>CAR_MOD_SHOP</StackName>
							<SizeOfStack value="3750"/>
							<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
							<StackName>PAUSE_MENU_SCRIPT</StackName>
							<SizeOfStack value="3076"/>
							<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
							<StackName>APP_INTERNET</StackName>
							<SizeOfStack value="4592"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>MULTIPLAYER_MISSION</StackName>
							<SizeOfStack value="5050"/>
							<NumberOfStacksOfThisSize value="19"/>
						</Item>
						<Item>
							<StackName>CONTACTS_APP</StackName>
							<SizeOfStack value="4000"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>INTERACTION_MENU</StackName>
							<SizeOfStack value="9800"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>SCRIPT_XML</StackName>
							<SizeOfStack value="8344"/>
							<NumberOfStacksOfThisSize value="4"/>
						</Item>
						<Item>
							<StackName>PROPERTY_INT</StackName>
							<SizeOfStack value="19400"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>ACTIVITY_CREATOR_INT</StackName>
							<SizeOfStack value="15900"/>
							<NumberOfStacksOfThisSize value="3"/>
						</Item>
						<Item>
							<StackName>SMPL_INTERIOR</StackName>
							<SizeOfStack value="2512"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>WAREHOUSE</StackName>
							<SizeOfStack value="14100"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>IE_DELIVERY</StackName>
							<SizeOfStack value="2324"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>SHOP_CONTROLLER</StackName>
							<SizeOfStack value="3800"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>AM_MP_YACHT</StackName>
							<SizeOfStack value="5000"/>
							<NumberOfStacksOfThisSize value="3"/>
						</Item>
						<Item>
							<StackName>INGAMEHUD</StackName>
							<SizeOfStack value="4600"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>TRANSITION</StackName>
							<SizeOfStack value="8032"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>FMMC_LAUNCHER</StackName>
							<SizeOfStack value="26000"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>MULTIPLAYER_FREEMODE</StackName>
							<SizeOfStack value="82500"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>MISSION</StackName>
							<SizeOfStack value="60500"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>MP_LAUNCH_SCRIPT</StackName>
							<SizeOfStack value="34750"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
					</StackSizeData>
				</ConfigScriptStackSizes>
			</Config>
		</Item>
		<Item>
			<Build>b3258</Build>
			<Platforms>any</Platforms>
			<Config type="CGameConfig">
				<ConfigScriptStackSizes>
					<StackSizeData>
						<Item>
							<StackName>MICRO</StackName>
							<SizeOfStack value="128"/>
							<NumberOfStacksOfThisSize value="20"/>
						</Item>
						<Item>
							<StackName>MINI</StackName>
							<SizeOfStack value="512"/>
							<NumberOfStacksOfThisSize value="20"/>
						</Item>
						<Item>
							<StackName>DEFAULT</StackName>
							<SizeOfStack value="1424"/>
							<NumberOfStacksOfThisSize value="68"/>
						</Item>
						<Item>
							<StackName>SPECIAL_ABILITY</StackName>
							<SizeOfStack value="1828"/>
							<NumberOfStacksOfThisSize value="13"/>
						</Item>
						<Item>
							<StackName>FRIEND</StackName>
							<SizeOfStack value="2050"/>
							<NumberOfStacksOfThisSize value="12"/>
						</Item>
						<Item>
							<StackName>SHOP</StackName>
							<SizeOfStack value="2324"/>
							<NumberOfStacksOfThisSize value="6"/>
						</Item>
						<Item>
							<StackName>CELLPHONE</StackName>
							<SizeOfStack value="2552"/>
							<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
							<StackName>VEHICLE_SPAWN</StackName>
							<SizeOfStack value="3568"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>CAR_MOD_SHOP</StackName>
							<SizeOfStack value="3750"/>
							<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
							<StackName>PAUSE_MENU_SCRIPT</StackName>
							<SizeOfStack value="3076"/>
							<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
							<StackName>APP_INTERNET</StackName>
							<SizeOfStack value="4592"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>MULTIPLAYER_MISSION</StackName>
							<SizeOfStack value="5050"/>
							<NumberOfStacksOfThisSize value="19"/>
						</Item>
						<Item>
							<StackName>CONTACTS_APP</StackName>
							<SizeOfStack value="4000"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>INTERACTION_MENU</StackName>
							<SizeOfStack value="9800"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>SCRIPT_XML</StackName>
							<SizeOfStack value="8344"/>
							<NumberOfStacksOfThisSize value="4"/>
						</Item>
						<Item>
							<StackName>PROPERTY_INT</StackName>
							<SizeOfStack value="19400"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>ACTIVITY_CREATOR_INT</StackName>
							<SizeOfStack value="15900"/>
							<NumberOfStacksOfThisSize value="3"/>
						</Item>
						<Item>
							<StackName>SMPL_INTERIOR</StackName>
							<SizeOfStack value="2512"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>WAREHOUSE</StackName>
							<SizeOfStack value="14100"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>IE_DELIVERY</StackName>
							<SizeOfStack value="2324"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>SHOP_CONTROLLER</StackName>
							<SizeOfStack value="3800"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>AM_MP_YACHT</StackName>
							<SizeOfStack value="5000"/>
							<NumberOfStacksOfThisSize value="3"/>
						</Item>
						<Item>
							<StackName>INGAMEHUD</StackName>
							<SizeOfStack value="4600"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>TRANSITION</StackName>
							<SizeOfStack value="8032"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>FMMC_LAUNCHER</StackName>
							<SizeOfStack value="26000"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>MULTIPLAYER_FREEMODE</StackName>
							<SizeOfStack value="82500"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>MISSION</StackName>
							<SizeOfStack value="62500"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>MP_LAUNCH_SCRIPT</StackName>
							<SizeOfStack value="34750"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
					</StackSizeData>
				</ConfigScriptStackSizes>
			</Config>
		</Item>
		<Item>
			<Build>b3323</Build>
			<Platforms>any</Platforms>
			<Config type="CGameConfig">
				<ConfigScriptStackSizes>
					<StackSizeData>
						<Item>
							<StackName>MICRO</StackName>
							<SizeOfStack value="128"/>
							<NumberOfStacksOfThisSize value="20"/>
						</Item>
						<Item>
							<StackName>MINI</StackName>
							<SizeOfStack value="512"/>
							<NumberOfStacksOfThisSize value="20"/>
						</Item>
						<Item>
							<StackName>DEFAULT</StackName>
							<SizeOfStack value="1424"/>
							<NumberOfStacksOfThisSize value="68"/>
						</Item>
						<Item>
							<StackName>SPECIAL_ABILITY</StackName>
							<SizeOfStack value="1828"/>
							<NumberOfStacksOfThisSize value="13"/>
						</Item>
						<Item>
							<StackName>FRIEND</StackName>
							<SizeOfStack value="2050"/>
							<NumberOfStacksOfThisSize value="12"/>
						</Item>
						<Item>
							<StackName>SHOP</StackName>
							<SizeOfStack value="2324"/>
							<NumberOfStacksOfThisSize value="6"/>
						</Item>
						<Item>
							<StackName>CELLPHONE</StackName>
							<SizeOfStack value="2552"/>
							<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
							<StackName>VEHICLE_SPAWN</StackName>
							<SizeOfStack value="3568"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>CAR_MOD_SHOP</StackName>
							<SizeOfStack value="3750"/>
							<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
							<StackName>PAUSE_MENU_SCRIPT</StackName>
							<SizeOfStack value="3076"/>
							<NumberOfStacksOfThisSize value="2"/>
						</Item>
						<Item>
							<StackName>APP_INTERNET</StackName>
							<SizeOfStack value="4592"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>MULTIPLAYER_MISSION</StackName>
							<SizeOfStack value="5050"/>
							<NumberOfStacksOfThisSize value="19"/>
						</Item>
						<Item>
							<StackName>CONTACTS_APP</StackName>
							<SizeOfStack value="4000"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>INTERACTION_MENU</StackName>
							<SizeOfStack value="9800"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>SCRIPT_XML</StackName>
							<SizeOfStack value="8344"/>
							<NumberOfStacksOfThisSize value="4"/>
						</Item>
						<Item>
							<StackName>PROPERTY_INT</StackName>
							<SizeOfStack value="19400"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>ACTIVITY_CREATOR_INT</StackName>
							<SizeOfStack value="15900"/>
							<NumberOfStacksOfThisSize value="3"/>
						</Item>
						<Item>
							<StackName>SMPL_INTERIOR</StackName>
							<SizeOfStack value="2512"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>WAREHOUSE</StackName>
							<SizeOfStack value="14100"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>IE_DELIVERY</StackName>
							<SizeOfStack value="2324"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>SHOP_CONTROLLER</StackName>
							<SizeOfStack value="3800"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>AM_MP_YACHT</StackName>
							<SizeOfStack value="5000"/>
							<NumberOfStacksOfThisSize value="3"/>
						</Item>
						<Item>
							<StackName>INGAMEHUD</StackName>
							<SizeOfStack value="4600"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>TRANSITION</StackName>
							<SizeOfStack value="8032"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>FMMC_LAUNCHER</StackName>
							<SizeOfStack value="27000"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>MULTIPLAYER_FREEMODE</StackName>
							<SizeOfStack value="85000"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>MISSION</StackName>
							<SizeOfStack value="62500"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>MP_LAUNCH_SCRIPT</StackName>
							<SizeOfStack value="34750"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
					</StackSizeData>
				</ConfigScriptStackSizes>
			</Config>
		</Item>
		<Item>
			<Build>b3407</Build>
			<Platforms>any</Platforms>
			<Config type="CGameConfig">
                <ConfigScriptStackSizes>
                    <StackSizeData>
                        <Item>
                            <StackName>MICRO</StackName>
                            <SizeOfStack value="128"/>
                            <NumberOfStacksOfThisSize value="20"/>
                        </Item>
                        <Item>
                            <StackName>MINI</StackName>
                            <SizeOfStack value="512"/>
                            <NumberOfStacksOfThisSize value="20"/>
                        </Item>
                        <Item>
                            <StackName>DEFAULT</StackName>
                            <SizeOfStack value="1424"/>
                            <NumberOfStacksOfThisSize value="68"/>
                        </Item>
                        <Item>
                            <StackName>SPECIAL_ABILITY</StackName>
                            <SizeOfStack value="1828"/>
                            <NumberOfStacksOfThisSize value="13"/>
                        </Item>
                        <Item>
                            <StackName>FRIEND</StackName>
                            <SizeOfStack value="2050"/>
                            <NumberOfStacksOfThisSize value="12"/>
                        </Item>
                        <Item>
                            <StackName>SHOP</StackName>
                            <SizeOfStack value="2324"/>
                            <NumberOfStacksOfThisSize value="6"/>
                        </Item>
                        <Item>
                            <StackName>CELLPHONE</StackName>
                            <SizeOfStack value="2552"/>
                            <NumberOfStacksOfThisSize value="2"/>
                        </Item>
                        <Item>
                            <StackName>VEHICLE_SPAWN</StackName>
                            <SizeOfStack value="3568"/>
                            <NumberOfStacksOfThisSize value="1"/>
                        </Item>
                        <Item>
                            <StackName>CAR_MOD_SHOP</StackName>
                            <SizeOfStack value="3750"/>
                            <NumberOfStacksOfThisSize value="2"/>
                        </Item>
                        <Item>
                            <StackName>PAUSE_MENU_SCRIPT</StackName>
                            <SizeOfStack value="3076"/>
                            <NumberOfStacksOfThisSize value="2"/>
                        </Item>
                        <Item>
                            <StackName>APP_INTERNET</StackName>
                            <SizeOfStack value="4592"/>
                            <NumberOfStacksOfThisSize value="1"/>
                        </Item>
                        <Item>
                            <StackName>MULTIPLAYER_MISSION</StackName>
                            <SizeOfStack value="5050"/>
                            <NumberOfStacksOfThisSize value="19"/>
                        </Item>
                        <Item>
                            <StackName>CONTACTS_APP</StackName>
                            <SizeOfStack value="4000"/>
                            <NumberOfStacksOfThisSize value="1"/>
                        </Item>
                        <Item>
                            <StackName>INTERACTION_MENU</StackName>
                            <SizeOfStack value="9800"/>
                            <NumberOfStacksOfThisSize value="1"/>
                        </Item>
                        <Item>
                            <StackName>SCRIPT_XML</StackName>
                            <SizeOfStack value="8344"/>
                            <NumberOfStacksOfThisSize value="4"/>
                        </Item>
                        <Item>
                            <StackName>PROPERTY_INT</StackName>
                            <SizeOfStack value="19400"/>
                            <NumberOfStacksOfThisSize value="1"/>
                        </Item>
                        <Item>
                            <StackName>ACTIVITY_CREATOR_INT</StackName>
                            <SizeOfStack value="15900"/>
                            <NumberOfStacksOfThisSize value="3"/>
                        </Item>
                        <Item>
                            <StackName>SMPL_INTERIOR</StackName>
                            <SizeOfStack value="2512"/>
                            <NumberOfStacksOfThisSize value="1"/>
                        </Item>
                        <Item>
                            <StackName>WAREHOUSE</StackName>
                            <SizeOfStack value="14100"/>
                            <NumberOfStacksOfThisSize value="1"/>
                        </Item>
                        <Item>
                            <StackName>IE_DELIVERY</StackName>
                            <SizeOfStack value="2324"/>
                            <NumberOfStacksOfThisSize value="1"/>
                        </Item>
                        <Item>
                            <StackName>SHOP_CONTROLLER</StackName>
                            <SizeOfStack value="3800"/>
                            <NumberOfStacksOfThisSize value="1"/>
                        </Item>
                        <Item>
                            <StackName>AM_MP_YACHT</StackName>
                            <SizeOfStack value="5000"/>
                            <NumberOfStacksOfThisSize value="3"/>
                        </Item>
                        <Item>
                            <StackName>INGAMEHUD</StackName>
                            <SizeOfStack value="4600"/>
                            <NumberOfStacksOfThisSize value="1"/>
                        </Item>
                        <Item>
                            <StackName>TRANSITION</StackName>
                            <SizeOfStack value="8032"/>
                            <NumberOfStacksOfThisSize value="1"/>
                        </Item>
                        <Item>
                            <StackName>FMMC_LAUNCHER</StackName>
                            <SizeOfStack value="27000"/>
                            <NumberOfStacksOfThisSize value="1"/>
                        </Item>
                        <Item>
                            <StackName>MULTIPLAYER_FREEMODE</StackName>
                            <SizeOfStack value="87500"/>
                            <NumberOfStacksOfThisSize value="1"/>
                        </Item>
                        <Item>
                            <StackName>MISSION</StackName>
                            <SizeOfStack value="63500"/>
                            <NumberOfStacksOfThisSize value="1"/>
                        </Item>
                        <Item>
                            <StackName>MP_LAUNCH_SCRIPT</StackName>
                            <SizeOfStack value="34750"/>
                            <NumberOfStacksOfThisSize value="1"/>
                        </Item>


                    </StackSizeData>
                </ConfigScriptStackSizes>
			</Config>
		</Item>
		<Item>
			<Build>nonfinal</Build>
			<Platforms>any</Platforms>
			<Config type="CGameConfig">
				<ConfigScriptStackSizes>
					<StackSizeData>
						<Item>
							<StackName>DEBUG_SCRIPT</StackName>
							<SizeOfStack value="4080"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>SOAK_TEST</StackName>
							<SizeOfStack value="4088"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
						<Item>
							<StackName>NETWORK_BOT</StackName>
							<SizeOfStack value="4096"/>
							<NumberOfStacksOfThisSize value="31"/>
						</Item>
						<Item>
							<StackName>DEBUG_MENU</StackName>
							<SizeOfStack value="35000"/>
							<NumberOfStacksOfThisSize value="1"/>
						</Item>
					</StackSizeData>
				</ConfigScriptStackSizes>
			</Config>
		</Item>

		<Item>
			<Build>nonfinal</Build>
			<Platforms>any</Platforms>
			<Config type="CGameConfig">
				<ConfigScriptResourceExpectedMaximums>
					<ExpectedMaximumsArray>
						<Item>
							<ResourceTypeName>PTFX</ResourceTypeName>
							<ExpectedMaximum value="61"/>
						</Item>
						<Item>
							<ResourceTypeName>PTFX_ASSET</ResourceTypeName>
							<ExpectedMaximum value="20"/>
						</Item>
						<Item>
							<ResourceTypeName>FIRE</ResourceTypeName>
							<ExpectedMaximum value="32"/>
						</Item>
						<Item>
							<ResourceTypeName>PEDGROUP</ResourceTypeName>
							<ExpectedMaximum value="33"/>
						</Item>
						<Item>
							<ResourceTypeName>SEQUENCE_TASK</ResourceTypeName>
							<ExpectedMaximum value="97"/>
						</Item>
						<Item>
							<ResourceTypeName>DECISION_MAKER</ResourceTypeName>
							<ExpectedMaximum value="5"/>
						</Item>
						<Item>
							<ResourceTypeName>CHECKPOINT</ResourceTypeName>
							<ExpectedMaximum value="61"/>
						</Item>
						<Item>
							<ResourceTypeName>TEXTURE_DICTIONARY</ResourceTypeName>
							<ExpectedMaximum value="55"/>
						</Item>
						<Item>
							<ResourceTypeName>DRAWABLE_DICTIONARY</ResourceTypeName>
							<ExpectedMaximum value="40"/>
						</Item>
						<Item>
							<ResourceTypeName>CLOTH_DICTIONARY</ResourceTypeName>
							<ExpectedMaximum value="2"/>
						</Item>
						<Item>
							<ResourceTypeName>FRAG_DICTIONARY</ResourceTypeName>
							<ExpectedMaximum value="44"/>
						</Item>
						<Item>
							<ResourceTypeName>DRAWABLE</ResourceTypeName>
							<ExpectedMaximum value="34"/>
						</Item>
						<Item>
							<ResourceTypeName>COVERPOINT</ResourceTypeName>
							<ExpectedMaximum value="51"/>
						</Item>
						<Item>
							<ResourceTypeName>ANIMATION</ResourceTypeName>
							<ExpectedMaximum value="57"/>
						</Item>
						<Item>
							<ResourceTypeName>MODEL</ResourceTypeName>
							<ExpectedMaximum value="265"/>
						</Item>
						<Item>
							<ResourceTypeName>RADAR_BLIP</ResourceTypeName>
							<ExpectedMaximum value="1800"/>
						</Item>
						<Item>
							<ResourceTypeName>ROPE</ResourceTypeName>
							<ExpectedMaximum value="32"/>
						</Item>
						<Item>
							<ResourceTypeName>CAMERA</ResourceTypeName>
							<ExpectedMaximum value="27"/>
						</Item>
						<Item>
							<ResourceTypeName>PATROL_ROUTE</ResourceTypeName>
							<ExpectedMaximum value="30"/>
						</Item>
						<Item>
							<ResourceTypeName>MLO</ResourceTypeName>
							<ExpectedMaximum value="35"/>
						</Item>
						<Item>
							<ResourceTypeName>RELATIONSHIP_GROUP</ResourceTypeName>
							<ExpectedMaximum value="251"/>
						</Item>
						<Item>
							<ResourceTypeName>SCALEFORM_MOVIE</ResourceTypeName>
							<ExpectedMaximum value="20"/>
						</Item>
						<Item>
							<ResourceTypeName>STREAMED_SCRIPT</ResourceTypeName>
							<ExpectedMaximum value="53"/>
						</Item>
						<Item>
							<ResourceTypeName>ITEM_SET</ResourceTypeName>
							<ExpectedMaximum value="6"/>
						</Item>
						<Item>
							<ResourceTypeName>VOLUME</ResourceTypeName>
							<ExpectedMaximum value="1"/>
						</Item>
						<Item>
							<ResourceTypeName>SPEED_ZONE</ResourceTypeName>
							<ExpectedMaximum value="220"/>
						</Item>
						<Item>
							<ResourceTypeName>WEAPON_ASSET</ResourceTypeName>
							<ExpectedMaximum value="71"/>
						</Item>
						<Item>
							<ResourceTypeName>VEHICLE_ASSET</ResourceTypeName>
							<ExpectedMaximum value="6"/>
						</Item>
						<Item>
							<ResourceTypeName>POPSCHEDULE_OVERRIDE</ResourceTypeName>
							<ExpectedMaximum value="1"/>
						</Item>
						<Item>
							<ResourceTypeName>POPSCHEDULE_OVERRIDE_VEHICLE_MODEL</ResourceTypeName>
							<ExpectedMaximum value="12"/>
						</Item>
						<Item>
							<ResourceTypeName>SCENARIO_BLOCKING_AREA</ResourceTypeName>
							<ExpectedMaximum value="45"/>
						</Item>
						<Item>
							<ResourceTypeName>BINK_MOVIE</ResourceTypeName>
							<ExpectedMaximum value="2"/>
						</Item>
						<Item>
							<ResourceTypeName>MOVIE_MESH_SET</ResourceTypeName>
							<ExpectedMaximum value="13"/>
						</Item>
						<Item>
							<ResourceTypeName>SET_REL_GROUP_DONT_AFFECT_WANTED_LEVEL</ResourceTypeName>
							<ExpectedMaximum value="6"/>
						</Item>
						<Item>
							<ResourceTypeName>VEHICLE_COMBAT_AVOIDANCE_AREA</ResourceTypeName>
							<ExpectedMaximum value="3"/>
						</Item>
						<Item>
							<ResourceTypeName>DISPATCH_TIME_BETWEEN_SPAWN_ATTEMPTS</ResourceTypeName>
							<ExpectedMaximum value="3"/>
						</Item>
						<Item>
							<ResourceTypeName>DISPATCH_TIME_BETWEEN_SPAWN_ATTEMPTS_MULTIPLIER</ResourceTypeName>
							<ExpectedMaximum value="3"/>
						</Item>
						<Item>
							<ResourceTypeName>SYNCED_SCENE</ResourceTypeName>
							<ExpectedMaximum value="51"/>
						</Item>
						<Item>
							<ResourceTypeName>CLIP_SET</ResourceTypeName>
							<ExpectedMaximum value="15"/>
						</Item>
						<Item>
							<ResourceTypeName>VEHICLE_RECORDING</ResourceTypeName>
							<ExpectedMaximum value="105"/>
						</Item>
						<Item>
							<ResourceTypeName>MOVEMENT_MODE_ASSET</ResourceTypeName>
							<ExpectedMaximum value="5"/>
						</Item>
						<Item>
							<ResourceTypeName>CUT_SCENE</ResourceTypeName>
							<ExpectedMaximum value="3"/>
						</Item>
						<Item>
							<ResourceTypeName>CUT_FILE</ResourceTypeName>
							<ExpectedMaximum value="3"/>
						</Item>
            <Item>
              <ResourceTypeName>GHOST_SETTINGS</ResourceTypeName>
              <ExpectedMaximum value="2"/>
            </Item>
            <Item>
              <ResourceTypeName>PICKUP_GENERATION_MULTIPLIER</ResourceTypeName>
              <ExpectedMaximum value="2"/>
            </Item>
          </ExpectedMaximumsArray>
				</ConfigScriptResourceExpectedMaximums>
			</Config>
		</Item>


	</ConfigArray>
</fwAllConfigs>
