const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

const API_BASE = 'http://localhost:3000';
const TEST_OUTPUT_DIR = './test_downloads';

// Tạo thư mục test nếu chưa có
if (!fs.existsSync(TEST_OUTPUT_DIR)) {
    fs.mkdirSync(TEST_OUTPUT_DIR, { recursive: true });
    console.log(`✅ Created test directory: ${TEST_OUTPUT_DIR}`);
}

// Function để download file
function downloadFile(url, outputPath) {
    return new Promise((resolve, reject) => {
        const protocol = url.startsWith('https') ? https : http;
        
        const file = fs.createWriteStream(outputPath);
        
        protocol.get(url, (response) => {
            if (response.statusCode === 200) {
                response.pipe(file);
                
                file.on('finish', () => {
                    file.close();
                    const stats = fs.statSync(outputPath);
                    resolve({
                        success: true,
                        size: stats.size,
                        path: outputPath
                    });
                });
            } else {
                file.close();
                fs.unlinkSync(outputPath); // Xóa file rỗng
                reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
            }
        }).on('error', (err) => {
            file.close();
            if (fs.existsSync(outputPath)) {
                fs.unlinkSync(outputPath);
            }
            reject(err);
        });
    });
}

// Function để lấy danh sách files
async function getFilesList() {
    return new Promise((resolve, reject) => {
        http.get(`${API_BASE}/api/files`, (response) => {
            let data = '';
            
            response.on('data', (chunk) => {
                data += chunk;
            });
            
            response.on('end', () => {
                try {
                    const result = JSON.parse(data);
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            });
        }).on('error', reject);
    });
}

// Main test function
async function testDownloads() {
    console.log('🚀 Starting download tests...\n');
    
    try {
        // 1. Lấy danh sách files
        console.log('📋 Getting files list...');
        const filesResponse = await getFilesList();
        
        if (!filesResponse.success) {
            throw new Error('Failed to get files list');
        }
        
        const allFiles = filesResponse.data.items.filter(item => item.type === 'file');
        console.log(`✅ Found ${allFiles.length} files total\n`);
        
        // 2. Test download một vài files đầu tiên
        const testFiles = allFiles.slice(0, 5); // Test 5 files đầu
        
        console.log('🔽 Testing downloads...');
        let successCount = 0;
        let failCount = 0;
        
        for (const file of testFiles) {
            const fileName = path.basename(file.path);
            const outputPath = path.join(TEST_OUTPUT_DIR, fileName);
            
            try {
                console.log(`  Downloading: ${file.path}`);
                
                // Test download qua API
                const downloadUrl = `${API_BASE}/api/download/${file.path}`;
                const result = await downloadFile(downloadUrl, outputPath);
                
                console.log(`  ✅ SUCCESS: ${fileName} (${result.size} bytes)`);
                successCount++;
                
            } catch (error) {
                console.log(`  ❌ FAILED: ${fileName} - ${error.message}`);
                failCount++;
            }
        }
        
        console.log(`\n📊 Test Results:`);
        console.log(`  ✅ Successful downloads: ${successCount}`);
        console.log(`  ❌ Failed downloads: ${failCount}`);
        console.log(`  📁 Files saved to: ${TEST_OUTPUT_DIR}`);
        
        // 3. Test direct file access
        console.log(`\n🔗 Testing direct file access...`);
        const firstFile = testFiles[0];
        if (firstFile) {
            const directUrl = `${API_BASE}/files/${firstFile.path}`;
            const directOutputPath = path.join(TEST_OUTPUT_DIR, `direct_${path.basename(firstFile.path)}`);
            
            try {
                const result = await downloadFile(directUrl, directOutputPath);
                console.log(`  ✅ Direct access SUCCESS: ${result.size} bytes`);
            } catch (error) {
                console.log(`  ❌ Direct access FAILED: ${error.message}`);
            }
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Chạy test
testDownloads();
