# Dutch translations for PACKAGE package.
# Copyright (C) 2020 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Automatically generated, 2020.
#
# <AUTHOR> <EMAIL>, 2020.
# <AUTHOR> <EMAIL>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-12-30 22:37+0100\n"
"PO-Revision-Date: 2020-06-27 18:11+0000\n"
"Last-Translator: Weblate Admin <<EMAIL>>\n"
"Language-Team: Dutch <http://translations.cfx.re/projects/citizenfx/client/"
"nl/>\n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.1.1\n"

#: client/launcher/MiniDump.cpp:1264
#, c-format
msgid ""
"\n"
"\n"
"Exception details: %s"
msgstr ""
"\n"
"\n"
"Uitzonderingdetails: %s"

#: client/launcher/MiniDump.cpp:1269
#, c-format
msgid ""
"\n"
"\n"
"Legacy crash hash: %s"
msgstr ""
"\n"
"\n"
"Verouderde crash-hash: %s"

#: client/launcher/MiniDump.cpp:1274
#, c-format
msgid ""
"\n"
"Stack trace:\n"
"%s"
msgstr ""

#: components/net/src/NetLibrary.cpp:637
msgid ""
"# Couldn't connect\n"
"Failed to get info from server (tried 3 times).\n"
"\n"
"---\n"
"\n"
"If you are the server owner, are you sure you are allowing UDP packets to "
"and from the server?"
msgstr ""

#: components/net/src/NetLibrary.cpp:674
#, c-format
msgid ""
"# Timed out\n"
"Client -> server connection timed out. Please try again later.\n"
"\n"
"---\n"
"\n"
"%s\n"
"[Reconnect](cfx.re://reconnect)"
msgstr ""

#: client/launcher/MiniDump.cpp:1260
#, c-format
msgid ""
"%s caused %s to stop working. A crash report is being uploaded to the %s "
"developers."
msgstr ""
"%s zorgde ervoor dat %s niet meer werkt. Een foutrapport wordt momenteel "
"geupload naar de ontwikkelaars van %s."

#: client/launcher/Main.cpp:652
#, c-format
msgid ""
"%s could not create a file in the folder it is placed in. Please move your "
"installation out of Program Files or another protected folder."
msgstr ""
"Het lukte %s niet om een bestand te maken in de map waarin het zich bevindt. "
"Zorg ervoor dat je installatie niet in Program Files of een andere "
"beschermde map staat."

#: client/launcher/ViabilityChecks.cpp:100
#, fuzzy, c-format
#| msgid ""
#| "This product requires Security Update for Windows 7 for x64-based systems "
#| "(*********) to be installed to run. Please install it, and try again."
msgid ""
"%s requires the Windows Media Feature Pack for Windows N editions to be "
"installed to run. Please install it, and try again."
msgstr ""
"Voor dit product is de Beveiligingsupdate voor Windows 7 voor op x64-"
"gebaseerde computers (*********) vereist. Installeer deze, en probeer het "
"opnieuw."

#: client/launcher/MiniDump.cpp:1417
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: %s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: %s\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Handtekening van fout: %s\n"
"Meldingsnummer: %s\n"
"Je kunt Ctrl+C indrukken om dit bericht te kopiëren en later te plakken."

#: client/launcher/MiniDump.cpp:1356
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: ... [uploading]\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: ... [uploading]\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Handtekening van fout: %s\n"
"Meldingsnummer: ... [wordt geupload]\n"
"Je kunt Ctrl+C indrukken om dit bericht te kopiëren en later te plakken."

#: client/launcher/MiniDump.cpp:1421
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "%s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid "%sYou can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Handtekening van fout: %s\n"
"%s\n"
"Je kunt Ctrl+C indrukken om dit bericht te kopiëren en later te plakken."

#: components/net/src/NetLibrary.cpp:99
#, c-format
msgid "**Timeout info**: game=%s, recv=%s, send=%s\n"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:426
msgid ""
"A call into the Windows API took too long recently and led to a game stutter."
msgstr ""

#: client/launcher/MiniDump.cpp:1235
#, c-format
msgid "An error at %s"
msgstr "Een fout op adres %s"

#: client/launcher/Installer.cpp:127
#, c-format
msgid "Are you sure you want to remove %s from the installation root at %s?"
msgstr "Weet je zeker dat je %s uit de installatiemap %s wilt verwijderen?"

#: client/launcher/Bootstrap.cpp:34
#, c-format
msgid "Bootstrapping %s..."
msgstr "%s voorbereiden om te starten..."

#: client/launcher/Updater.cpp:624
#, c-format
msgid "Checking %s"
msgstr ""

#: client/launcher/MiniDump.cpp:1349
#, c-format
msgid "Crash signature: %s\n"
msgstr ""

#: client/launcher/GameCache.cpp:794
#, fuzzy, c-format
#| msgid ""
#| "DLC files are missing (or corrupted) in your game installation. Please "
#| "update or verify the game using Steam or the Social Club launcher and try "
#| "again. See http://rsg.ms/verify step 4 for more info."
msgid ""
"DLC files are missing (or corrupted) in your game installation. Please "
"update or verify the game using Steam, Epic Games Launcher or Rockstar Games "
"Launcher and try again. See http://rsg.ms/verify step 4 for more info.\n"
"Currently, the game installation in '%s' is being used.\n"
"Relevant files: \n"
"%s"
msgstr ""
"Er ontbreken DLC-bestanden in je spelinstallatie, of deze zijn beschadigd. "
"Update of verifieer het spel via Steam of de Social Club Launcher en probeer "
"het opnieuw. Zie ook stap 4 op http://rsg.ms/verify (Engelstalig) voor meer "
"informatie."

#: client/launcher/ViabilityChecks.cpp:57
#, c-format
msgid "DXGI 1.2 support is required to run this product %s"
msgstr "Ondersteuning voor DXGI 1.2 is benodigd om het product %s te starten"

#: client/launcher/Download.cpp:273
#, c-format
msgid "Downloaded %.2f/%.2f MB (%.0f%%, %.1f MB/s)"
msgstr ""

#: client/launcher/MiniDump.cpp:1231
#, c-format
msgid "Error %s"
msgstr "Fout %s"

#: client/launcher/InstallerExtraction.cpp:534
#, c-format
msgid "Extracting %s"
msgstr "%s uitpakken"

#: client/launcher/InstallerExtraction.cpp:270
#, c-format
msgid "Extracting %s (scanning)"
msgstr "%s uitpakken (scannen)"

#: client/launcher/MiniDump.cpp:1245
msgid "FiveM crashed... but we're on it!"
msgstr "FiveM werkt niet meer... maar we weten er van af!"

#: client/launcher/Main.cpp:633
msgid ""
"FiveM does not support running under elevated privileges. Please change your "
"Windows settings to not run FiveM as administrator.\n"
"The game will exit now."
msgstr ""
"FiveM kan niet gestart worden met verhoogde bevoegdheden. Wijzig je "
"instellingen in Windows zodat FiveM niet als beheerder gestart wordt.\n"
"Het spel zal zichzelf nu afsluiten."

#: client/launcher/MiniDump.cpp:1308
msgid "Game crashed: "
msgstr "Spel werkt niet meer: "

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:428
msgid ""
"Please close any software you have running in the background (including "
"Windows apps such as File Explorer or Task Manager)."
msgstr ""

#: client/launcher/ViabilityChecks.cpp:50
msgid "Please install Windows 7 SP1 or greater, and try again."
msgstr "Installeer Windows 7 SP1 of hoger, en probeer het opnieuw."

#: client/launcher/ViabilityChecks.cpp:54
msgid "Please install the Platform Update for Windows 7, and try again."
msgstr ""
"Installeer de update voor het Windows 7-platform, en probeer het opnieuw."

#: client/launcher/MiniDump.cpp:328 client/launcher/MiniDump.cpp:1447
msgid "Save information"
msgstr ""

#: client/launcher/MiniDump.cpp:1343
msgid ""
"Save information\n"
"Stores a file with crash information that you should copy and upload when "
"asking for help."
msgstr ""
"Informatie opslaan\n"
"Slaat een bestand op met diagnostische informatie welke je kan uploaden als "
"je om hulp vraagt."

#: client/launcher/GameSelect.cpp:194
msgid "Select the folder containing Grand Theft Auto V"
msgstr "Selecteer de map met Grand Theft Auto V"

#: components/ros-patches-five/src/AccountID.cpp:265
msgid "Signing in with Epic"
msgstr ""

#: components/ros-patches-five/src/AccountID.cpp:229
msgid "Signing in with Steam"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:424
msgid "Slow system performance detected"
msgstr ""

#: client/launcher/Download.cpp:511
msgid "Starting IPFS discovery..."
msgstr "IPFS-discoveryproces starten..."

#: client/launcher/ViabilityChecks.cpp:46
msgid "The game will exit now."
msgstr "Het spel zal zichzelf nu afsluiten."

#: client/launcher/GameCache.cpp:783
#, fuzzy, c-format
#| msgid ""
#| "The local %s game cache is outdated, and needs to be updated. This will "
#| "copy %.2f MB of data from the local disk, and download %.2f MB of data "
#| "from the internet.\n"
#| "Do you wish to continue?"
msgid ""
"The local %s game data is outdated, and needs to be updated. This will copy "
"%.2f MB of data from the local disk, and download %.2f MB of data from the "
"internet.\n"
"Do you wish to continue?"
msgstr ""
"De lokale cache voor %s is verouderd en moet worden bijgewerkt. Tijdens dit "
"proces zal %.2f MB aan gegevens gekopieerd worden vanaf de lokale schijf, en "
"%.2f MB aan gegevens gedownload worden vanaf het internet.\n"
"Wil je doorgaan?"

#: client/launcher/GameSelect.cpp:329
#, c-format
msgid "The selected path does not contain a %s file."
msgstr "Het geselecteerde pad bevat geen bestand genaamd %s."

#: client/launcher/ViabilityChecks.cpp:78
msgid ""
"This product requires Security Update for Windows 7 for x64-based systems "
"(*********) to be installed to run. Please install it, and try again."
msgstr ""
"Voor dit product is de Beveiligingsupdate voor Windows 7 voor op x64-"
"gebaseerde computers (*********) vereist. Installeer deze, en probeer het "
"opnieuw."

#: client/launcher/Main.cpp:809
msgid "Transitioning to another build..."
msgstr "Overstappen naar een andere versie..."

#: client/launcher/MiniDump.cpp:1300
msgid "Unhandled exception: "
msgstr "Onverwerkte uitzondering: "

#: client/launcher/Installer.cpp:125
#, c-format
msgid "Uninstall %s"
msgstr "%s verwijderen"

#: client/launcher/Installer.cpp:126
#, c-format
msgid "Uninstall %s?"
msgstr "%s verwijderen?"

#: client/launcher/Updater.cpp:502
#, c-format
msgid "Updating %s..."
msgstr "%s bijwerken..."

#: client/launcher/GameCache.cpp:1050
#, fuzzy
#| msgid "Updating game cache..."
msgid "Updating game storage..."
msgstr "Spelcache bijwerken..."

#: client/launcher/Updater.cpp:468
msgid "Verifying content..."
msgstr "Data verifiëren..."

#: client/launcher/GameCache.cpp:883
msgid "Verifying game content..."
msgstr "Speldata verifiëren..."

#: client/launcher/Main.cpp:810
msgid "We're getting there."
msgstr "Bijna klaar."

#: client/launcher/Main.cpp:613
#, c-format
msgid ""
"You are currently using an outdated version of Windows. This may lead to "
"issues using the %s client. Please update to Windows 10 version 1703 "
"(\"Creators Update\") or higher in case you are experiencing any issues. The "
"game will continue to start now."
msgstr ""
"Je gebruikt momenteel een verouderde versie van Windows. Dit kan problemen "
"veroorzaken met de %s-client. Update naar Windows 10 versie 1703 (\"Creators "
"Update\") of hoger wanneer je problemen ervaart. Het spel wordt nu opgestart."

#~ msgid ""
#~ "\n"
#~ "\n"
#~ "This is a fatal error because game unloading failed. Please report this "
#~ "issue and how to cause it (what server you played on, any resources/"
#~ "scripts, etc.) so this can be solved."
#~ msgstr ""
#~ "\n"
#~ "\n"
#~ "Dit is een fatale fout omdat het spel niet afgesloten kon worden. Maak "
#~ "indien mogelijk melding van dit probleem en hoe het te veroorzaken is "
#~ "(welke server je speelde, plug-ins/scripts, enz.) opdat dit opgelost kan "
#~ "worden."

#, c-format
#~ msgid ""
#~ "A game error (at %016llx) caused %s to stop working. A crash report has "
#~ "been uploaded to the %s developers.\n"
#~ "\n"
#~ "%s"
#~ msgstr ""
#~ "Een fout in het spel (adres %016llx) zorgde ervoor dat %s niet meer "
#~ "werkt. Een foutrapport wordt momenteel geupload naar de ontwikkelaars van "
#~ "%s.\n"
#~ "\n"
#~ "%s"

#, c-format
#~ msgid "RAGE error: %s"
#~ msgstr "RAGE-fout: %s"
