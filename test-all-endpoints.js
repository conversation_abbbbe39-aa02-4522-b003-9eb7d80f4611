const http = require('http');
const fs = require('fs');
const path = require('path');

const API_BASE = 'http://localhost:3000';

// Helper function để gọi API
function makeRequest(url, method = 'GET') {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port,
            path: urlObj.pathname + urlObj.search,
            method: method,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const result = JSON.parse(data);
                    resolve({
                        statusCode: res.statusCode,
                        data: result
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        data: data
                    });
                }
            });
        });

        req.on('error', reject);
        req.end();
    });
}

async function testAllEndpoints() {
    console.log('🧪 Testing All API Endpoints\n');
    console.log('=' * 50);

    // 1. Test Health Check
    console.log('1️⃣ Testing Health Check...');
    try {
        const health = await makeRequest(`${API_BASE}/api/health`);
        if (health.statusCode === 200 && health.data.success) {
            console.log('   ✅ Health check PASSED');
            console.log(`   📅 Server time: ${health.data.timestamp}`);
        } else {
            console.log('   ❌ Health check FAILED');
        }
    } catch (error) {
        console.log(`   ❌ Health check ERROR: ${error.message}`);
    }

    console.log('');

    // 2. Test Get Files List
    console.log('2️⃣ Testing Get Files List...');
    let allFiles = [];
    try {
        const files = await makeRequest(`${API_BASE}/api/files`);
        if (files.statusCode === 200 && files.data.success) {
            allFiles = files.data.data.items;
            const fileCount = allFiles.filter(item => item.type === 'file').length;
            const dirCount = allFiles.filter(item => item.type === 'directory').length;
            
            console.log('   ✅ Get files PASSED');
            console.log(`   📁 Total items: ${allFiles.length}`);
            console.log(`   📄 Files: ${fileCount}`);
            console.log(`   📂 Directories: ${dirCount}`);
        } else {
            console.log('   ❌ Get files FAILED');
        }
    } catch (error) {
        console.log(`   ❌ Get files ERROR: ${error.message}`);
    }

    console.log('');

    // 3. Test Create Test Folder
    console.log('3️⃣ Testing Create Test Folder...');
    try {
        const testFolder = await makeRequest(`${API_BASE}/api/create-test-folder`, 'POST');
        if (testFolder.statusCode === 200 && testFolder.data.success) {
            console.log('   ✅ Create test folder PASSED');
            console.log(`   📁 Test folder created: ${testFolder.data.data.testFolderCreated}`);
            console.log(`   📄 Files returned: ${testFolder.data.data.totalItems}`);
        } else {
            console.log('   ❌ Create test folder FAILED');
        }
    } catch (error) {
        console.log(`   ❌ Create test folder ERROR: ${error.message}`);
    }

    console.log('');

    // 4. Test Browse Directory
    console.log('4️⃣ Testing Browse Directory...');
    try {
        const browse = await makeRequest(`${API_BASE}/api/browse/bin`);
        if (browse.statusCode === 200 && browse.data.success) {
            console.log('   ✅ Browse directory PASSED');
            console.log(`   📂 Path: ${browse.data.data.path}`);
            console.log(`   📄 Items in bin: ${browse.data.data.totalItems}`);
        } else {
            console.log('   ❌ Browse directory FAILED');
        }
    } catch (error) {
        console.log(`   ❌ Browse directory ERROR: ${error.message}`);
    }

    console.log('');

    // 5. Test Download Endpoints
    console.log('5️⃣ Testing Download Endpoints...');
    
    // Lấy một file để test
    const testFile = allFiles.find(item => item.type === 'file' && item.size < 100000); // File nhỏ hơn 100KB
    
    if (testFile) {
        console.log(`   Testing with file: ${testFile.path} (${testFile.size} bytes)`);
        
        // Test API download
        try {
            const response = await new Promise((resolve, reject) => {
                http.get(`${API_BASE}/api/download/${testFile.path}`, (res) => {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        contentLength: res.headers['content-length']
                    });
                    res.destroy(); // Không cần download thực sự
                }).on('error', reject);
            });
            
            if (response.statusCode === 200) {
                console.log('   ✅ API download endpoint PASSED');
                console.log(`   📦 Content-Length: ${response.contentLength} bytes`);
            } else {
                console.log(`   ❌ API download FAILED (${response.statusCode})`);
            }
        } catch (error) {
            console.log(`   ❌ API download ERROR: ${error.message}`);
        }

        // Test Direct file access
        try {
            const response = await new Promise((resolve, reject) => {
                http.get(`${API_BASE}/files/${testFile.path}`, (res) => {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        contentLength: res.headers['content-length']
                    });
                    res.destroy(); // Không cần download thực sự
                }).on('error', reject);
            });
            
            if (response.statusCode === 200) {
                console.log('   ✅ Direct file access PASSED');
                console.log(`   📦 Content-Length: ${response.contentLength} bytes`);
            } else {
                console.log(`   ❌ Direct file access FAILED (${response.statusCode})`);
            }
        } catch (error) {
            console.log(`   ❌ Direct file access ERROR: ${error.message}`);
        }
    } else {
        console.log('   ⚠️  No suitable test file found');
    }

    console.log('');

    // 6. Test ZIP Download
    console.log('6️⃣ Testing ZIP Download...');
    try {
        const response = await new Promise((resolve, reject) => {
            http.get(`${API_BASE}/api/download-all-zip`, (res) => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    contentType: res.headers['content-type']
                });
                res.destroy(); // Không cần download thực sự
            }).on('error', reject);
        });
        
        if (response.statusCode === 200 && response.contentType === 'application/zip') {
            console.log('   ✅ ZIP download PASSED');
            console.log(`   📦 Content-Type: ${response.contentType}`);
        } else {
            console.log(`   ❌ ZIP download FAILED (${response.statusCode})`);
        }
    } catch (error) {
        console.log(`   ❌ ZIP download ERROR: ${error.message}`);
    }

    console.log('');
    console.log('🎉 All endpoint tests completed!');
    console.log('=' * 50);
}

// Chạy test
testAllEndpoints().catch(console.error);
