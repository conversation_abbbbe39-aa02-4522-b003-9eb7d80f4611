# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Petr <PERSON> <<EMAIL>>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-12-30 22:37+0100\n"
"PO-Revision-Date: 2020-11-17 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Czech <http://translations.cfx.re/projects/citizenfx/client/"
"cs/>\n"
"Language: cs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 2;\n"
"X-Generator: Weblate 4.3\n"

#: client/launcher/MiniDump.cpp:1264
#, c-format
msgid ""
"\n"
"\n"
"Exception details: %s"
msgstr ""
"\n"
"\n"
"Detaily výjimky: %s"

#: client/launcher/MiniDump.cpp:1269
#, c-format
msgid ""
"\n"
"\n"
"Legacy crash hash: %s"
msgstr ""
"\n"
"\n"
"Hash pádu hry: %s"

#: client/launcher/MiniDump.cpp:1274
#, c-format
msgid ""
"\n"
"Stack trace:\n"
"%s"
msgstr ""

#: components/net/src/NetLibrary.cpp:637
msgid ""
"# Couldn't connect\n"
"Failed to get info from server (tried 3 times).\n"
"\n"
"---\n"
"\n"
"If you are the server owner, are you sure you are allowing UDP packets to "
"and from the server?"
msgstr ""

#: components/net/src/NetLibrary.cpp:674
#, c-format
msgid ""
"# Timed out\n"
"Client -> server connection timed out. Please try again later.\n"
"\n"
"---\n"
"\n"
"%s\n"
"[Reconnect](cfx.re://reconnect)"
msgstr ""

#: client/launcher/MiniDump.cpp:1260
#, c-format
msgid ""
"%s caused %s to stop working. A crash report is being uploaded to the %s "
"developers."
msgstr "%s způsobilo pád %s. Hlášení o pádu se nahrává pro %s vývojáře."

#: client/launcher/Main.cpp:652
#, c-format
msgid ""
"%s could not create a file in the folder it is placed in. Please move your "
"installation out of Program Files or another protected folder."
msgstr ""
"%s nemohl vytvořit soubor v adresáři, ve kterém se nachází. Prosím "
"přemístěte vaši instalaci jinam z Program Files nebo jiného chráněného "
"adresáře."

#: client/launcher/ViabilityChecks.cpp:100
#, fuzzy, c-format
#| msgid ""
#| "This product requires Security Update for Windows 7 for x64-based systems "
#| "(*********) to be installed to run. Please install it, and try again."
msgid ""
"%s requires the Windows Media Feature Pack for Windows N editions to be "
"installed to run. Please install it, and try again."
msgstr ""
"Tento produkt vyžaduje k běhu aktualizaci Security Update for Windows 7 for "
"x64-based systems (*********). Prosím nainstalujte tuto aktualizaci a zkuste "
"to znovu."

#: client/launcher/MiniDump.cpp:1417
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: %s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: %s\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Podpis pádu: %s\n"
"ID nahlášení: %s\n"
"Můžeš zmáčknout Ctrl-C pro zkopírování zprávy pádu hry a někam ho vložit."

#: client/launcher/MiniDump.cpp:1356
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: ... [uploading]\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: ... [uploading]\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Podpis pádu: %s\n"
"ID nahlášení: ... [nahrávám]\n"
"Můžeš zmáčknout Ctrl-C pro zkopírování zprávy pádu hry a někam ho vložit."

#: client/launcher/MiniDump.cpp:1421
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "%s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid "%sYou can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Podpis pádu: %s\n"
"%s\n"
"Můžeš zmáčknout Ctrl-C pro zkopírování zprávy pádu hry a někam ho vložit."

#: components/net/src/NetLibrary.cpp:99
#, c-format
msgid "**Timeout info**: game=%s, recv=%s, send=%s\n"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:426
msgid ""
"A call into the Windows API took too long recently and led to a game stutter."
msgstr ""

#: client/launcher/MiniDump.cpp:1235
#, c-format
msgid "An error at %s"
msgstr "Chyba v %s"

#: client/launcher/Installer.cpp:127
#, c-format
msgid "Are you sure you want to remove %s from the installation root at %s?"
msgstr "Jsi si jistý, že chceš odstranit %s z kořenu instalace v %s?"

#: client/launcher/Bootstrap.cpp:34
#, c-format
msgid "Bootstrapping %s..."
msgstr "Bootstrapping %s..."

#: client/launcher/Updater.cpp:624
#, c-format
msgid "Checking %s"
msgstr "Kontroluji %s"

#: client/launcher/MiniDump.cpp:1349
#, c-format
msgid "Crash signature: %s\n"
msgstr ""

#: client/launcher/GameCache.cpp:794
#, fuzzy, c-format
#| msgid ""
#| "DLC files are missing (or corrupted) in your game installation. Please "
#| "update or verify the game using Steam or the Social Club launcher and try "
#| "again. See http://rsg.ms/verify step 4 for more info."
msgid ""
"DLC files are missing (or corrupted) in your game installation. Please "
"update or verify the game using Steam, Epic Games Launcher or Rockstar Games "
"Launcher and try again. See http://rsg.ms/verify step 4 for more info.\n"
"Currently, the game installation in '%s' is being used.\n"
"Relevant files: \n"
"%s"
msgstr ""
"DLC soubory nebyly nalezeny (nebo jsou poškozené) ve tvé instalaci. Prosím "
"aktualizuj nebo ověř hru pomocí klienta Steam nebo Social Club launcher a "
"zkus to znovu. Podívej se na http://rsg.ms/verify krok 4 pro více informací."

#: client/launcher/ViabilityChecks.cpp:57
#, c-format
msgid "DXGI 1.2 support is required to run this product %s"
msgstr "DXGI 1.2 musí být podporování pro spuštění tohoto produktu %s"

#: client/launcher/Download.cpp:273
#, c-format
msgid "Downloaded %.2f/%.2f MB (%.0f%%, %.1f MB/s)"
msgstr "Staženo %.2f/%.2f MB (%.0f%%, %.1f MB/s)"

#: client/launcher/MiniDump.cpp:1231
#, c-format
msgid "Error %s"
msgstr "Chyba %s"

#: client/launcher/InstallerExtraction.cpp:534
#, c-format
msgid "Extracting %s"
msgstr "Extrahuji %s"

#: client/launcher/InstallerExtraction.cpp:270
#, c-format
msgid "Extracting %s (scanning)"
msgstr "Extrahuji %s (skenuji)"

#: client/launcher/MiniDump.cpp:1245
msgid "FiveM crashed... but we're on it!"
msgstr "FiveM spadlo... ale děláme na tom!"

#: client/launcher/Main.cpp:633
msgid ""
"FiveM does not support running under elevated privileges. Please change your "
"Windows settings to not run FiveM as administrator.\n"
"The game will exit now."
msgstr ""
"FiveM nepodporuje běh se zvýšenými oprávněními. Změňte nastavení systému "
"Windows tak, aby nespouštěl FiveM jako správce.\n"
"Hra se nyní ukončí."

#: client/launcher/MiniDump.cpp:1308
msgid "Game crashed: "
msgstr "Pád hry: "

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:428
msgid ""
"Please close any software you have running in the background (including "
"Windows apps such as File Explorer or Task Manager)."
msgstr ""

#: client/launcher/ViabilityChecks.cpp:50
msgid "Please install Windows 7 SP1 or greater, and try again."
msgstr "Prosím nainstalujte Windows 7 SP1 nebo novější a zkuste to znovu."

#: client/launcher/ViabilityChecks.cpp:54
msgid "Please install the Platform Update for Windows 7, and try again."
msgstr "Prosím nainstaluje Platforum Update pro Windows 7 a zkuste to znovu."

#: client/launcher/MiniDump.cpp:328 client/launcher/MiniDump.cpp:1447
msgid "Save information"
msgstr ""

#: client/launcher/MiniDump.cpp:1343
msgid ""
"Save information\n"
"Stores a file with crash information that you should copy and upload when "
"asking for help."
msgstr ""
"Uložit informace\n"
"Uloží soubor s informacemi o pádu hry. Tento soubory byste měli zkopírovat a "
"nahrát při žádosti o pomoc."

#: client/launcher/GameSelect.cpp:194
msgid "Select the folder containing Grand Theft Auto V"
msgstr "Zvolte adresář obsahující instalaci Grand Theft Auto V"

#: components/ros-patches-five/src/AccountID.cpp:265
msgid "Signing in with Epic"
msgstr ""

#: components/ros-patches-five/src/AccountID.cpp:229
msgid "Signing in with Steam"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:424
msgid "Slow system performance detected"
msgstr ""

#: client/launcher/Download.cpp:511
msgid "Starting IPFS discovery..."
msgstr "Spouštím průzkum IPFS..."

#: client/launcher/ViabilityChecks.cpp:46
msgid "The game will exit now."
msgstr "Hra se nyní ukončí."

#: client/launcher/GameCache.cpp:783
#, fuzzy, c-format
#| msgid ""
#| "The local %s game cache is outdated, and needs to be updated. This will "
#| "copy %.2f MB of data from the local disk, and download %.2f MB of data "
#| "from the internet.\n"
#| "Do you wish to continue?"
msgid ""
"The local %s game data is outdated, and needs to be updated. This will copy "
"%.2f MB of data from the local disk, and download %.2f MB of data from the "
"internet.\n"
"Do you wish to continue?"
msgstr ""
"Lokální %s mezipaměť hry je zastaralá a potřebuje aktualizovat. Tato akce "
"nakopíruje %.2f MB dat z lokálního disku, a stáhne %.2f MB dat z internetu.\n"
"Přejete si pokračovat?"

#: client/launcher/GameSelect.cpp:329
#, c-format
msgid "The selected path does not contain a %s file."
msgstr "Zvolené umístění neobsahuje soubor %s."

#: client/launcher/ViabilityChecks.cpp:78
msgid ""
"This product requires Security Update for Windows 7 for x64-based systems "
"(*********) to be installed to run. Please install it, and try again."
msgstr ""
"Tento produkt vyžaduje k běhu aktualizaci Security Update for Windows 7 for "
"x64-based systems (*********). Prosím nainstalujte tuto aktualizaci a zkuste "
"to znovu."

#: client/launcher/Main.cpp:809
msgid "Transitioning to another build..."
msgstr "Probíhá přechod na jinou verzi..."

#: client/launcher/MiniDump.cpp:1300
msgid "Unhandled exception: "
msgstr "Neošetřená výjimka: "

#: client/launcher/Installer.cpp:125
#, c-format
msgid "Uninstall %s"
msgstr "Odinstalovat %s"

#: client/launcher/Installer.cpp:126
#, c-format
msgid "Uninstall %s?"
msgstr "Odinstalovat %s?"

#: client/launcher/Updater.cpp:502
#, c-format
msgid "Updating %s..."
msgstr "Aktualizuji %s..."

#: client/launcher/GameCache.cpp:1050
#, fuzzy
#| msgid "Updating game cache..."
msgid "Updating game storage..."
msgstr "Aktualizuji mezipaměť hry..."

#: client/launcher/Updater.cpp:468
msgid "Verifying content..."
msgstr "Ověřuji obsah..."

#: client/launcher/GameCache.cpp:883
msgid "Verifying game content..."
msgstr "Ověřuji herní obsah..."

#: client/launcher/Main.cpp:810
msgid "We're getting there."
msgstr "Už to skoro bude."

#: client/launcher/Main.cpp:613
#, c-format
msgid ""
"You are currently using an outdated version of Windows. This may lead to "
"issues using the %s client. Please update to Windows 10 version 1703 "
"(\"Creators Update\") or higher in case you are experiencing any issues. The "
"game will continue to start now."
msgstr ""
"Aktuálně používáte zastaralou verzi Windows. To může vést k problémům "
"používání klienta %s. Prosím aktualizujte alespoň na Windows 10 verzi 1703 "
"(\"Creators Update\") v případě, že zaznamenáváte nějaké potíže. Hra se nyní "
"zapne."

#~ msgid ""
#~ "\n"
#~ "\n"
#~ "This is a fatal error because game unloading failed. Please report this "
#~ "issue and how to cause it (what server you played on, any resources/"
#~ "scripts, etc.) so this can be solved."
#~ msgstr ""
#~ "\n"
#~ "\n"
#~ "Došlo k fatální chybě protože se nepovedlo správně ukončit hru. Prosím "
#~ "nahlaste tento problém a popište jak ho způsobit (na jakém serveru jste "
#~ "hráli, jaké skripty, atd.), aby mohlo dojít k opravě."

#, c-format
#~ msgid ""
#~ "A game error (at %016llx) caused %s to stop working. A crash report has "
#~ "been uploaded to the %s developers.\n"
#~ "\n"
#~ "%s"
#~ msgstr ""
#~ "Chyba hry (na %016llx) způsobila pád %s. Hlášení o pádu se nahrává pro %s "
#~ "vývojáře.\n"
#~ "\n"
#~ "%s"

#, c-format
#~ msgid "RAGE error: %s"
#~ msgstr "RAGE chyba: %s"
