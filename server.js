const express = require('express');
const fs = require('fs');
const path = require('path');
const archiver = require('archiver');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Đường dẫn đến thư mục files
const FILES_DIR = path.join(__dirname, 'files');

// Hàm đệ quy để lấy tất cả files và folders
function getAllFilesAndFolders(dirPath, basePath = '') {
    const items = [];
    
    try {
        const entries = fs.readdirSync(dirPath, { withFileTypes: true });
        
        for (const entry of entries) {
            const fullPath = path.join(dirPath, entry.name);
            const relativePath = path.join(basePath, entry.name);
            
            if (entry.isDirectory()) {
                // Thêm thông tin thư mục
                items.push({
                    type: 'directory',
                    name: entry.name,
                    path: relativePath.replace(/\\/g, '/'),
                    fullPath: fullPath
                });
                
                // Đệ quy lấy nội dung bên trong thư mục
                const subItems = getAllFilesAndFolders(fullPath, relativePath);
                items.push(...subItems);
            } else {
                // Thêm thông tin file
                const stats = fs.statSync(fullPath);
                items.push({
                    type: 'file',
                    name: entry.name,
                    path: relativePath.replace(/\\/g, '/'),
                    fullPath: fullPath,
                    size: stats.size,
                    modified: stats.mtime
                });
            }
        }
    } catch (error) {
        console.error(`Error reading directory ${dirPath}:`, error);
    }
    
    return items;
}

// API endpoint để lấy danh sách tất cả files và folders
app.get('/api/files', (req, res) => {
    try {
        if (!fs.existsSync(FILES_DIR)) {
            return res.status(404).json({
                success: false,
                message: 'Files directory not found'
            });
        }
        
        const allItems = getAllFilesAndFolders(FILES_DIR);
        
        res.json({
            success: true,
            message: 'Files retrieved successfully',
            data: {
                totalItems: allItems.length,
                items: allItems
            }
        });
    } catch (error) {
        console.error('Error getting files:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

// API endpoint để tải xuống một file cụ thể
app.get('/api/download/:path(*)', (req, res) => {
    try {
        const filePath = req.params.path;
        const fullPath = path.join(FILES_DIR, filePath);
        
        // Kiểm tra file có tồn tại không
        if (!fs.existsSync(fullPath)) {
            return res.status(404).json({
                success: false,
                message: 'File not found'
            });
        }
        
        // Kiểm tra đây có phải là file không (không phải thư mục)
        const stats = fs.statSync(fullPath);
        if (!stats.isFile()) {
            return res.status(400).json({
                success: false,
                message: 'Path is not a file'
            });
        }
        
        // Gửi file
        res.download(fullPath, path.basename(fullPath), (err) => {
            if (err) {
                console.error('Error downloading file:', err);
                if (!res.headersSent) {
                    res.status(500).json({
                        success: false,
                        message: 'Error downloading file'
                    });
                }
            }
        });
    } catch (error) {
        console.error('Error in download endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

// API endpoint để tải xuống thư mục dưới dạng ZIP (giữ lại cho option)
app.get('/api/download-all-zip', (req, res) => {
    try {
        if (!fs.existsSync(FILES_DIR)) {
            return res.status(404).json({
                success: false,
                message: 'Files directory not found'
            });
        }

        // Tạo archive
        const archive = archiver('zip', {
            zlib: { level: 9 } // Compression level
        });

        // Set headers
        res.attachment('files.zip');
        res.setHeader('Content-Type', 'application/zip');

        // Pipe archive data to response
        archive.pipe(res);

        // Add files to archive, giữ nguyên cấu trúc thư mục
        archive.directory(FILES_DIR, false);

        // Finalize archive
        archive.finalize();

        archive.on('error', (err) => {
            console.error('Archive error:', err);
            if (!res.headersSent) {
                res.status(500).json({
                    success: false,
                    message: 'Error creating archive'
                });
            }
        });

    } catch (error) {
        console.error('Error in download-all-zip endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

// API endpoint để serve files trực tiếp từ thư mục files
app.use('/files', express.static(FILES_DIR, {
    dotfiles: 'allow',
    index: false,
    setHeaders: (res, path) => {
        // Set headers để force download thay vì hiển thị trong browser
        const filename = require('path').basename(path);
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    }
}));

// API endpoint để browse và download thư mục/file cụ thể
app.get('/api/browse/*', (req, res) => {
    try {
        const requestedPath = req.params[0] || '';
        const fullPath = path.join(FILES_DIR, requestedPath);

        // Kiểm tra path có tồn tại không
        if (!fs.existsSync(fullPath)) {
            return res.status(404).json({
                success: false,
                message: 'Path not found'
            });
        }

        const stats = fs.statSync(fullPath);

        if (stats.isFile()) {
            // Nếu là file, download trực tiếp
            res.download(fullPath, path.basename(fullPath), (err) => {
                if (err) {
                    console.error('Error downloading file:', err);
                    if (!res.headersSent) {
                        res.status(500).json({
                            success: false,
                            message: 'Error downloading file'
                        });
                    }
                }
            });
        } else if (stats.isDirectory()) {
            // Nếu là thư mục, trả về danh sách files trong thư mục đó
            const items = getAllFilesAndFolders(fullPath, requestedPath);
            res.json({
                success: true,
                message: 'Directory contents retrieved successfully',
                data: {
                    path: requestedPath,
                    totalItems: items.length,
                    items: items
                }
            });
        } else {
            res.status(400).json({
                success: false,
                message: 'Invalid path type'
            });
        }

    } catch (error) {
        console.error('Error in browse endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

// API endpoint để tạo thư mục test (như yêu cầu của bạn)
app.post('/api/create-test-folder', (req, res) => {
    try {
        const testFolderPath = path.join(__dirname, 'test');
        
        if (!fs.existsSync(testFolderPath)) {
            fs.mkdirSync(testFolderPath, { recursive: true });
        }
        
        // Sau khi tạo thư mục test, trả về danh sách files
        const allItems = getAllFilesAndFolders(FILES_DIR);
        
        res.json({
            success: true,
            message: 'Test folder created and files retrieved successfully',
            data: {
                testFolderCreated: true,
                totalItems: allItems.length,
                items: allItems
            }
        });
    } catch (error) {
        console.error('Error creating test folder:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: 'Server is running',
        timestamp: new Date().toISOString()
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
    console.log(`Health check: http://localhost:${PORT}/api/health`);
    console.log(`Get files: http://localhost:${PORT}/api/files`);
    console.log(`Create test folder: http://localhost:${PORT}/api/create-test-folder`);
    console.log(`Browse/Download: http://localhost:${PORT}/api/browse/[path]`);
    console.log(`Direct file access: http://localhost:${PORT}/files/[filepath]`);
    console.log(`Download all as ZIP: http://localhost:${PORT}/api/download-all-zip`);
});

module.exports = app;
