# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2020.
# <AUTHOR> <EMAIL>, 2020.
# <AUTHOR> <EMAIL>, 2020.
# <AUTHOR> <EMAIL>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-12-30 22:37+0100\n"
"PO-Revision-Date: 2020-10-21 22:57+0000\n"
"Last-Translator: Ni<PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: French <http://translations.cfx.re/projects/citizenfx/client/"
"fr/>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.3\n"

#: client/launcher/MiniDump.cpp:1264
#, c-format
msgid ""
"\n"
"\n"
"Exception details: %s"
msgstr ""
"\n"
"\n"
"Détails de l'exception : %s"

#: client/launcher/MiniDump.cpp:1269
#, c-format
msgid ""
"\n"
"\n"
"Legacy crash hash: %s"
msgstr ""
"\n"
"\n"
"Héritage du hash de crash : %s"

#: client/launcher/MiniDump.cpp:1274
#, c-format
msgid ""
"\n"
"Stack trace:\n"
"%s"
msgstr ""

#: components/net/src/NetLibrary.cpp:637
msgid ""
"# Couldn't connect\n"
"Failed to get info from server (tried 3 times).\n"
"\n"
"---\n"
"\n"
"If you are the server owner, are you sure you are allowing UDP packets to "
"and from the server?"
msgstr ""

#: components/net/src/NetLibrary.cpp:674
#, c-format
msgid ""
"# Timed out\n"
"Client -> server connection timed out. Please try again later.\n"
"\n"
"---\n"
"\n"
"%s\n"
"[Reconnect](cfx.re://reconnect)"
msgstr ""

#: client/launcher/MiniDump.cpp:1260
#, c-format
msgid ""
"%s caused %s to stop working. A crash report is being uploaded to the %s "
"developers."
msgstr ""
"%s a provoqué l'arrêt de %s. Un rapport de crash est en cours de "
"téléchargement vers les développeurs %s."

#: client/launcher/Main.cpp:652
#, c-format
msgid ""
"%s could not create a file in the folder it is placed in. Please move your "
"installation out of Program Files or another protected folder."
msgstr ""
"%s n'a pas pu créer un fichier dans le dossier dans lequel il est placé. "
"Veuillez déplacer votre installation en dehors de Program Files ou d'un "
"autre dossier protégé."

#: client/launcher/ViabilityChecks.cpp:100
#, fuzzy, c-format
#| msgid ""
#| "This product requires Security Update for Windows 7 for x64-based systems "
#| "(*********) to be installed to run. Please install it, and try again."
msgid ""
"%s requires the Windows Media Feature Pack for Windows N editions to be "
"installed to run. Please install it, and try again."
msgstr ""
"Ce produit requiert l'installation de la mise à jour de sécurité pour "
"Windows 7 pour les systèmes 64 bits (*********) pour fonctionner. Veuillez "
"l'installer et réessayer."

#: client/launcher/MiniDump.cpp:1417
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: %s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: %s\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Signature de crash : %s\n"
"ID de rapport : %s\n"
"Vous pouvez appuyer sur Ctrl-C pour copier ce message et le coller ailleurs."

#: client/launcher/MiniDump.cpp:1356
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: ... [uploading]\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: ... [uploading]\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Signature de crash : %s\n"
"ID de rapport : ... [téléchargement]\n"
"Vous pouvez appuyer sur Ctrl-C pour copier ce message et le coller ailleurs."

#: client/launcher/MiniDump.cpp:1421
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "%s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid "%sYou can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Signature de crash : %s\n"
"%s\n"
"Vous pouvez appuyer sur Ctrl-C pour copier ce message et le coller ailleurs."

#: components/net/src/NetLibrary.cpp:99
#, c-format
msgid "**Timeout info**: game=%s, recv=%s, send=%s\n"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:426
msgid ""
"A call into the Windows API took too long recently and led to a game stutter."
msgstr ""

#: client/launcher/MiniDump.cpp:1235
#, c-format
msgid "An error at %s"
msgstr "Une erreur à %s"

#: client/launcher/Installer.cpp:127
#, c-format
msgid "Are you sure you want to remove %s from the installation root at %s?"
msgstr "Voulez-vous vraiment supprimer %s de la racine d'installation %s ?"

#: client/launcher/Bootstrap.cpp:34
#, c-format
msgid "Bootstrapping %s..."
msgstr "Démarrage de %s..."

#: client/launcher/Updater.cpp:624
#, c-format
msgid "Checking %s"
msgstr "Vérification %s"

#: client/launcher/MiniDump.cpp:1349
#, c-format
msgid "Crash signature: %s\n"
msgstr ""

#: client/launcher/GameCache.cpp:794
#, fuzzy, c-format
#| msgid ""
#| "DLC files are missing (or corrupted) in your game installation. Please "
#| "update or verify the game using Steam or the Social Club launcher and try "
#| "again. See http://rsg.ms/verify step 4 for more info."
msgid ""
"DLC files are missing (or corrupted) in your game installation. Please "
"update or verify the game using Steam, Epic Games Launcher or Rockstar Games "
"Launcher and try again. See http://rsg.ms/verify step 4 for more info.\n"
"Currently, the game installation in '%s' is being used.\n"
"Relevant files: \n"
"%s"
msgstr ""
"Les fichiers DLC sont manquants (ou corrompus) dans votre installation de "
"jeu. Veuillez mettre à jour ou vérifier le jeu à l'aide de Steam ou de "
"Social Club et réessayer. Voir l'étape 4 http://rsg.ms/verify pour plus "
"d'informations."

#: client/launcher/ViabilityChecks.cpp:57
#, c-format
msgid "DXGI 1.2 support is required to run this product %s"
msgstr "La prise en charge de DXGI 1.2 est requise pour exécuter ce produit %s"

#: client/launcher/Download.cpp:273
#, c-format
msgid "Downloaded %.2f/%.2f MB (%.0f%%, %.1f MB/s)"
msgstr "Téléchargement %.2f/%.2f MB (%.0f%%, %.1f MB/s)"

#: client/launcher/MiniDump.cpp:1231
#, c-format
msgid "Error %s"
msgstr "Erreur %s"

#: client/launcher/InstallerExtraction.cpp:534
#, c-format
msgid "Extracting %s"
msgstr "Extraction %s"

#: client/launcher/InstallerExtraction.cpp:270
#, c-format
msgid "Extracting %s (scanning)"
msgstr "Extraction %s (analyse)"

#: client/launcher/MiniDump.cpp:1245
msgid "FiveM crashed... but we're on it!"
msgstr "FiveM a cessé de fonctionner... mais nous sommes dessus !"

#: client/launcher/Main.cpp:633
msgid ""
"FiveM does not support running under elevated privileges. Please change your "
"Windows settings to not run FiveM as administrator.\n"
"The game will exit now."
msgstr ""
"FiveM ne prend pas en charge l'exécution avec des privilèges élevés. "
"Veuillez modifier vos paramètres Windows pour ne pas exécuter FiveM en tant "
"qu'administrateur.\n"
"Le jeu va se fermer maintenant."

#: client/launcher/MiniDump.cpp:1308
msgid "Game crashed: "
msgstr "Le jeu a cessé de fonctionner : "

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:428
msgid ""
"Please close any software you have running in the background (including "
"Windows apps such as File Explorer or Task Manager)."
msgstr ""

#: client/launcher/ViabilityChecks.cpp:50
msgid "Please install Windows 7 SP1 or greater, and try again."
msgstr "Veuillez installer Windows 7 SP1 ou mieux et réessayez."

#: client/launcher/ViabilityChecks.cpp:54
msgid "Please install the Platform Update for Windows 7, and try again."
msgstr ""
"Veuillez installer la mise à jour de la plateforme pour Windows 7 et "
"réessayez."

#: client/launcher/MiniDump.cpp:328 client/launcher/MiniDump.cpp:1447
msgid "Save information"
msgstr ""

#: client/launcher/MiniDump.cpp:1343
msgid ""
"Save information\n"
"Stores a file with crash information that you should copy and upload when "
"asking for help."
msgstr ""
"Sauvegarder les informations\n"
"Stocker un fichier avec les informations de crash que vous devez copier et "
"télécharger quand vous demandez de l'aide."

#: client/launcher/GameSelect.cpp:194
msgid "Select the folder containing Grand Theft Auto V"
msgstr "Sélectionner le dossier qui contient Grand Theft Auto V"

#: components/ros-patches-five/src/AccountID.cpp:265
msgid "Signing in with Epic"
msgstr ""

#: components/ros-patches-five/src/AccountID.cpp:229
msgid "Signing in with Steam"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:424
msgid "Slow system performance detected"
msgstr ""

#: client/launcher/Download.cpp:511
msgid "Starting IPFS discovery..."
msgstr "Démarrage de la découverte IPFS ..."

#: client/launcher/ViabilityChecks.cpp:46
msgid "The game will exit now."
msgstr "Le jeu va se fermer maintenant."

#: client/launcher/GameCache.cpp:783
#, fuzzy, c-format
#| msgid ""
#| "The local %s game cache is outdated, and needs to be updated. This will "
#| "copy %.2f MB of data from the local disk, and download %.2f MB of data "
#| "from the internet.\n"
#| "Do you wish to continue?"
msgid ""
"The local %s game data is outdated, and needs to be updated. This will copy "
"%.2f MB of data from the local disk, and download %.2f MB of data from the "
"internet.\n"
"Do you wish to continue?"
msgstr ""
"Le cache local du jeu %s est obsolète et doit être mis à jour. La mise à "
"jour va copier %.2f MB de données depuis votre disque local et télécharger "
"%.2f MB de données depuis internet.\n"
"Souhaitez-vous continuer ?"

#: client/launcher/GameSelect.cpp:329
#, c-format
msgid "The selected path does not contain a %s file."
msgstr "Le chemin sélectionné ne contient pas le fichier %s."

#: client/launcher/ViabilityChecks.cpp:78
msgid ""
"This product requires Security Update for Windows 7 for x64-based systems "
"(*********) to be installed to run. Please install it, and try again."
msgstr ""
"Ce produit requiert l'installation de la mise à jour de sécurité pour "
"Windows 7 pour les systèmes 64 bits (*********) pour fonctionner. Veuillez "
"l'installer et réessayer."

#: client/launcher/Main.cpp:809
msgid "Transitioning to another build..."
msgstr "Transition vers une autre version ..."

#: client/launcher/MiniDump.cpp:1300
msgid "Unhandled exception: "
msgstr "Exception non-gérée : "

#: client/launcher/Installer.cpp:125
#, c-format
msgid "Uninstall %s"
msgstr "Désinstaller %s"

#: client/launcher/Installer.cpp:126
#, c-format
msgid "Uninstall %s?"
msgstr "Désinstaller %s ?"

#: client/launcher/Updater.cpp:502
#, c-format
msgid "Updating %s..."
msgstr "Mise à jour %s ..."

#: client/launcher/GameCache.cpp:1050
#, fuzzy
#| msgid "Updating game cache..."
msgid "Updating game storage..."
msgstr "Mise à jour du cache du jeu..."

#: client/launcher/Updater.cpp:468
msgid "Verifying content..."
msgstr "Vérification du contenu..."

#: client/launcher/GameCache.cpp:883
msgid "Verifying game content..."
msgstr "Vérification du contenu du jeu ..."

#: client/launcher/Main.cpp:810
msgid "We're getting there."
msgstr "On y est presque."

#: client/launcher/Main.cpp:613
#, c-format
msgid ""
"You are currently using an outdated version of Windows. This may lead to "
"issues using the %s client. Please update to Windows 10 version 1703 "
"(\"Creators Update\") or higher in case you are experiencing any issues. The "
"game will continue to start now."
msgstr ""
"Vous utilisez actuellement une version obsolète de Windows. Cela peut "
"entraîner des problèmes d'utilisation du client %s. Veuillez effectuer la "
"mise à jour vers Windows 10 version 1703 (\"Creators Update\") ou une "
"version supérieur si vous rencontrez des problèmes. Le jeu continuera à "
"démarrer maintenant."

#~ msgid ""
#~ "\n"
#~ "\n"
#~ "This is a fatal error because game unloading failed. Please report this "
#~ "issue and how to cause it (what server you played on, any resources/"
#~ "scripts, etc.) so this can be solved."
#~ msgstr ""
#~ "\n"
#~ "\n"
#~ "C'est une erreur fatale car le déchargement du jeu a échoué. Veuillez "
#~ "signaler ce problème et comment le provoquer (sur quel serveur vous avez "
#~ "joué, toutes les ressources / scripts, etc.) afin que cela puisse être "
#~ "résolu."

#, c-format
#~ msgid ""
#~ "A game error (at %016llx) caused %s to stop working. A crash report has "
#~ "been uploaded to the %s developers.\n"
#~ "\n"
#~ "%s"
#~ msgstr ""
#~ "Une erreur de jeu (à %016llx) a entraîné l'arrêt de %s. Un rapport "
#~ "d'erreur a été téléchargé vers les développeurs %s.\n"
#~ "\n"
#~ "%s"

#, c-format
#~ msgid "RAGE error: %s"
#~ msgstr "Erreur RAGE : %s"
