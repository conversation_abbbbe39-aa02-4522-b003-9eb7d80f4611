# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-12-30 22:37+0100\n"
"PO-Revision-Date: 2020-07-02 08:59+0000\n"
"Last-Translator: Utku <<EMAIL>>\n"
"Language-Team: Turkish <http://translations.cfx.re/projects/citizenfx/client/"
"tr/>\n"
"Language: tr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.1.1\n"

#: client/launcher/MiniDump.cpp:1264
#, c-format
msgid ""
"\n"
"\n"
"Exception details: %s"
msgstr ""
"\n"
"\n"
"Sıra dışı durum ayrıntıları: %s"

#: client/launcher/MiniDump.cpp:1269
#, c-format
msgid ""
"\n"
"\n"
"Legacy crash hash: %s"
msgstr ""
"\n"
"\n"
"Eski hata hash'i: %s"

#: client/launcher/MiniDump.cpp:1274
#, c-format
msgid ""
"\n"
"Stack trace:\n"
"%s"
msgstr ""

#: components/net/src/NetLibrary.cpp:637
msgid ""
"# Couldn't connect\n"
"Failed to get info from server (tried 3 times).\n"
"\n"
"---\n"
"\n"
"If you are the server owner, are you sure you are allowing UDP packets to "
"and from the server?"
msgstr ""

#: components/net/src/NetLibrary.cpp:674
#, c-format
msgid ""
"# Timed out\n"
"Client -> server connection timed out. Please try again later.\n"
"\n"
"---\n"
"\n"
"%s\n"
"[Reconnect](cfx.re://reconnect)"
msgstr ""

#: client/launcher/MiniDump.cpp:1260
#, c-format
msgid ""
"%s caused %s to stop working. A crash report is being uploaded to the %s "
"developers."
msgstr ""
"%s , %s çalışmasını engelledi. Hata raporu %s geliştiricilerine gönderildi."

#: client/launcher/Main.cpp:652
#, c-format
msgid ""
"%s could not create a file in the folder it is placed in. Please move your "
"installation out of Program Files or another protected folder."
msgstr ""
"%s kurulduğu klasörde dosya oluşturamıyor. Lütfen yüklemenizi, Program Files "
"veya hangi korunan klasör ise dışarı taşıyın."

#: client/launcher/ViabilityChecks.cpp:100
#, fuzzy, c-format
#| msgid ""
#| "This product requires Security Update for Windows 7 for x64-based systems "
#| "(*********) to be installed to run. Please install it, and try again."
msgid ""
"%s requires the Windows Media Feature Pack for Windows N editions to be "
"installed to run. Please install it, and try again."
msgstr ""
"Bu ürünün çalışması için x64 tabanlı Windows 7 sistemleri (*********) için "
"Security Update (Güvenlik Güncellemesi) gerekiyor. Lütfen güncellemeyi kurun "
"ve tekrar deneyin."

#: client/launcher/MiniDump.cpp:1417
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: %s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: %s\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Hata imzası: %s\n"
"Rapor ID: %s\n"
"Ctrl + C ile bu hatayı kopyalayabilirsiniz."

#: client/launcher/MiniDump.cpp:1356
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: ... [uploading]\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: ... [uploading]\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Hata imzası: %s\n"
"Rapor ID: ... [yükleniyor]\n"
"Ctrl + C ile bu hatayı kopyalayabilirsiniz."

#: client/launcher/MiniDump.cpp:1421
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "%s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid "%sYou can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Hata imzası: %s\n"
"%s\n"
"Ctrl + C ile bu hatayı kopyalayabilirsiniz."

#: components/net/src/NetLibrary.cpp:99
#, c-format
msgid "**Timeout info**: game=%s, recv=%s, send=%s\n"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:426
msgid ""
"A call into the Windows API took too long recently and led to a game stutter."
msgstr ""

#: client/launcher/MiniDump.cpp:1235
#, c-format
msgid "An error at %s"
msgstr "%s 'de bir hata var"

#: client/launcher/Installer.cpp:127
#, c-format
msgid "Are you sure you want to remove %s from the installation root at %s?"
msgstr ""
"%s 'i, %s adresindeki kurulum kökünden kaldırmak istediğinize emin misin?"

#: client/launcher/Bootstrap.cpp:34
#, c-format
msgid "Bootstrapping %s..."
msgstr "Önyükleme yapılıyor: %s..."

#: client/launcher/Updater.cpp:624
#, c-format
msgid "Checking %s"
msgstr "Kontrol ediliyor: %s"

#: client/launcher/MiniDump.cpp:1349
#, c-format
msgid "Crash signature: %s\n"
msgstr ""

#: client/launcher/GameCache.cpp:794
#, fuzzy, c-format
#| msgid ""
#| "DLC files are missing (or corrupted) in your game installation. Please "
#| "update or verify the game using Steam or the Social Club launcher and try "
#| "again. See http://rsg.ms/verify step 4 for more info."
msgid ""
"DLC files are missing (or corrupted) in your game installation. Please "
"update or verify the game using Steam, Epic Games Launcher or Rockstar Games "
"Launcher and try again. See http://rsg.ms/verify step 4 for more info.\n"
"Currently, the game installation in '%s' is being used.\n"
"Relevant files: \n"
"%s"
msgstr ""
"Oyun kurulumunuzda DLC dosyaları eksik (veya hatalı). Lütfen oyun "
"dosyalarınız Steam veya Social Club Launcher kullanarak denetleyin. Daha "
"fazla bilgi için: http://rsg.ms/verify 4.adım."

#: client/launcher/ViabilityChecks.cpp:57
#, c-format
msgid "DXGI 1.2 support is required to run this product %s"
msgstr "Bu ürünü çalıştırmak için DXGI 1.2 desteği gerekmektedir %s"

#: client/launcher/Download.cpp:273
#, c-format
msgid "Downloaded %.2f/%.2f MB (%.0f%%, %.1f MB/s)"
msgstr "İndirilen %.2f/%.2f MB (%.0f%%, %.1f MB/s)"

#: client/launcher/MiniDump.cpp:1231
#, c-format
msgid "Error %s"
msgstr "Hata %s"

#: client/launcher/InstallerExtraction.cpp:534
#, c-format
msgid "Extracting %s"
msgstr "Çıkartlıyor %s"

#: client/launcher/InstallerExtraction.cpp:270
#, c-format
msgid "Extracting %s (scanning)"
msgstr "Çıkartılıyor %s (taranıyor)"

#: client/launcher/MiniDump.cpp:1245
msgid "FiveM crashed... but we're on it!"
msgstr "FiveM hata verdi... üzerinde çalışıyoruz!"

#: client/launcher/Main.cpp:633
msgid ""
"FiveM does not support running under elevated privileges. Please change your "
"Windows settings to not run FiveM as administrator.\n"
"The game will exit now."
msgstr ""
"FiveM özel izinler ile çalıştırılmayı desteklememektedir. Lütfen FiveM' i "
"yönetici yetkisi ile çalıştırmayın.\n"
"Oyun şimdi kapatılacak."

#: client/launcher/MiniDump.cpp:1308
msgid "Game crashed: "
msgstr "Oyun çöktü: "

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:428
msgid ""
"Please close any software you have running in the background (including "
"Windows apps such as File Explorer or Task Manager)."
msgstr ""

#: client/launcher/ViabilityChecks.cpp:50
msgid "Please install Windows 7 SP1 or greater, and try again."
msgstr ""
"Lütfen Windows 7 SP1 veya daha üst versiyonunu kurun ve tekrar deneyin."

#: client/launcher/ViabilityChecks.cpp:54
msgid "Please install the Platform Update for Windows 7, and try again."
msgstr "Lütfen Windows 7 için Platform Update' i kurun ve tekrar deneyin."

#: client/launcher/MiniDump.cpp:328 client/launcher/MiniDump.cpp:1447
msgid "Save information"
msgstr ""

#: client/launcher/MiniDump.cpp:1343
msgid ""
"Save information\n"
"Stores a file with crash information that you should copy and upload when "
"asking for help."
msgstr ""
"Bilgi kaydet\n"
"Yardım isterken yüklemeniz için hata bilgileri içeren bir dosya oluşturur."

#: client/launcher/GameSelect.cpp:194
msgid "Select the folder containing Grand Theft Auto V"
msgstr "Grand Theft Auto V içeren klasörü seçin"

#: components/ros-patches-five/src/AccountID.cpp:265
msgid "Signing in with Epic"
msgstr ""

#: components/ros-patches-five/src/AccountID.cpp:229
msgid "Signing in with Steam"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:424
msgid "Slow system performance detected"
msgstr ""

#: client/launcher/Download.cpp:511
msgid "Starting IPFS discovery..."
msgstr "IPFS keşfi başlatılıyor..."

#: client/launcher/ViabilityChecks.cpp:46
msgid "The game will exit now."
msgstr "Oyun şimdi kapatılacak."

#: client/launcher/GameCache.cpp:783
#, fuzzy, c-format
#| msgid ""
#| "The local %s game cache is outdated, and needs to be updated. This will "
#| "copy %.2f MB of data from the local disk, and download %.2f MB of data "
#| "from the internet.\n"
#| "Do you wish to continue?"
msgid ""
"The local %s game data is outdated, and needs to be updated. This will copy "
"%.2f MB of data from the local disk, and download %.2f MB of data from the "
"internet.\n"
"Do you wish to continue?"
msgstr ""
"Yerel %s oyun belleği eskimiş ve güncellenmesi gerek. Bu işlem %.2f MB "
"veriyi yerel diskten kopyalayacak ve %.2f MB veriyi internetten indirecek.\n"
"Devam etmek istiyor musunuz?"

#: client/launcher/GameSelect.cpp:329
#, c-format
msgid "The selected path does not contain a %s file."
msgstr "Seçili yol %s dosyasını içermiyor."

#: client/launcher/ViabilityChecks.cpp:78
msgid ""
"This product requires Security Update for Windows 7 for x64-based systems "
"(*********) to be installed to run. Please install it, and try again."
msgstr ""
"Bu ürünün çalışması için x64 tabanlı Windows 7 sistemleri (*********) için "
"Security Update (Güvenlik Güncellemesi) gerekiyor. Lütfen güncellemeyi kurun "
"ve tekrar deneyin."

#: client/launcher/Main.cpp:809
msgid "Transitioning to another build..."
msgstr "Başka bir yapıya geçiş yapılıyor..."

#: client/launcher/MiniDump.cpp:1300
msgid "Unhandled exception: "
msgstr "İşlenemeyen özel durum: "

#: client/launcher/Installer.cpp:125
#, c-format
msgid "Uninstall %s"
msgstr "Kaldır %s"

#: client/launcher/Installer.cpp:126
#, c-format
msgid "Uninstall %s?"
msgstr "%s kaldır?"

#: client/launcher/Updater.cpp:502
#, c-format
msgid "Updating %s..."
msgstr "%s güncelleniyor..."

#: client/launcher/GameCache.cpp:1050
#, fuzzy
#| msgid "Updating game cache..."
msgid "Updating game storage..."
msgstr "Oyun belleği güncelleniyor..."

#: client/launcher/Updater.cpp:468
msgid "Verifying content..."
msgstr "İçerik denetleniyor..."

#: client/launcher/GameCache.cpp:883
msgid "Verifying game content..."
msgstr "Oyun içeriği denetleniyor..."

#: client/launcher/Main.cpp:810
msgid "We're getting there."
msgstr "Neredeyse bitti."

#: client/launcher/Main.cpp:613
#, c-format
msgid ""
"You are currently using an outdated version of Windows. This may lead to "
"issues using the %s client. Please update to Windows 10 version 1703 "
"(\"Creators Update\") or higher in case you are experiencing any issues. The "
"game will continue to start now."
msgstr ""
"Eski bir Windows sürümü kullanıyorsunuz. Bu durum %s istemcisini kullanmada "
"soruna yol açabilir. Eğer sorun yaşarsanız lütfen Windows 10 sürüm 1703 "
"(\"Creators Update\") veya daha bir üst sürüme yükseltin. Oyun şimdi "
"başlayacak."

#~ msgid ""
#~ "\n"
#~ "\n"
#~ "This is a fatal error because game unloading failed. Please report this "
#~ "issue and how to cause it (what server you played on, any resources/"
#~ "scripts, etc.) so this can be solved."
#~ msgstr ""
#~ "\n"
#~ "\n"
#~ "Oyun boşaltması başarısız oldu. Lütfen bu hatayı ve hatayı nasıl "
#~ "aldığınızı (hangi sunucuda oynadınız, hangi scriptte hata vardı vb.) "
#~ "raporlayın, bu sayede hata çözülebilinir."

#, c-format
#~ msgid ""
#~ "A game error (at %016llx) caused %s to stop working. A crash report has "
#~ "been uploaded to the %s developers.\n"
#~ "\n"
#~ "%s"
#~ msgstr ""
#~ "(%016llx) deki bir oyun hatası %s çalışmasına engel oldu. Hata raporu %s "
#~ "geliştiricilerine gönderildi.\n"
#~ "\n"
#~ "%s"

#, c-format
#~ msgid "RAGE error: %s"
#~ msgstr "RAGE hatası: %s"
