# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON> <<EMAIL>>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-12-30 22:37+0100\n"
"PO-Revision-Date: 2020-07-12 15:16+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Arabic <http://translations.cfx.re/projects/citizenfx/client/"
"ar/>\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 ? 4 : 5;\n"
"X-Generator: Weblate 4.1.1\n"

#: client/launcher/MiniDump.cpp:1264
#, c-format
msgid ""
"\n"
"\n"
"Exception details: %s"
msgstr ""
"\n"
"\n"
"تفاصيل الاستثناء: %s"

#: client/launcher/MiniDump.cpp:1269
#, c-format
msgid ""
"\n"
"\n"
"Legacy crash hash: %s"
msgstr ""
"\n"
"\n"
"رمز ميراث الحادث: %s"

#: client/launcher/MiniDump.cpp:1274
#, c-format
msgid ""
"\n"
"Stack trace:\n"
"%s"
msgstr ""

#: components/net/src/NetLibrary.cpp:637
msgid ""
"# Couldn't connect\n"
"Failed to get info from server (tried 3 times).\n"
"\n"
"---\n"
"\n"
"If you are the server owner, are you sure you are allowing UDP packets to "
"and from the server?"
msgstr ""

#: components/net/src/NetLibrary.cpp:674
#, c-format
msgid ""
"# Timed out\n"
"Client -> server connection timed out. Please try again later.\n"
"\n"
"---\n"
"\n"
"%s\n"
"[Reconnect](cfx.re://reconnect)"
msgstr ""

#: client/launcher/MiniDump.cpp:1260
#, c-format
msgid ""
"%s caused %s to stop working. A crash report is being uploaded to the %s "
"developers."
msgstr ""
"%s سبب %s للوقوف عن العمل. يتم الان عملية رفع البلاغ عن هذا الحادث الى "
"مبرمجين %s."

#: client/launcher/Main.cpp:652
#, c-format
msgid ""
"%s could not create a file in the folder it is placed in. Please move your "
"installation out of Program Files or another protected folder."
msgstr ""
"%s لم يستطع صنع ملف في المجلد الموضوع فيه. الرجاء نقل ملفات العبة من مجلد "
"Program Files او اي مجلد محمي اخر."

#: client/launcher/ViabilityChecks.cpp:100
#, fuzzy, c-format
#| msgid ""
#| "This product requires Security Update for Windows 7 for x64-based systems "
#| "(*********) to be installed to run. Please install it, and try again."
msgid ""
"%s requires the Windows Media Feature Pack for Windows N editions to be "
"installed to run. Please install it, and try again."
msgstr ""
"هذا المنتج يحتاج لتحديث الامان ل (Windows 7 x64-based systems (*********. "
"الرجاء تسطيبه والمحاولة مرة اخرى."

#: client/launcher/MiniDump.cpp:1417
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: %s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: %s\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"توقيع الحادث: %s\n"
"رمز البلاغ: %s\n"
"يمكنك ان تعمل Ctrl-C لنسخ هذه الرسالة ولصقها في مكان اخر."

#: client/launcher/MiniDump.cpp:1356
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: ... [uploading]\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: ... [uploading]\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"توقيع الحادث: %s\n"
"رمز البلاغ: ... جاري الرفع\n"
"يمكنك ان تعمل Ctrl-C لنسخ هذه الرسالة ولقصها في مكان اخر."

#: client/launcher/MiniDump.cpp:1421
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "%s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid "%sYou can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"توقيع الحادث: %s\n"
"%s\n"
"يمكنك ان تعمل Ctrl-C لنسخ هذه الرسالة ولصقها في مكان اخر."

#: components/net/src/NetLibrary.cpp:99
#, c-format
msgid "**Timeout info**: game=%s, recv=%s, send=%s\n"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:426
msgid ""
"A call into the Windows API took too long recently and led to a game stutter."
msgstr ""

#: client/launcher/MiniDump.cpp:1235
#, c-format
msgid "An error at %s"
msgstr "خطاء في %s"

#: client/launcher/Installer.cpp:127
#, c-format
msgid "Are you sure you want to remove %s from the installation root at %s?"
msgstr "هل انت متاكد لحذف %s من ملف التحميل الاساسي في %s?"

#: client/launcher/Bootstrap.cpp:34
#, c-format
msgid "Bootstrapping %s..."
msgstr "جاري التشغيل %s..."

#: client/launcher/Updater.cpp:624
#, c-format
msgid "Checking %s"
msgstr "جاري التحقق %s"

#: client/launcher/MiniDump.cpp:1349
#, c-format
msgid "Crash signature: %s\n"
msgstr ""

#: client/launcher/GameCache.cpp:794
#, fuzzy, c-format
#| msgid ""
#| "DLC files are missing (or corrupted) in your game installation. Please "
#| "update or verify the game using Steam or the Social Club launcher and try "
#| "again. See http://rsg.ms/verify step 4 for more info."
msgid ""
"DLC files are missing (or corrupted) in your game installation. Please "
"update or verify the game using Steam, Epic Games Launcher or Rockstar Games "
"Launcher and try again. See http://rsg.ms/verify step 4 for more info.\n"
"Currently, the game installation in '%s' is being used.\n"
"Relevant files: \n"
"%s"
msgstr ""
"ملفات ال DLC مفقودة (او معطوبة) في ملفات تسطيب لعبتك. الرجاء تحديث او التاكد "
"من ملفات لعبتك من خلال Steam او Social club والمحاولة مرة اخرة. القي نظرة "
"على http://rsg.ms/verify الخطوة الرابعة للمزيد من المعلومات."

#: client/launcher/ViabilityChecks.cpp:57
#, c-format
msgid "DXGI 1.2 support is required to run this product %s"
msgstr "مطلوب دعم DXGl 1.2 لتشغيل هذا المنتج %s"

#: client/launcher/Download.cpp:273
#, c-format
msgid "Downloaded %.2f/%.2f MB (%.0f%%, %.1f MB/s)"
msgstr "تم تحميل %.2f/%.2f ميجا بايت (%.0f%%, %.1f ميجا بايت/ ثانية)"

#: client/launcher/MiniDump.cpp:1231
#, c-format
msgid "Error %s"
msgstr "خطاء %s"

#: client/launcher/InstallerExtraction.cpp:534
#, c-format
msgid "Extracting %s"
msgstr "اخراج %s"

#: client/launcher/InstallerExtraction.cpp:270
#, c-format
msgid "Extracting %s (scanning)"
msgstr "اخراج %s (جاري المسح)"

#: client/launcher/MiniDump.cpp:1245
msgid "FiveM crashed... but we're on it!"
msgstr "حصل حادث في FiveM... لكننا نعمل لحلها!"

#: client/launcher/Main.cpp:633
msgid ""
"FiveM does not support running under elevated privileges. Please change your "
"Windows settings to not run FiveM as administrator.\n"
"The game will exit now."
msgstr ""
"FiveM لا تدعم العمل مع امتيازات مرتفعة. الرجاء تغير اعدادات ال Windows لعدم "
"تشغيل FiveM ك ادمن.\n"
"العبة سوف تغلق الان."

#: client/launcher/MiniDump.cpp:1308
msgid "Game crashed: "
msgstr "تعطلت العبة: "

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:428
msgid ""
"Please close any software you have running in the background (including "
"Windows apps such as File Explorer or Task Manager)."
msgstr ""

#: client/launcher/ViabilityChecks.cpp:50
msgid "Please install Windows 7 SP1 or greater, and try again."
msgstr "الرجاء تسطيب Windows 7 SPI او اكبر، واعد المحاولة."

#: client/launcher/ViabilityChecks.cpp:54
msgid "Please install the Platform Update for Windows 7, and try again."
msgstr "الرجاء تحميل تحديث Windows 7 والمحاولة مجدداً."

#: client/launcher/MiniDump.cpp:328 client/launcher/MiniDump.cpp:1447
msgid "Save information"
msgstr ""

#: client/launcher/MiniDump.cpp:1343
msgid ""
"Save information\n"
"Stores a file with crash information that you should copy and upload when "
"asking for help."
msgstr ""
"حفظ المعلومات\n"
"حفظ ملف يحتوي على معلومات التعطل التي يجب عليك نسخها ورفعها عند طلبك "
"للمساعدة."

#: client/launcher/GameSelect.cpp:194
msgid "Select the folder containing Grand Theft Auto V"
msgstr "اختر الملف الذي يحتوي على لعبة Grand Theft Auto V"

#: components/ros-patches-five/src/AccountID.cpp:265
msgid "Signing in with Epic"
msgstr ""

#: components/ros-patches-five/src/AccountID.cpp:229
msgid "Signing in with Steam"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:424
msgid "Slow system performance detected"
msgstr ""

#: client/launcher/Download.cpp:511
msgid "Starting IPFS discovery..."
msgstr "جاري اكتشاف IPFS..."

#: client/launcher/ViabilityChecks.cpp:46
msgid "The game will exit now."
msgstr "العبة سوف تخرج الان."

#: client/launcher/GameCache.cpp:783
#, fuzzy, c-format
#| msgid ""
#| "The local %s game cache is outdated, and needs to be updated. This will "
#| "copy %.2f MB of data from the local disk, and download %.2f MB of data "
#| "from the internet.\n"
#| "Do you wish to continue?"
msgid ""
"The local %s game data is outdated, and needs to be updated. This will copy "
"%.2f MB of data from the local disk, and download %.2f MB of data from the "
"internet.\n"
"Do you wish to continue?"
msgstr ""
"مخباء الملفات (Cache) الحالي %s قديم، ويحتاج للتحديث. سوف يتم نسخ %.2f ميجا "
"بايت من المعلومات من القرص المحلي، ويتم تحميل %.2f ميجا بايت من المعلومات من "
"الانترنت.\n"
"هل تريد التكملة؟"

#: client/launcher/GameSelect.cpp:329
#, c-format
msgid "The selected path does not contain a %s file."
msgstr "المسار المحدد لايحتوي على ملف %s."

#: client/launcher/ViabilityChecks.cpp:78
msgid ""
"This product requires Security Update for Windows 7 for x64-based systems "
"(*********) to be installed to run. Please install it, and try again."
msgstr ""
"هذا المنتج يحتاج لتحديث الامان ل (Windows 7 x64-based systems (*********. "
"الرجاء تسطيبه والمحاولة مرة اخرى."

#: client/launcher/Main.cpp:809
msgid "Transitioning to another build..."
msgstr "جاري الانتقال الى بناء اخر..."

#: client/launcher/MiniDump.cpp:1300
msgid "Unhandled exception: "
msgstr "استثناء غير معالج: "

#: client/launcher/Installer.cpp:125
#, c-format
msgid "Uninstall %s"
msgstr "الغاء التثبيت %s"

#: client/launcher/Installer.cpp:126
#, c-format
msgid "Uninstall %s?"
msgstr "الغاء التثبيت %s?"

#: client/launcher/Updater.cpp:502
#, c-format
msgid "Updating %s..."
msgstr "تحديث %s..."

#: client/launcher/GameCache.cpp:1050
#, fuzzy
#| msgid "Updating game cache..."
msgid "Updating game storage..."
msgstr "تحديث ملف Cache للعبة..."

#: client/launcher/Updater.cpp:468
msgid "Verifying content..."
msgstr "جاري التاكد من المحتوى..."

#: client/launcher/GameCache.cpp:883
msgid "Verifying game content..."
msgstr "جاري التأكد من المحتوى..."

#: client/launcher/Main.cpp:810
msgid "We're getting there."
msgstr "شارفنا على الوصول."

#: client/launcher/Main.cpp:613
#, c-format
msgid ""
"You are currently using an outdated version of Windows. This may lead to "
"issues using the %s client. Please update to Windows 10 version 1703 "
"(\"Creators Update\") or higher in case you are experiencing any issues. The "
"game will continue to start now."
msgstr ""
"انت الان تقوم باستعمال نسخة Windows قديمة. هذا قد يؤودي الى مشاكل باستعمال "
"%s. الرجاء التحديث الى Windows 10 نسخة 1703 (Creators Update) او اعلى."

#~ msgid ""
#~ "\n"
#~ "\n"
#~ "This is a fatal error because game unloading failed. Please report this "
#~ "issue and how to cause it (what server you played on, any resources/"
#~ "scripts, etc.) so this can be solved."
#~ msgstr ""
#~ "\n"
#~ "\n"
#~ "هذا خطاء قاتل بسبب فشل تفريغ العبة. الرجاء الابلاغ عن هذه المشكلة مع "
#~ "طريقة حدوثها (السيرفر الذي كنت تلعب عليه, السكربتات/الموارد, الخ) لحل هذه "
#~ "المشكلة."

#, c-format
#~ msgid ""
#~ "A game error (at %016llx) caused %s to stop working. A crash report has "
#~ "been uploaded to the %s developers.\n"
#~ "\n"
#~ "%s"
#~ msgstr ""
#~ "خطاء لعبة (في %016llx) سبب %s للتوقف عن العمل. لقد تم رفع تبليغ عن الحادث "
#~ "الى مبرمجين %s.\n"
#~ "\n"
#~ "%s"

#, c-format
#~ msgid "RAGE error: %s"
#~ msgstr "خطاء RAGE: %s"
