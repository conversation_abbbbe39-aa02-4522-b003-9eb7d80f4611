# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON> <<EMAIL>>, 2021.
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-12-30 22:37+0100\n"
"PO-Revision-Date: 2021-01-14 16:30+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Swedish <http://translations.cfx.re/projects/citizenfx/client/"
"sv/>\n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.3\n"

#: client/launcher/MiniDump.cpp:1264
#, c-format
msgid ""
"\n"
"\n"
"Exception details: %s"
msgstr ""
"\n"
"\n"
"Detaljer: %s"

#: client/launcher/MiniDump.cpp:1269
#, c-format
msgid ""
"\n"
"\n"
"Legacy crash hash: %s"
msgstr ""
"\n"
"\n"
"Felkod: %s"

#: client/launcher/MiniDump.cpp:1274
#, c-format
msgid ""
"\n"
"Stack trace:\n"
"%s"
msgstr ""

#: components/net/src/NetLibrary.cpp:637
msgid ""
"# Couldn't connect\n"
"Failed to get info from server (tried 3 times).\n"
"\n"
"---\n"
"\n"
"If you are the server owner, are you sure you are allowing UDP packets to "
"and from the server?"
msgstr ""

#: components/net/src/NetLibrary.cpp:674
#, c-format
msgid ""
"# Timed out\n"
"Client -> server connection timed out. Please try again later.\n"
"\n"
"---\n"
"\n"
"%s\n"
"[Reconnect](cfx.re://reconnect)"
msgstr ""

#: client/launcher/MiniDump.cpp:1260
#, c-format
msgid ""
"%s caused %s to stop working. A crash report is being uploaded to the %s "
"developers."
msgstr ""
"%s orsakade %s att fungera. En felrapport har blivit uppladdad till %s "
"utvecklarna."

#: client/launcher/Main.cpp:652
#, c-format
msgid ""
"%s could not create a file in the folder it is placed in. Please move your "
"installation out of Program Files or another protected folder."
msgstr ""
"%s kunde inte skapa en fil i installationsmappen. Vänligen flytta "
"installationen till en mapp utanför Progam Filer eller annan skyddad mapp."

#: client/launcher/ViabilityChecks.cpp:100
#, fuzzy, c-format
#| msgid ""
#| "This product requires Security Update for Windows 7 for x64-based systems "
#| "(*********) to be installed to run. Please install it, and try again."
msgid ""
"%s requires the Windows Media Feature Pack for Windows N editions to be "
"installed to run. Please install it, and try again."
msgstr ""
"Denna produkt kräver Säkerhetsuppdateringar för Windows 7 x64-system "
"(*********) för att kunna köras. Vänligen uppdatera och försök igen."

#: client/launcher/MiniDump.cpp:1417
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: %s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: %s\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Krasch signatur: %s\n"
"Rapport ID: %s\n"
"Du kan klicka på Ctrl + C för och kopiera detta meddelande och klistra in på "
"annat ställe."

#: client/launcher/MiniDump.cpp:1356
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: ... [uploading]\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: ... [uploading]\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Krasch signatur: %s\n"
"Rapport ID: ... [laddar upp]\n"
"Du kan klicka på Ctrl + C för och kopiera detta meddelande och klistra in på "
"annat ställe."

#: client/launcher/MiniDump.cpp:1421
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "%s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid "%sYou can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Krasch signatur: %s\n"
"%s\n"
"Du kan klicka på Ctrl + C för och kopiera detta meddelande och klistra in på "
"annat ställe."

#: components/net/src/NetLibrary.cpp:99
#, c-format
msgid "**Timeout info**: game=%s, recv=%s, send=%s\n"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:426
msgid ""
"A call into the Windows API took too long recently and led to a game stutter."
msgstr ""

#: client/launcher/MiniDump.cpp:1235
#, c-format
msgid "An error at %s"
msgstr "Ett fel vid %s"

#: client/launcher/Installer.cpp:127
#, c-format
msgid "Are you sure you want to remove %s from the installation root at %s?"
msgstr "Är du säker på att du vill ta bort %s från installationen i %s?"

#: client/launcher/Bootstrap.cpp:34
#, c-format
msgid "Bootstrapping %s..."
msgstr "Startar upp %s..."

#: client/launcher/Updater.cpp:624
#, c-format
msgid "Checking %s"
msgstr "Kontrollerar %s"

#: client/launcher/MiniDump.cpp:1349
#, c-format
msgid "Crash signature: %s\n"
msgstr ""

#: client/launcher/GameCache.cpp:794
#, fuzzy, c-format
#| msgid ""
#| "DLC files are missing (or corrupted) in your game installation. Please "
#| "update or verify the game using Steam or the Social Club launcher and try "
#| "again. See http://rsg.ms/verify step 4 for more info."
msgid ""
"DLC files are missing (or corrupted) in your game installation. Please "
"update or verify the game using Steam, Epic Games Launcher or Rockstar Games "
"Launcher and try again. See http://rsg.ms/verify step 4 for more info.\n"
"Currently, the game installation in '%s' is being used.\n"
"Relevant files: \n"
"%s"
msgstr ""
"DLC filer saknas (eller är korrupta) i din spelinstallation. Vänligen "
"uppdatera eller reparera spelets installation. Se http://rsg.ms/verify steg "
"4 för mer information."

#: client/launcher/ViabilityChecks.cpp:57
#, c-format
msgid "DXGI 1.2 support is required to run this product %s"
msgstr "DXGI 1.2 stöd krävs för att kunna köra denna produkt %s"

#: client/launcher/Download.cpp:273
#, c-format
msgid "Downloaded %.2f/%.2f MB (%.0f%%, %.1f MB/s)"
msgstr "Laddar ned %.2f/%.2f MB (%.0f%%, %.1f MB/s)"

#: client/launcher/MiniDump.cpp:1231
#, c-format
msgid "Error %s"
msgstr "Fel %s"

#: client/launcher/InstallerExtraction.cpp:534
#, c-format
msgid "Extracting %s"
msgstr "Paketerar upp %s"

#: client/launcher/InstallerExtraction.cpp:270
#, c-format
msgid "Extracting %s (scanning)"
msgstr "Paketerar upp %s (Söker igenom)"

#: client/launcher/MiniDump.cpp:1245
msgid "FiveM crashed... but we're on it!"
msgstr "FiveM kraschade... men vi är på det!"

#: client/launcher/Main.cpp:633
msgid ""
"FiveM does not support running under elevated privileges. Please change your "
"Windows settings to not run FiveM as administrator.\n"
"The game will exit now."
msgstr ""
"FiveM stödjer inte körning under administratörsrättigheter. Vänliga ändra "
"dina Windows inställningar till att inte köra FiveM som administratör.\n"
"Spelet avslutas."

#: client/launcher/MiniDump.cpp:1308
msgid "Game crashed: "
msgstr "Spelet kraschade: "

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:428
msgid ""
"Please close any software you have running in the background (including "
"Windows apps such as File Explorer or Task Manager)."
msgstr ""

#: client/launcher/ViabilityChecks.cpp:50
msgid "Please install Windows 7 SP1 or greater, and try again."
msgstr "Vänliga installera Windows 7 SP1 eller senare, och försök igen."

#: client/launcher/ViabilityChecks.cpp:54
msgid "Please install the Platform Update for Windows 7, and try again."
msgstr "Vänligen installera Windows 7 uppdateringar, och försök igen."

#: client/launcher/MiniDump.cpp:328 client/launcher/MiniDump.cpp:1447
msgid "Save information"
msgstr ""

#: client/launcher/MiniDump.cpp:1343
msgid ""
"Save information\n"
"Stores a file with crash information that you should copy and upload when "
"asking for help."
msgstr ""
"Spara information\n"
"Sparar ned en fil med krasch information som du skall bifoga när du ber om "
"hjälp."

#: client/launcher/GameSelect.cpp:194
msgid "Select the folder containing Grand Theft Auto V"
msgstr "Välj installationskatalogen som innehåller Grand Theft Auto V"

#: components/ros-patches-five/src/AccountID.cpp:265
msgid "Signing in with Epic"
msgstr ""

#: components/ros-patches-five/src/AccountID.cpp:229
msgid "Signing in with Steam"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:424
msgid "Slow system performance detected"
msgstr ""

#: client/launcher/Download.cpp:511
msgid "Starting IPFS discovery..."
msgstr "Startar IPFS sökning..."

#: client/launcher/ViabilityChecks.cpp:46
msgid "The game will exit now."
msgstr "Spelet kommer stängas av."

#: client/launcher/GameCache.cpp:783
#, fuzzy, c-format
#| msgid ""
#| "The local %s game cache is outdated, and needs to be updated. This will "
#| "copy %.2f MB of data from the local disk, and download %.2f MB of data "
#| "from the internet.\n"
#| "Do you wish to continue?"
msgid ""
"The local %s game data is outdated, and needs to be updated. This will copy "
"%.2f MB of data from the local disk, and download %.2f MB of data from the "
"internet.\n"
"Do you wish to continue?"
msgstr ""
"Lagrad data för %s är utdaterad och måste uppdateras. Det kommer kopieras "
"%.2f MB av data från lokal disk, och laddas ned %.2f MB av data från "
"internet.\n"
"Vill du fortsätta?"

#: client/launcher/GameSelect.cpp:329
#, c-format
msgid "The selected path does not contain a %s file."
msgstr "Den valda sökvägen innehåller inte en %s fil."

#: client/launcher/ViabilityChecks.cpp:78
msgid ""
"This product requires Security Update for Windows 7 for x64-based systems "
"(*********) to be installed to run. Please install it, and try again."
msgstr ""
"Denna produkt kräver Säkerhetsuppdateringar för Windows 7 x64-system "
"(*********) för att kunna köras. Vänligen uppdatera och försök igen."

#: client/launcher/Main.cpp:809
msgid "Transitioning to another build..."
msgstr "Byter över till en annan version..."

#: client/launcher/MiniDump.cpp:1300
msgid "Unhandled exception: "
msgstr "Okänt fel: "

#: client/launcher/Installer.cpp:125
#, c-format
msgid "Uninstall %s"
msgstr "Avinstallera %s"

#: client/launcher/Installer.cpp:126
#, c-format
msgid "Uninstall %s?"
msgstr "Avinstallera %s?"

#: client/launcher/Updater.cpp:502
#, c-format
msgid "Updating %s..."
msgstr "Uppdaterar %s..."

#: client/launcher/GameCache.cpp:1050
#, fuzzy
#| msgid "Updating game cache..."
msgid "Updating game storage..."
msgstr "Uppdaterar lagrad speldata..."

#: client/launcher/Updater.cpp:468
msgid "Verifying content..."
msgstr "Verifierar innehåll..."

#: client/launcher/GameCache.cpp:883
msgid "Verifying game content..."
msgstr "Verifierar spelinnehåll..."

#: client/launcher/Main.cpp:810
msgid "We're getting there."
msgstr "Snart framme."

#: client/launcher/Main.cpp:613
#, c-format
msgid ""
"You are currently using an outdated version of Windows. This may lead to "
"issues using the %s client. Please update to Windows 10 version 1703 "
"(\"Creators Update\") or higher in case you are experiencing any issues. The "
"game will continue to start now."
msgstr ""
"Du använder en gammal version av Windows. Vilket kan leda till att problem "
"uppstår vid användning av %s klienten. Vänligen uppdatera till Windows 10 "
"version 1703 (\"Creators Update) eller nyare ifall du stöter på problem. "
"Spelet kommer nu att starta."

#~ msgid ""
#~ "\n"
#~ "\n"
#~ "This is a fatal error because game unloading failed. Please report this "
#~ "issue and how to cause it (what server you played on, any resources/"
#~ "scripts, etc.) so this can be solved."
#~ msgstr ""
#~ "\n"
#~ "\n"
#~ "Ett fel uppstod när spelet lästes ut. Vänligen rapportera detta fel och "
#~ "hur man kan återskapa det (Vilken server spelade du på, vilka "
#~ "resurstillägg/scripts användes m.m.) för att lösa problemet."

#, c-format
#~ msgid ""
#~ "A game error (at %016llx) caused %s to stop working. A crash report has "
#~ "been uploaded to the %s developers.\n"
#~ "\n"
#~ "%s"
#~ msgstr ""
#~ "Ett fel uppstod (vid %016llx) som orsakade %s att fungera. En felrapport "
#~ "har blivit uppladdad till %s utvecklarna.\n"
#~ "\n"
#~ "%s"

#, c-format
#~ msgid "RAGE error: %s"
#~ msgstr "RAGE fel: %s"
