# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2020.
# <PERSON> <<EMAIL>>, 2020.
# <AUTHOR> <EMAIL>, 2020.
# <AUTHOR> <EMAIL>, 2021.
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-12-30 22:37+0100\n"
"PO-Revision-Date: 2021-03-03 16:15+0000\n"
"Last-Translator: Doxylamin <<EMAIL>>\n"
"Language-Team: German <http://translations.cfx.re/projects/citizenfx/client/"
"de/>\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.5\n"

#: client/launcher/MiniDump.cpp:1264
#, c-format
msgid ""
"\n"
"\n"
"Exception details: %s"
msgstr ""
"\n"
"\n"
"Fehlerinformationen: %s"

#: client/launcher/MiniDump.cpp:1269
#, c-format
msgid ""
"\n"
"\n"
"Legacy crash hash: %s"
msgstr ""
"\n"
"\n"
"Alter crash hash: %s"

#: client/launcher/MiniDump.cpp:1274
#, c-format
msgid ""
"\n"
"Stack trace:\n"
"%s"
msgstr ""

#: components/net/src/NetLibrary.cpp:637
msgid ""
"# Couldn't connect\n"
"Failed to get info from server (tried 3 times).\n"
"\n"
"---\n"
"\n"
"If you are the server owner, are you sure you are allowing UDP packets to "
"and from the server?"
msgstr ""

#: components/net/src/NetLibrary.cpp:674
#, c-format
msgid ""
"# Timed out\n"
"Client -> server connection timed out. Please try again later.\n"
"\n"
"---\n"
"\n"
"%s\n"
"[Reconnect](cfx.re://reconnect)"
msgstr ""

#: client/launcher/MiniDump.cpp:1260
#, c-format
msgid ""
"%s caused %s to stop working. A crash report is being uploaded to the %s "
"developers."
msgstr ""
"%s hat verursacht, dass %s nicht mehr funktioniert. Ein Absturzbericht wird "
"an die %s Entwickler hochgeladen."

#: client/launcher/Main.cpp:652
#, c-format
msgid ""
"%s could not create a file in the folder it is placed in. Please move your "
"installation out of Program Files or another protected folder."
msgstr ""
"%s konnte in dem Ordner, in dem es sich befindet, keine Datei erstellen. "
"Bitte verschiebe deine Installation aus dem Ordner \"Programme\" oder einem "
"anderen geschützten Ordner."

#: client/launcher/ViabilityChecks.cpp:100
#, fuzzy, c-format
#| msgid ""
#| "This product requires Security Update for Windows 7 for x64-based systems "
#| "(*********) to be installed to run. Please install it, and try again."
msgid ""
"%s requires the Windows Media Feature Pack for Windows N editions to be "
"installed to run. Please install it, and try again."
msgstr ""
"Für die Ausführung dieses Produkts muss das Sicherheitsupdate für Windows 7 "
"für x64-basierte Systeme (*********) installiert sein. Bitte installiere es "
"und versuche es erneut."

#: client/launcher/MiniDump.cpp:1417
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: %s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: %s\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Absturzsignatur: %s\n"
"Meldung ID: %s\n"
"Sie können Strg-C drücken, um diese Nachricht zu kopieren und an einer "
"anderen Stelle einzufügen."

#: client/launcher/MiniDump.cpp:1356
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: ... [uploading]\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: ... [uploading]\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Absturzsignatur: %s\n"
"Meldung ID: ... [lädt hoch]\n"
"Sie können Strg-C drücken, um diese Nachricht zu kopieren und an einer "
"anderen Stelle einzufügen."

#: client/launcher/MiniDump.cpp:1421
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "%s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid "%sYou can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"Absturzsignatur: %s\n"
"%s\n"
"Sie können Strg-C drücken, um diese Nachricht zu kopieren und an einer "
"anderen Stelle einzufügen."

#: components/net/src/NetLibrary.cpp:99
#, c-format
msgid "**Timeout info**: game=%s, recv=%s, send=%s\n"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:426
msgid ""
"A call into the Windows API took too long recently and led to a game stutter."
msgstr ""

#: client/launcher/MiniDump.cpp:1235
#, c-format
msgid "An error at %s"
msgstr "Ein Fehler bei %s"

#: client/launcher/Installer.cpp:127
#, c-format
msgid "Are you sure you want to remove %s from the installation root at %s?"
msgstr ""
"Bist du sicher, dass du %s aus dem Installationsorder bei %s entfernen "
"möchtest?"

#: client/launcher/Bootstrap.cpp:34
#, c-format
msgid "Bootstrapping %s..."
msgstr "Bootstrapping %s..."

#: client/launcher/Updater.cpp:624
#, c-format
msgid "Checking %s"
msgstr "Überprüfe %s"

#: client/launcher/MiniDump.cpp:1349
#, c-format
msgid "Crash signature: %s\n"
msgstr ""

#: client/launcher/GameCache.cpp:794
#, fuzzy, c-format
#| msgid ""
#| "DLC files are missing (or corrupted) in your game installation. Please "
#| "update or verify the game using Steam or the Social Club launcher and try "
#| "again. See http://rsg.ms/verify step 4 for more info."
msgid ""
"DLC files are missing (or corrupted) in your game installation. Please "
"update or verify the game using Steam, Epic Games Launcher or Rockstar Games "
"Launcher and try again. See http://rsg.ms/verify step 4 for more info.\n"
"Currently, the game installation in '%s' is being used.\n"
"Relevant files: \n"
"%s"
msgstr ""
"DLC-Dateien fehlen (oder sind beschädigt) in deiner Spielinstallation. Bitte "
"aktualisiere oder verifiziere das Spiel über Steam oder den Social Club "
"Launcher und versuche es erneut. Siehe http://rsg.ms/verify (Schritt 4) für "
"weitere Informationen."

#: client/launcher/ViabilityChecks.cpp:57
#, c-format
msgid "DXGI 1.2 support is required to run this product %s"
msgstr ""
"DXGI 1.2 Unterstützung ist erforderlich um dieses Produkt auszuführen %s"

#: client/launcher/Download.cpp:273
#, c-format
msgid "Downloaded %.2f/%.2f MB (%.0f%%, %.1f MB/s)"
msgstr "%.2f/%.2f MB (%.0f%%, %.1f MB/s) heruntergeladen"

#: client/launcher/MiniDump.cpp:1231
#, c-format
msgid "Error %s"
msgstr "Fehler %s"

#: client/launcher/InstallerExtraction.cpp:534
#, c-format
msgid "Extracting %s"
msgstr "Entpacke %s"

#: client/launcher/InstallerExtraction.cpp:270
#, c-format
msgid "Extracting %s (scanning)"
msgstr "Entpacke %s (Scannen)"

#: client/launcher/MiniDump.cpp:1245
msgid "FiveM crashed... but we're on it!"
msgstr "FiveM ist abgestürzt... aber wir sind dran!"

#: client/launcher/Main.cpp:633
msgid ""
"FiveM does not support running under elevated privileges. Please change your "
"Windows settings to not run FiveM as administrator.\n"
"The game will exit now."
msgstr ""
"FiveM unterstützt die Ausführung mit erweiterten Rechten nicht. Bitte ändere "
"die Windows-Einstellungen so, dass FiveM nicht als Administrator ausgeführt "
"wird.\n"
"Das Spiel wird jetzt beendet."

#: client/launcher/MiniDump.cpp:1308
msgid "Game crashed: "
msgstr "Spielabsturz: "

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:428
msgid ""
"Please close any software you have running in the background (including "
"Windows apps such as File Explorer or Task Manager)."
msgstr ""

#: client/launcher/ViabilityChecks.cpp:50
msgid "Please install Windows 7 SP1 or greater, and try again."
msgstr ""
"Bitte installiere Windows 7 SP1 oder höher, und versuche es dann erneut."

#: client/launcher/ViabilityChecks.cpp:54
msgid "Please install the Platform Update for Windows 7, and try again."
msgstr ""
"Bitte installiere das Plattform-Update für Windows 7 und versuche es erneut."

#: client/launcher/MiniDump.cpp:328 client/launcher/MiniDump.cpp:1447
msgid "Save information"
msgstr ""

#: client/launcher/MiniDump.cpp:1343
msgid ""
"Save information\n"
"Stores a file with crash information that you should copy and upload when "
"asking for help."
msgstr ""
"Informationen speichern\n"
"Speichert eine Datei mit Absturzinformationen, die du kopieren und hochladen "
"solltest, wenn du um Hilfe bittest."

#: client/launcher/GameSelect.cpp:194
msgid "Select the folder containing Grand Theft Auto V"
msgstr "Wähle den Ordner, der Grand Theft Auto V enthält"

#: components/ros-patches-five/src/AccountID.cpp:265
msgid "Signing in with Epic"
msgstr ""

#: components/ros-patches-five/src/AccountID.cpp:229
msgid "Signing in with Steam"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:424
msgid "Slow system performance detected"
msgstr ""

#: client/launcher/Download.cpp:511
msgid "Starting IPFS discovery..."
msgstr "Starten der IPFS-Erkennung..."

#: client/launcher/ViabilityChecks.cpp:46
msgid "The game will exit now."
msgstr "Das Spiel wird jetzt beendet."

#: client/launcher/GameCache.cpp:783
#, fuzzy, c-format
#| msgid ""
#| "The local %s game cache is outdated, and needs to be updated. This will "
#| "copy %.2f MB of data from the local disk, and download %.2f MB of data "
#| "from the internet.\n"
#| "Do you wish to continue?"
msgid ""
"The local %s game data is outdated, and needs to be updated. This will copy "
"%.2f MB of data from the local disk, and download %.2f MB of data from the "
"internet.\n"
"Do you wish to continue?"
msgstr ""
"Der lokale %s Spielcache ist veraltet und muss aktualisiert werden. Dies "
"wird %.2f MB Daten von der lokalen Festplatte kopieren, und %.2f MB Daten "
"aus dem Internet herunterladen.\n"
"Möchtest du fortfahren?"

#: client/launcher/GameSelect.cpp:329
#, c-format
msgid "The selected path does not contain a %s file."
msgstr "Der ausgewählte Pfad enthält keine %s Datei."

#: client/launcher/ViabilityChecks.cpp:78
msgid ""
"This product requires Security Update for Windows 7 for x64-based systems "
"(*********) to be installed to run. Please install it, and try again."
msgstr ""
"Für die Ausführung dieses Produkts muss das Sicherheitsupdate für Windows 7 "
"für x64-basierte Systeme (*********) installiert sein. Bitte installiere es "
"und versuche es erneut."

#: client/launcher/Main.cpp:809
msgid "Transitioning to another build..."
msgstr "Wechsle zu einer anderen Spielversion..."

#: client/launcher/MiniDump.cpp:1300
msgid "Unhandled exception: "
msgstr "Unbehandelte Ausnahme: "

#: client/launcher/Installer.cpp:125
#, c-format
msgid "Uninstall %s"
msgstr "Deinstalliere %s"

#: client/launcher/Installer.cpp:126
#, c-format
msgid "Uninstall %s?"
msgstr "%s deinstallieren?"

#: client/launcher/Updater.cpp:502
#, c-format
msgid "Updating %s..."
msgstr "Aktualisiere %s..."

#: client/launcher/GameCache.cpp:1050
#, fuzzy
#| msgid "Updating game cache..."
msgid "Updating game storage..."
msgstr "Aktualisiere Spielcache..."

#: client/launcher/Updater.cpp:468
msgid "Verifying content..."
msgstr "Überprüfen des Inhalts..."

#: client/launcher/GameCache.cpp:883
msgid "Verifying game content..."
msgstr "Überprüfen des Spielinhalts..."

#: client/launcher/Main.cpp:810
msgid "We're getting there."
msgstr "Wir sind bald fertig."

#: client/launcher/Main.cpp:613
#, c-format
msgid ""
"You are currently using an outdated version of Windows. This may lead to "
"issues using the %s client. Please update to Windows 10 version 1703 "
"(\"Creators Update\") or higher in case you are experiencing any issues. The "
"game will continue to start now."
msgstr ""
"Du verwendest derzeit eine veraltete Version von Windows. Dies kann zu "
"Problemen bei der Nutzung des %s-Clients führen. Bitte aktualisiere auf "
"Windows 10 Version 1703 (\"Creators Update\") oder höher, falls Probleme "
"auftreten. Das Spiel wird jetzt weiter starten."

#~ msgid ""
#~ "\n"
#~ "\n"
#~ "This is a fatal error because game unloading failed. Please report this "
#~ "issue and how to cause it (what server you played on, any resources/"
#~ "scripts, etc.) so this can be solved."
#~ msgstr ""
#~ "\n"
#~ "\n"
#~ "Dies ist ein fataler Fehler, weil das Entladen des Spiels fehlgeschlagen "
#~ "ist. Bitte melde dieses Problem und wie es zustande kommt (auf welchem "
#~ "Server du gespielt hast, welche Ressourcen/Skripte genutzt werden, etc.), "
#~ "damit dies behoben werden kann."

#, c-format
#~ msgid ""
#~ "A game error (at %016llx) caused %s to stop working. A crash report has "
#~ "been uploaded to the %s developers.\n"
#~ "\n"
#~ "%s"
#~ msgstr ""
#~ "Ein Spielfehler (bei %016llx) hat dazu geführt, dass %s nicht mehr "
#~ "funktioniert. Es wurde ein Absturzbericht an die Entwickler von %s "
#~ "hochgeladen.\n"
#~ "\n"
#~ "%s"

#, c-format
#~ msgid "RAGE error: %s"
#~ msgstr "RAGE Fehler: %s"
