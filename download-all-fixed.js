const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');

const API_BASE = 'http://localhost:3000';
const DOWNLOAD_DIR = './downloaded_files_fixed';

// T<PERSON>o thư mục download nếu chưa có
if (!fs.existsSync(DOWNLOAD_DIR)) {
    fs.mkdirSync(DOWNLOAD_DIR, { recursive: true });
    console.log(`[SUCCESS] Created download directory: ${DOWNLOAD_DIR}`);
}

// Function để download file
function downloadFile(url, outputPath) {
    return new Promise((resolve, reject) => {
        const protocol = url.startsWith('https') ? https : http;
        
        // Tạo thư mục cha nếu chưa có
        const dir = path.dirname(outputPath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        
        const file = fs.createWriteStream(outputPath);
        
        protocol.get(url, (response) => {
            if (response.statusCode === 200) {
                response.pipe(file);
                
                file.on('finish', () => {
                    file.close();
                    const stats = fs.statSync(outputPath);
                    resolve({
                        success: true,
                        size: stats.size,
                        path: outputPath
                    });
                });
            } else {
                file.close();
                if (fs.existsSync(outputPath)) {
                    fs.unlinkSync(outputPath);
                }
                reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
            }
        }).on('error', (err) => {
            file.close();
            if (fs.existsSync(outputPath)) {
                fs.unlinkSync(outputPath);
            }
            reject(err);
        });
    });
}

// Function để lấy danh sách tất cả files
async function getAllFilesForDownload() {
    return new Promise((resolve, reject) => {
        http.get(`${API_BASE}/api/download-all-files`, (response) => {
            let data = '';
            
            response.on('data', (chunk) => {
                data += chunk;
            });
            
            response.on('end', () => {
                try {
                    const result = JSON.parse(data);
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            });
        }).on('error', reject);
    });
}

// Function để tạo thư mục
function createDirectories(directories) {
    console.log('[INFO] Creating directories...');
    let createdCount = 0;
    
    directories.forEach(dir => {
        const dirPath = path.join(DOWNLOAD_DIR, dir.path);
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            createdCount++;
        }
    });
    
    console.log(`[SUCCESS] Created ${createdCount} directories`);
}

// Helper function để format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Main function
async function downloadAllFiles() {
    console.log('[START] Starting download of ALL files and directories...\n');
    
    try {
        // 1. Lấy danh sách tất cả files
        console.log('[INFO] Getting complete files list...');
        const response = await getAllFilesForDownload();
        
        if (!response.success) {
            throw new Error('Failed to get files list');
        }
        
        const { files, directories, totalFiles, totalDirectories } = response.data;
        
        console.log(`[SUCCESS] Found ${totalFiles} files and ${totalDirectories} directories\n`);
        
        // 2. Tạo tất cả thư mục trước
        createDirectories(directories);
        console.log('');
        
        // 3. Download tất cả files
        console.log('[INFO] Starting file downloads...');
        
        let successCount = 0;
        let failCount = 0;
        let totalSize = 0;
        
        // Download files theo batch để không quá tải server
        const batchSize = 5; // Download 5 files cùng lúc
        
        for (let i = 0; i < files.length; i += batchSize) {
            const batch = files.slice(i, i + batchSize);
            
            console.log(`\n[BATCH] ${Math.floor(i/batchSize) + 1}/${Math.ceil(files.length/batchSize)} (${batch.length} files)`);
            
            const promises = batch.map(async (file) => {
                const outputPath = path.join(DOWNLOAD_DIR, file.path);
                
                try {
                    console.log(`  [DOWNLOADING] ${file.path}`);
                    const result = await downloadFile(file.downloadUrl, outputPath);
                    console.log(`  [SUCCESS] ${file.name} (${formatFileSize(result.size)})`);
                    return { success: true, size: result.size, file: file.path };
                } catch (error) {
                    console.log(`  [FAILED] ${file.name} - ${error.message}`);
                    return { success: false, file: file.path, error: error.message };
                }
            });
            
            const results = await Promise.all(promises);
            
            // Tính toán kết quả batch
            results.forEach(result => {
                if (result.success) {
                    successCount++;
                    totalSize += result.size;
                } else {
                    failCount++;
                }
            });
            
            // Hiển thị progress
            const progress = ((i + batch.length) / files.length * 100).toFixed(1);
            console.log(`  [PROGRESS] ${progress}% (${successCount + failCount}/${files.length})`);
        }
        
        // 4. Hiển thị kết quả cuối cùng
        console.log('\n' + '='.repeat(60));
        console.log('[COMPLETED] Download finished!');
        console.log('='.repeat(60));
        console.log(`[DIRECTORIES] Total created: ${directories.length}`);
        console.log(`[SUCCESS] Successful downloads: ${successCount}`);
        console.log(`[FAILED] Failed downloads: ${failCount}`);
        console.log(`[SIZE] Total downloaded: ${formatFileSize(totalSize)}`);
        console.log(`[LOCATION] Files saved to: ${path.resolve(DOWNLOAD_DIR)}`);
        
        if (failCount > 0) {
            console.log(`\n[WARNING] ${failCount} files failed to download. Check the logs above for details.`);
        } else {
            console.log('\n[SUCCESS] All files downloaded successfully!');
        }
        
    } catch (error) {
        console.error('[ERROR] Download failed:', error.message);
    }
}

// Chạy download
downloadAllFiles();
