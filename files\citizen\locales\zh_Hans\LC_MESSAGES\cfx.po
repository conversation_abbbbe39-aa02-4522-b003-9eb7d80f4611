# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2020.
# <AUTHOR> <EMAIL>, 2020.
# <AUTHOR> <EMAIL>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-12-30 22:37+0100\n"
"PO-Revision-Date: 2020-11-12 10:29+0000\n"
"Last-Translator: Akkariin <<EMAIL>>\n"
"Language-Team: Chinese (Simplified) <http://translations.cfx.re/projects/"
"citizenfx/client/zh_Hans/>\n"
"Language: zh_Hans\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 4.3\n"

#: client/launcher/MiniDump.cpp:1264
#, c-format
msgid ""
"\n"
"\n"
"Exception details: %s"
msgstr ""
"\n"
"\n"
"错误详细信息: %s"

#: client/launcher/MiniDump.cpp:1269
#, c-format
msgid ""
"\n"
"\n"
"Legacy crash hash: %s"
msgstr ""
"\n"
"\n"
"错误哈希代码: %s"

#: client/launcher/MiniDump.cpp:1274
#, c-format
msgid ""
"\n"
"Stack trace:\n"
"%s"
msgstr ""

#: components/net/src/NetLibrary.cpp:637
msgid ""
"# Couldn't connect\n"
"Failed to get info from server (tried 3 times).\n"
"\n"
"---\n"
"\n"
"If you are the server owner, are you sure you are allowing UDP packets to "
"and from the server?"
msgstr ""

#: components/net/src/NetLibrary.cpp:674
#, c-format
msgid ""
"# Timed out\n"
"Client -> server connection timed out. Please try again later.\n"
"\n"
"---\n"
"\n"
"%s\n"
"[Reconnect](cfx.re://reconnect)"
msgstr ""

#: client/launcher/MiniDump.cpp:1260
#, c-format
msgid ""
"%s caused %s to stop working. A crash report is being uploaded to the %s "
"developers."
msgstr "%s 导致 %s 停止工作。崩溃报告正在上传到 %s 的开发人员。"

#: client/launcher/Main.cpp:652
#, c-format
msgid ""
"%s could not create a file in the folder it is placed in. Please move your "
"installation out of Program Files or another protected folder."
msgstr ""
"%s 无法在其所在的文件夹中创建文件。请将安装目录移出 “Program Files” 或其他受"
"保护的文件夹。"

#: client/launcher/ViabilityChecks.cpp:100
#, fuzzy, c-format
#| msgid ""
#| "This product requires Security Update for Windows 7 for x64-based systems "
#| "(*********) to be installed to run. Please install it, and try again."
msgid ""
"%s requires the Windows Media Feature Pack for Windows N editions to be "
"installed to run. Please install it, and try again."
msgstr ""
"此产品需要安装 Windows 7（基于 64 位的系统）的安全更新（*********）才能运行。"
"请安装它，然后重试。"

#: client/launcher/MiniDump.cpp:1417
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: %s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: %s\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"崩溃签名: %s\n"
"报告 ID: %s\n"
"你可以按 Ctrl-C 复制此信息并将其粘贴到其他地方。"

#: client/launcher/MiniDump.cpp:1356
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "Report ID: ... [uploading]\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid ""
"%sReport ID: ... [uploading]\n"
"You can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"崩溃签名: %s\n"
"报告 ID: ... [正在上传中]\n"
"你可以按 Ctrl-C 复制此信息并将其粘贴到其他位置。"

#: client/launcher/MiniDump.cpp:1421
#, fuzzy, c-format
#| msgid ""
#| "Crash signature: %s\n"
#| "%s\n"
#| "You can press Ctrl-C to copy this message and paste it elsewhere."
msgid "%sYou can press Ctrl-C to copy this message and paste it elsewhere."
msgstr ""
"崩溃签名: %s\n"
"%s\n"
"你可以按 Ctrl-C 复制此信息并将其粘贴到其他地方。"

#: components/net/src/NetLibrary.cpp:99
#, c-format
msgid "**Timeout info**: game=%s, recv=%s, send=%s\n"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:426
msgid ""
"A call into the Windows API took too long recently and led to a game stutter."
msgstr ""

#: client/launcher/MiniDump.cpp:1235
#, c-format
msgid "An error at %s"
msgstr "%s 发生错误"

#: client/launcher/Installer.cpp:127
#, c-format
msgid "Are you sure you want to remove %s from the installation root at %s?"
msgstr "您确定要从 %s 的安装根目录中删除 %s 吗？"

#: client/launcher/Bootstrap.cpp:34
#, c-format
msgid "Bootstrapping %s..."
msgstr "正在初始化 %s..."

#: client/launcher/Updater.cpp:624
#, c-format
msgid "Checking %s"
msgstr "正在校验 %s"

#: client/launcher/MiniDump.cpp:1349
#, c-format
msgid "Crash signature: %s\n"
msgstr ""

#: client/launcher/GameCache.cpp:794
#, fuzzy, c-format
#| msgid ""
#| "DLC files are missing (or corrupted) in your game installation. Please "
#| "update or verify the game using Steam or the Social Club launcher and try "
#| "again. See http://rsg.ms/verify step 4 for more info."
msgid ""
"DLC files are missing (or corrupted) in your game installation. Please "
"update or verify the game using Steam, Epic Games Launcher or Rockstar Games "
"Launcher and try again. See http://rsg.ms/verify step 4 for more info.\n"
"Currently, the game installation in '%s' is being used.\n"
"Relevant files: \n"
"%s"
msgstr ""
"游戏安装目录的 DLC 文件丢失或损坏。请使用 Steam 或 Social Club 启动器更新或验"
"证游戏，然后重试。有关更多信息，请参见 http://rsg.ms/verify 的第四个步骤。"

#: client/launcher/ViabilityChecks.cpp:57
#, c-format
msgid "DXGI 1.2 support is required to run this product %s"
msgstr "需要 DXGI 1.2 支持才能运行 %s"

#: client/launcher/Download.cpp:273
#, c-format
msgid "Downloaded %.2f/%.2f MB (%.0f%%, %.1f MB/s)"
msgstr "已下载 %.2f/%.2f MB (%.0f%%, %.1f MB/s)"

#: client/launcher/MiniDump.cpp:1231
#, c-format
msgid "Error %s"
msgstr "错误 %s"

#: client/launcher/InstallerExtraction.cpp:534
#, c-format
msgid "Extracting %s"
msgstr "解压中 %s"

#: client/launcher/InstallerExtraction.cpp:270
#, c-format
msgid "Extracting %s (scanning)"
msgstr "正在解压 %s (搜索中)"

#: client/launcher/MiniDump.cpp:1245
msgid "FiveM crashed... but we're on it!"
msgstr "FiveM 崩溃了...但是我们在努力解决！"

#: client/launcher/Main.cpp:633
msgid ""
"FiveM does not support running under elevated privileges. Please change your "
"Windows settings to not run FiveM as administrator.\n"
"The game will exit now."
msgstr ""
"FiveM 不支持以管理员身份运行。请更改您的 Windows 设置为不以管理员身份运行 "
"FiveM。\n"
"现在游戏将会关闭。"

#: client/launcher/MiniDump.cpp:1308
msgid "Game crashed: "
msgstr "游戏发生崩溃： "

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:428
msgid ""
"Please close any software you have running in the background (including "
"Windows apps such as File Explorer or Task Manager)."
msgstr ""

#: client/launcher/ViabilityChecks.cpp:50
msgid "Please install Windows 7 SP1 or greater, and try again."
msgstr "请安装 Windows 7 SP1 或更高版本，然后重试。"

#: client/launcher/ViabilityChecks.cpp:54
msgid "Please install the Platform Update for Windows 7, and try again."
msgstr "请安装 Windows 7 平台更新，然后重试。"

#: client/launcher/MiniDump.cpp:328 client/launcher/MiniDump.cpp:1447
msgid "Save information"
msgstr ""

#: client/launcher/MiniDump.cpp:1343
msgid ""
"Save information\n"
"Stores a file with crash information that you should copy and upload when "
"asking for help."
msgstr ""
"保存信息\n"
"将包含崩溃转储信息的文件保存到本地，在您需要帮助时将它复制并上传。"

#: client/launcher/GameSelect.cpp:194
msgid "Select the folder containing Grand Theft Auto V"
msgstr "选择包含 GTA5 的文件夹"

#: components/ros-patches-five/src/AccountID.cpp:265
msgid "Signing in with Epic"
msgstr ""

#: components/ros-patches-five/src/AccountID.cpp:229
msgid "Signing in with Steam"
msgstr ""

#: components/citizen-devtools/src/ResourceTimeWarnings.cpp:424
msgid "Slow system performance detected"
msgstr ""

#: client/launcher/Download.cpp:511
msgid "Starting IPFS discovery..."
msgstr "正在启动 IPFS 网络发现..."

#: client/launcher/ViabilityChecks.cpp:46
msgid "The game will exit now."
msgstr "游戏将立即退出。"

#: client/launcher/GameCache.cpp:783
#, fuzzy, c-format
#| msgid ""
#| "The local %s game cache is outdated, and needs to be updated. This will "
#| "copy %.2f MB of data from the local disk, and download %.2f MB of data "
#| "from the internet.\n"
#| "Do you wish to continue?"
msgid ""
"The local %s game data is outdated, and needs to be updated. This will copy "
"%.2f MB of data from the local disk, and download %.2f MB of data from the "
"internet.\n"
"Do you wish to continue?"
msgstr ""
"本地的 %s 游戏缓存已过期，需要更新。这将从本地硬盘复制 %.2f MB 的数据，并从互"
"联网下载 %.2f MB 的数据。\n"
"您想继续吗？"

#: client/launcher/GameSelect.cpp:329
#, c-format
msgid "The selected path does not contain a %s file."
msgstr "所选的目录不包含 %s 文件。"

#: client/launcher/ViabilityChecks.cpp:78
msgid ""
"This product requires Security Update for Windows 7 for x64-based systems "
"(*********) to be installed to run. Please install it, and try again."
msgstr ""
"此产品需要安装 Windows 7（基于 64 位的系统）的安全更新（*********）才能运行。"
"请安装它，然后重试。"

#: client/launcher/Main.cpp:809
msgid "Transitioning to another build..."
msgstr "过渡到另一个版本..."

#: client/launcher/MiniDump.cpp:1300
msgid "Unhandled exception: "
msgstr "未处理的异常： "

#: client/launcher/Installer.cpp:125
#, c-format
msgid "Uninstall %s"
msgstr "卸载 %s"

#: client/launcher/Installer.cpp:126
#, c-format
msgid "Uninstall %s?"
msgstr "卸载 %s ？"

#: client/launcher/Updater.cpp:502
#, c-format
msgid "Updating %s..."
msgstr "正在更新 %s..."

#: client/launcher/GameCache.cpp:1050
#, fuzzy
#| msgid "Updating game cache..."
msgid "Updating game storage..."
msgstr "正在更新游戏缓存..."

#: client/launcher/Updater.cpp:468
msgid "Verifying content..."
msgstr "正在验证文件..."

#: client/launcher/GameCache.cpp:883
msgid "Verifying game content..."
msgstr "正在验证游戏文件..."

#: client/launcher/Main.cpp:810
msgid "We're getting there."
msgstr "即将加载完成"

#: client/launcher/Main.cpp:613
#, c-format
msgid ""
"You are currently using an outdated version of Windows. This may lead to "
"issues using the %s client. Please update to Windows 10 version 1703 "
"(\"Creators Update\") or higher in case you are experiencing any issues. The "
"game will continue to start now."
msgstr ""
"您当前正在使用 Windows 的过时版本。 使用 %s 客户端可能会导致问题。如果遇到任"
"何问题，请更新到Windows 10 1703 或更高版本。现在将继续启动游戏。"

#~ msgid ""
#~ "\n"
#~ "\n"
#~ "This is a fatal error because game unloading failed. Please report this "
#~ "issue and how to cause it (what server you played on, any resources/"
#~ "scripts, etc.) so this can be solved."
#~ msgstr ""
#~ "\n"
#~ "\n"
#~ "发生了致命错误，游戏未能正常结束。 请报告此问题及其原因（您在哪个服务器游"
#~ "玩，使用了哪些资源或脚本等），以便解决此问题。"

#, c-format
#~ msgid ""
#~ "A game error (at %016llx) caused %s to stop working. A crash report has "
#~ "been uploaded to the %s developers.\n"
#~ "\n"
#~ "%s"
#~ msgstr ""
#~ "发生游戏错误 (于 %016llx) 导致 %s 停止运作。崩溃报告已上传到 %s 的开发人"
#~ "员。\n"
#~ "\n"
#~ "%s"

#, c-format
#~ msgid "RAGE error: %s"
#~ msgstr "RAGE 错误：%s"
